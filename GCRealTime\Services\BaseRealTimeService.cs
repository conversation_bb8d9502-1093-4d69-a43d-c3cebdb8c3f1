using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using GCRealTime.Core;
using Microsoft.Extensions.Logging;
using GenesysCloudUtils;
using GenesysCloudUtils.WebSocket;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace GCRealTime.Services
{
    /// <summary>
    /// Base class for all real-time services that connect to Genesys Cloud APIs
    /// Implements IRealtimeService to provide common functionality
    /// </summary>
    public abstract class BaseRealTimeService : IRealtimeService
    {
        // Channel response class for deserialization - moved inside the class to avoid scope issues
        private class ChannelResponse
        {
            public string id { get; set; }
            public string connectUri { get; set; }
        }

        protected readonly ILogger _logger;
        protected DBUtils.DBUtils DBAdapter { get; }
        protected bool _pendingDatabaseWrite;
        protected List<Thread> WebSocketThreads = new List<Thread>();
        protected List<IWebSocketManager> WebSocketManagers = new List<IWebSocketManager>();
        protected string _timeZoneConfig = "UTC";
        protected TimeZoneInfo _timeZone = TimeZoneInfo.Utc;
        protected JsonUtils _jsonUtils;
        protected int TotalErrors = 0;
        protected bool _isDisposed = false;
        protected const int MAX_TOPICS_PER_SUBSCRIPTION = 999;
        
        // Add missing fields for error tracking
        protected int _failedChannelAttempts = 0;
        protected const int MAX_CHANNEL_FAILURES = 3;

        // Add fields needed for recovery
        protected int _restartAttempts = 0;
        protected const int MAX_RESTART_ATTEMPTS = 3;
        
        // Add missing field for running state
        protected bool _isRunning = false;

        // Change the fields for tracking events and activity
        private readonly object _metricsLock = new object();
        protected long _eventsProcessed = 0;
        protected DateTime _lastActivity = DateTime.MinValue;

        // Add properties with public getters
        public DateTime LastActivity 
        {
            get { return _lastActivity; }
            protected set { _lastActivity = value; }
        }
        
        public long EventsProcessed
        {
            get { return _eventsProcessed; }
            protected set { _eventsProcessed = value; }
        }

        // Fix the WebSocketManagers property to avoid recursion by using a different name
        public IReadOnlyCollection<IWebSocketManager> ActiveWebSocketManagers
        {
            get { return WebSocketManagers.AsReadOnly(); }
        }

        protected BaseRealTimeService(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _jsonUtils = new JsonUtils(logger);
        }

        protected BaseRealTimeService(ILogger logger, DBUtils.DBUtils dbAdapter) : this(logger)
        {
            DBAdapter = dbAdapter ?? throw new ArgumentNullException(nameof(dbAdapter));
        }

        // Add a thread-safe database write method that creates a semaphore to prevent concurrent writes
        private static readonly SemaphoreSlim _dbWriteSemaphore = new SemaphoreSlim(1, 1);

        /// <summary>
        /// Writes data to the database in a thread-safe manner
        /// </summary>
        /// <param name="dataTable">The DataTable to write</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <returns>True if successful, false otherwise</returns>
        protected async Task<bool> SafeWriteToDatabase(DataTable dataTable, string operationName)
        {
            if (dataTable == null || dataTable.Rows.Count == 0)
                return true;
                
            string threadName = Thread.CurrentThread.Name ?? "UnnamedThread";
            bool success = false;
            
            try
            {
                // Wait to acquire the semaphore
                await _dbWriteSemaphore.WaitAsync();
                
                try
                {
                    _logger?.LogDebug("[Base][Thread:{ThreadName}][DB] Beginning {Operation} with {Rows} rows", 
                        threadName, operationName, dataTable.Rows.Count);
                        
                    // Create a cloned copy to prevent modification during the write operation
                    // FIX: Create a snapshot of the data inside the lock to prevent collection modification
                    DataTable clonedTable;
                    lock (dataTable)
                    {
                        // Clone the schema first
                        clonedTable = dataTable.Clone();
                        
                        // Manually copy each row to avoid collection modification issues
                        foreach (DataRow row in dataTable.Rows)
                        {
                            // Only copy rows that aren't deleted/detached
                            if (row.RowState != DataRowState.Deleted && row.RowState != DataRowState.Detached)
                            {
                                clonedTable.ImportRow(row);
                            }
                        }
                    }
                    
                    // Perform the actual database write with a dedicated connection
                    DBAdapter.WriteSQLDataBulk(clonedTable);
                    
                    _logger?.LogInformation("[Base][Thread:{ThreadName}][DB] Successfully completed {Operation} with {Rows} rows", 
                        threadName, operationName, clonedTable.Rows.Count);
                        
                    success = true;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[Base][Thread:{ThreadName}][DB] Error during {Operation} with {Rows} rows",
                        threadName, operationName, dataTable.Rows.Count);
                }
                finally
                {
                    // Always release the semaphore
                    _dbWriteSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Thread:{ThreadName}][DB] Failed to acquire semaphore for {Operation}",
                    threadName, operationName);
            }
            
            return success;
        }

        protected virtual void ProcessDatabaseWrites()
        {
            // Add thread name to log
            string threadName = Thread.CurrentThread.Name ?? "UnnamedThread";
            _logger?.LogDebug("[Base][Thread:{ThreadName}][DB] Processing database writes", threadName);
            
            // Override in derived classes to implement specific database write logic
            _pendingDatabaseWrite = false;
        }

        // IRealtimeService implementation
        public virtual void Initialize()
        {
            _logger?.LogInformation("[Base][Init] Initializing service");
            
            try
            {
                if (DBAdapter != null)
                    DBAdapter.Initialize();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Init] Error initializing database adapter");
                throw;
            }
        }

        public virtual async Task InitializeAsync()
        {
            await Task.Run(() => Initialize());
        }

        public virtual void Start()
        {
            _logger?.LogInformation("[Base][Start] Starting service");
            _isRunning = true; // Set the running state to true when starting
        }

        public virtual async Task StartAsync(CancellationToken token)
        {
            await Task.Run(() => Start(), token);
        }

        public virtual void Stop()
        {
            _logger?.LogInformation("[Base][Stop] Stopping service");
            
            foreach (var manager in WebSocketManagers)
            {
                try
                {
                    manager.Dispose();
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[Base][Stop] Error disposing websocket manager");
                }
            }
            
            WebSocketManagers.Clear();
            
            foreach (var thread in WebSocketThreads)
            {
                if (thread.IsAlive)
                {
                    try
                    {
                        thread.Join(2000); // Wait up to 2 seconds for thread to terminate
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[Base][Stop] Error waiting for thread to terminate");
                    }
                }
            }
            
            WebSocketThreads.Clear();
        }

        public virtual async Task StopAsync()
        {
            await Task.Run(() => Stop());
        }

        public virtual bool IsHealthy()
        {
            return WebSocketManagers.Count > 0 && WebSocketManagers.TrueForAll(m => m.IsConnected);
        }

        public virtual async Task<bool> CheckHealthAsync()
        {
            try
            {
                // Basic health check implementation
                bool isHealthy = WebSocketManagers.Count > 0 && WebSocketManagers.Any(m => m.IsConnected);
                
                // If not healthy and we're still running, attempt recovery
                if (!isHealthy && _isRunning)
                {
                    _logger?.LogWarning("[Base][Health] Service unhealthy, attempting auto-recovery");
                    await RecoverFailedConnectionsAsync();
                    
                    // Check again after recovery attempt
                    isHealthy = WebSocketManagers.Count > 0 && WebSocketManagers.Any(m => m.IsConnected);
                    
                    if (!isHealthy)
                    {
                        _logger?.LogError("[Base][Health] Recovery failed, service remains unhealthy");
                    }
                    else
                    {
                        _logger?.LogInformation("[Base][Health] Recovery successful, service is now healthy");
                    }
                }
                
                return isHealthy;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Health] Error checking health");
                return false;
            }
        }

        public virtual void Dispose()
        {
            if (!_isDisposed)
            {
                try
                {
                    Stop();
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[Base][Dispose] Error stopping service during disposal");
                }
                
                _isDisposed = true;
            }
        }

        protected virtual void ReceiveData(string jsonString, string threadName)
        {
            // Base implementation to be overridden by derived classes
            _logger?.LogTrace("[Base][ReceiveData] Received data: {JsonString}", jsonString);
        }

        protected virtual void RefreshChannels()
        {
            // Base implementation to be overridden by derived classes
            _logger?.LogTrace("[Base][RefreshChannels] Refreshing channels");
        }

        protected virtual void CreateWebSocket(string socketAddress, string socketChannel, string threadName)
        {
            // Base implementation to be overridden by derived classes
            _logger?.LogTrace("[Base][CreateWebSocket] Creating websocket: {SocketAddress}, {SocketChannel}, {ThreadName}", 
                socketAddress, socketChannel, threadName);
        }
        
        protected virtual WebSocketDetail CreateChannel(string name)
        {
            try
            {
                _logger?.LogInformation("[Base][Channel] Attempting to create channel {Name}", name);
                
                // Check jsonUtils instance
                if (_jsonUtils == null)
                {
                    _logger?.LogError("[Base][Channel] JsonUtils is null, cannot create channel {Name}", name);
                    return null;
                }
                
                // Check API endpoint
                if (string.IsNullOrEmpty(_jsonUtils.ApiEndpoint))
                {
                    _logger?.LogError("[Base][Channel] API endpoint is not configured");
                    return null;
                }
                
                // Add retry logic with exponential backoff
                const int maxRetries = 3;
                Exception lastException = null;
                
                for (int attempt = 1; attempt <= maxRetries; attempt++)
                {
                    try
                    {
                        // Create a notification channel with more detailed error handling
                        string endpoint = $"{_jsonUtils.ApiEndpoint}/api/v2/notifications/channels";
                        _logger?.LogDebug("[Base][Channel] Making API request to {Endpoint} to create channel", endpoint);
                        
                        // Create a specific request body with connectUri specified
                        string requestBody = "{\"connectUri\": true}";
                        
                        // Use JsonRestAsync which returns the raw response as a string
                        string response = _jsonUtils.JsonRestAsync(endpoint, null, "POST", requestBody)
                            .GetAwaiter().GetResult();
                        
                        if (string.IsNullOrEmpty(response))
                        {
                            _logger?.LogWarning("[Base][Channel] Empty response when creating channel {Name} (attempt {Attempt}/{MaxRetries})", 
                                name, attempt, maxRetries);
                            
                            if (attempt < maxRetries)
                            {
                                int delayMs = (int)Math.Pow(2, attempt) * 500; // 1s, 2s, 4s...
                                _logger?.LogDebug("[Base][Channel] Waiting {DelayMs}ms before retry {Attempt}", delayMs, attempt + 1);
                                Thread.Sleep(delayMs);
                            }
                            continue; // Try again
                        }
                        
                        // Log the raw response for debugging
                        _logger?.LogDebug("[Base][Channel] Raw API response: {Response}", response);
                        
                        try
                        {
                            // Try to deserialize the response directly
                            var channelData = JsonConvert.DeserializeObject<ChannelResponse>(response);
                            
                            if (channelData == null)
                            {
                                _logger?.LogWarning("[Base][Channel] Failed to deserialize channel response");
                                continue;
                            }
                            
                            if (string.IsNullOrEmpty(channelData.id) || string.IsNullOrEmpty(channelData.connectUri))
                            {
                                _logger?.LogWarning("[Base][Channel] Invalid channel data: ID={Id}, ConnectUri={ConnectUri}", 
                                    channelData.id ?? "null", 
                                    channelData.connectUri ?? "null");
                                continue;
                            }
                            
                            var detail = new WebSocketDetail
                            {
                                id = channelData.id,
                                connectUri = channelData.connectUri,
                                ReportName = name,
                                Created = DateTime.UtcNow,
                                Expires = DateTime.UtcNow.AddMinutes(60)
                            };
                            
                            _logger?.LogInformation("[Base][Channel] Successfully created channel {Name} with ID {Id}", name, detail.id);
                            return detail;
                        }
                        catch (JsonException jsonEx)
                        {
                            _logger?.LogError(jsonEx, "[Base][Channel] JSON deserialization error for response: {Response}", response);
                            
                            // Try to extract error details if the response is an error object
                            if (response.Contains("error") || response.Contains("errorCode"))
                            {
                                try {
                                    var errorObj = JObject.Parse(response);
                                    string errorMessage = errorObj["message"]?.ToString() ?? "Unknown error";
                                    string errorCode = errorObj["errorCode"]?.ToString() ?? "Unknown code";
                                    _logger?.LogError("[Base][Channel] API returned error: {ErrorCode} - {ErrorMessage}", errorCode, errorMessage);
                                }
                                catch {
                                    // Couldn't parse the error details
                                }
                            }
                            
                            if (attempt < maxRetries)
                            {
                                int delayMs = (int)Math.Pow(2, attempt) * 1000;
                                Thread.Sleep(delayMs);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        lastException = ex;
                        _logger?.LogWarning(ex, "[Base][Channel] Error creating notification channel {Name} (attempt {Attempt}/{MaxRetries})", 
                            name, attempt, maxRetries);
                        
                        // Add exponential backoff between retries
                        if (attempt < maxRetries)
                        {
                            int delayMs = (int)Math.Pow(2, attempt) * 1000; // 1s, 2s, 4s...
                            _logger?.LogDebug("[Base][Channel] Waiting {DelayMs}ms before retry {Attempt}", delayMs, attempt + 1);
                            Thread.Sleep(delayMs);
                        }
                    }
                }
                
                // If we get here, all retries failed
                if (lastException != null)
                {
                    _logger?.LogError(lastException, "[Base][Channel] Failed to create channel {Name} after multiple attempts", name);
                }
                else
                {
                    _logger?.LogError("[Base][Channel] Failed to create channel {Name} after multiple attempts (no exception)", name);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Channel] Unhandled exception creating notification channel {Name}", name);
                return null;
            }
        }

        protected bool TableExists(string tableName)
        {
            if (string.IsNullOrEmpty(tableName))
                throw new ArgumentException("Table name cannot be null or empty", nameof(tableName));
                
            try
            {
                string query = DBAdapter.DBType switch
                {
                    CSG.Adapter.Configuration.DatabaseType.MSSQL => 
                        $"SELECT CASE WHEN OBJECT_ID('{tableName}') IS NOT NULL THEN 1 ELSE 0 END AS TableExists",
                    CSG.Adapter.Configuration.DatabaseType.MySQL => 
                        $"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = '{tableName.ToLower()}'",
                    CSG.Adapter.Configuration.DatabaseType.PostgreSQL => 
                        $"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = '{tableName.ToLower()}') as exists",
                    _ => throw new NotSupportedException($"Unsupported database type: {DBAdapter.DBType}")
                };
                
                object result = DBAdapter.ExecuteScalar(query);
                return Convert.ToBoolean(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][DB] Error checking if table '{TableName}' exists", tableName);
                return false;
            }
        }

        protected DataTable GetPaginatedTableData(string tableName, string orderByColumn, string whereClause = "", int limit = 1000)
        {
            if (string.IsNullOrEmpty(tableName))
                throw new ArgumentException("Table name cannot be null or empty", nameof(tableName));
                
            try
            {
                // Only add WHERE if whereClause is not empty
                string whereStatement = string.IsNullOrEmpty(whereClause) ? "" : $" WHERE {whereClause}";
                
                string query = DBAdapter.DBType switch
                {
                    CSG.Adapter.Configuration.DatabaseType.MSSQL => 
                        $"SELECT TOP {limit} * FROM {tableName}{whereStatement} ORDER BY {orderByColumn} DESC",
                    CSG.Adapter.Configuration.DatabaseType.MySQL => 
                        $"SELECT * FROM {tableName}{whereStatement} ORDER BY {orderByColumn} DESC LIMIT {limit}",
                    CSG.Adapter.Configuration.DatabaseType.PostgreSQL => 
                        $"SELECT * FROM {tableName}{whereStatement} ORDER BY {orderByColumn} DESC LIMIT {limit}",
                    _ => throw new NotSupportedException($"Unsupported database type: {DBAdapter.DBType}")
                };
                
                return DBAdapter.GetSQLTableData(query, $"Existing{tableName}Data");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][DB] Error getting paginated data from table '{TableName}'", tableName);
                return new DataTable();
            }
        }

        protected List<DataTable> ChunkDataTable(DataTable source, int chunkSize)
        {
            List<DataTable> chunks = new List<DataTable>();
            if (source == null || source.Rows.Count == 0)
                return chunks;
                
            DataTable clone = source.Clone();
            int rowCount = 0;
            
            foreach (DataRow row in source.Rows)
            {
                clone.ImportRow(row);
                rowCount++;
                
                if (rowCount >= chunkSize)
                {
                    chunks.Add(clone);
                    clone = source.Clone();
                    rowCount = 0;
                }
            }
            
            if (rowCount > 0)
            {
                chunks.Add(clone);
            }
            
            return chunks;
        }

        protected virtual void CheckFailureThreshold()
        {
            if (_failedChannelAttempts >= MAX_CHANNEL_FAILURES)
            {
                _logger?.LogCritical("Service {ServiceName} has failed {FailCount} times - exceeding the threshold of {MaxFailures}. All services are mandatory. Exiting application.", 
                    GetType().Name, _failedChannelAttempts, MAX_CHANNEL_FAILURES);
                
                // Terminate the entire application immediately since all services are mandatory
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Attempts to recover failed WebSocket connections
        /// </summary>
        protected virtual async Task RecoverFailedConnectionsAsync()
        {
            try
            {
                _restartAttempts++;
                if (_restartAttempts > MAX_RESTART_ATTEMPTS)
                {
                    _logger?.LogError("[Base][Recover] Too many restart attempts ({Count}), giving up", _restartAttempts);
                    return;
                }
                
                _logger?.LogWarning("[Base][Recover] Attempting to recover connections (attempt {Count})", _restartAttempts);
                
                // Clear existing resources
                foreach (var mgr in WebSocketManagers)
                {
                    try
                    {
                        mgr.Dispose();
                    }
                    catch (Exception ex) 
                    {
                        _logger?.LogWarning(ex, "[Base][Recover] Error disposing WebSocket manager");
                    }
                }
                
                WebSocketManagers.Clear();
                WebSocketThreads.Clear();
                
                // Wait a moment before trying again
                await Task.Delay(5000);
                
                // Implementation note: Derived classes should override this method
                // to create new connections specific to their subscription needs.
                _logger?.LogInformation("[Base][Recover] Base recovery completed. Derived classes should override for specific recovery.");
                
                // Wait a moment to let connections establish
                await Task.Delay(5000);
                
                // Log the status after recovery attempt
                _logger?.LogInformation("[Base][Recover] Recovery completed. Active connections: {Count}", 
                    WebSocketManagers.Count(wm => wm.IsConnected));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Recover] Failed to recover connections");
            }
        }

        // Add a method to update metrics that concrete services can call
        protected void IncrementEventsProcessed()
        {
            lock (_metricsLock)
            {
                // Increment the counter thread-safely
                _eventsProcessed++;
                
                // Update the last activity timestamp
                _lastActivity = DateTime.UtcNow;
                
                // Log the event to help with debugging - log absolute count
                _logger?.LogTrace("[Base][Events] Event counter now at {EventCount}", _eventsProcessed);
            }
        }

        // Add implementation for FlushMetrics required by the interface
        public virtual void FlushMetrics()
        {
            // Only log current values, never modify them
            _logger?.LogDebug("[Base][Metrics] Current metrics snapshot: EventsProcessed={EventCount}, LastActivity={LastActivity}",
                EventsProcessed, 
                LastActivity);
        }

        // Ensure the GetServiceMetrics method returns all the required metrics
        public virtual Dictionary<string, object> GetServiceMetrics()
        {
            return new Dictionary<string, object>
            {
                ["ServiceName"] = GetType().Name,
                ["IsRunning"] = _isRunning,
                ["ActiveConnections"] = WebSocketManagers.Count(m => m.IsConnected),
                ["TotalConnections"] = WebSocketManagers.Count,
                ["EventsProcessed"] = EventsProcessed,
                ["LastEventTime"] = LastActivity,
                ["ErrorCount"] = TotalErrors,
                ["FailureAttempts"] = _failedChannelAttempts,
                ["RestartAttempts"] = _restartAttempts
            };
        }

        protected virtual async Task ProcessEventAsync(object eventData)
        {
            // Base implementation does nothing but update metrics
            IncrementEventsProcessed();
        }

        protected virtual async Task PollDataAsync()
        {
            // Base implementation just updates LastActivity
            LastActivity = DateTime.UtcNow;
        }
    }
}