﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using ActiveQMem = GenesysCloudDefQueueRealtime;
using Adh = GenesysCloudDefAdherence;
using DBUtils;
using HeadCountFore = GenesysCloudDefHeadCountForecast;
using ManUnitUsers = GenesysCloudDefManagementUnitUsers;
using Newtonsoft.Json;
using OfferedFore = GenesysCloudDefOfferedForecast;
using ShortTermFore = GenesysCloudDefShortTermForecast;
using StandardUtils;
using TimeOffReq = GenesysCloudDefTimeOffRequests;
using WFMSched = GenesysCloudDefWFMSchedule;
using WFMSchedDets = GenesysCloudDefScheduleDetailsMapping;
using GenesysCloudUtils.WebSocket; // Add this import

namespace GenesysCloudUtils
{
    public class BUData
    {
        public string? CustomerKeyID { get; set; }
        public string? GCApiKey { get; set; }
        public DateTime WFMScheduleLastUpdate { get; set; }
        public DateTime AdherenceLastUpdate { get; set; }
        public DataSet? GCControlData { get; set; }
        public string? TimeZoneConfig { get; set; }
        public string? OAuthUser { get; set; }

        public Boolean Errors { get; set; }

        private Utils UCAUtils = new Utils();
        private Simple3Des? UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        private Boolean CanContinue { get; set; }
        private int TotalResponses { get; set; }
        private DataTable? DownloadTable { get; set; }
        private DataTable? ManagementDownloadMap { get; set; }

        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        ClientWebSocket SocketAdh = new ClientWebSocket();

        public void Initialize()
        {
            GCUtilities.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
            OAuthUser = GCControlData.Tables["GCControlData"].Rows[0]["GC_USERId"].ToString();
            DBUtil.Initialize();
        }


        public DataTable ActiveQMembers(DataTable DTQueueDetails)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            DataTable ActiveQMembersData = DBUtil.CreateInMemTable("activeqmembersdata");





            int MaxRowsToSend = 100;
            int currentPage = 1;


            int totalPages = 0;
            //int totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;

            if (DTQueueDetails.Rows.Count % MaxRowsToSend == 0)
            {
                Console.WriteLine("Reading Bock of Data :Equal Division Pages is not adding one");
                totalPages = (DTQueueDetails.Rows.Count / MaxRowsToSend);
            }
            else
            {
                Console.WriteLine("Reading Bock of Data :Not Equal Division Pages adding one");
                totalPages = (DTQueueDetails.Rows.Count / MaxRowsToSend) + 1;
            }


            DateTime CurrentDate = DateTime.UtcNow;

            while (currentPage <= totalPages)
            {

                Console.WriteLine("Current Queue Page:{0}", currentPage);
                DataTable dtTemp = DTQueueDetails.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                dtTemp.TableName = DTQueueDetails.TableName;

                StringBuilder JSONSelect = new StringBuilder();

                foreach (DataRow DRQueue in dtTemp.Rows)
                {
                    JSONSelect.Append("{\"dimension\": \"queueId\",\"value\": \"" + DRQueue["id"] + "\"},");
                }

                JSONSelect.Length = JSONSelect.Length - 1;

                #region "SearchString"
                String JsonSearchString = "{\"detailMetrics\": [ \"oWaiting\", \"oInteracting\",\"oOnQueueUsers\",\"oUserPresences\",\"oMemberUsers\",\"oOffQueueUsers\",\"oActiveUsers\"], " +
                                          "  \"metrics\": [ \"oWaiting\", \"oInteracting\",\"oOnQueueUsers\",\"oUserPresences\" ,\"oMemberUsers\",\"oOffQueueUsers\",\"oActiveUsers\"], " +
                                          "  \"filter\": { " +
                                          "    \"type\": \"and\", " +
                                          "    \"clauses\": [ " +
                                          "      { " +
                                          "        \"type\": \"or\", " +
                                          "        \"predicates\": [ " +
                                          JSONSelect.ToString() +
                                          "        ] " +
                                          "      } " +
                                          "    ] " +
                                          "  } " +
                                          "} ";
                #endregion

                //Console.WriteLine(JsonSearchString);

                String JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/queues/observations/query", GCApiKey, JsonSearchString);


                if (JsonString.Length > 30)
                {
                    ActiveQMem.QueueRealTime QueueData = new ActiveQMem.QueueRealTime();

                    string OffLine = "";

                    QueueData = JsonConvert.DeserializeObject<ActiveQMem.QueueRealTime>(JsonString,
                                      new JsonSerializerSettings
                                      {
                                          NullValueHandling = NullValueHandling.Ignore
                                      });

                    foreach (string OffLineMapping in QueueData.systemToOrganizationMappings.OFFLINE)
                    {
                        OffLine = OffLineMapping;
                    }

                    foreach (ActiveQMem.Result QueueObsRes in QueueData.results)
                    {


                        if (QueueObsRes.group.mediaType == null)
                        {
                            DataRow QMembers = ActiveQMembersData.NewRow();

                            QMembers["keyid"] = QueueObsRes.group.queueId + "|" + CurrentDate.ToUniversalTime();
                            QMembers["queueid"] = QueueObsRes.group.queueId;
                            QMembers["startdate"] = CurrentDate.ToUniversalTime();
                            QMembers["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(CurrentDate.ToUniversalTime(), AppTimeZone);

                            QMembers["activemembers"] = 0;
                            QMembers["memberusers"] = 0;
                            QMembers["onqueueusers"] = 0;
                            QMembers["offqueueusers"] = 0;


                            foreach (ActiveQMem.Datum Stats in QueueObsRes.data)
                            {

                                switch (Stats.metric.ToLower())
                                {
                                    case "oactiveusers":
                                        QMembers["activemembers"] = Convert.ToInt64(QMembers["activemembers"]) + Stats.stats.count;
                                        break;
                                    case "omemberusers":
                                        QMembers["memberusers"] = Convert.ToInt64(QMembers["memberusers"]) + Stats.stats.count;
                                        break;
                                    case "oonqueueusers":
                                        QMembers["onqueueusers"] = Convert.ToInt64(QMembers["onqueueusers"]) + Stats.stats.count;
                                        break;
                                    case "ooffqueueusers":
                                        QMembers["offqueueusers"] = Convert.ToInt64(QMembers["offqueueusers"]) + Stats.stats.count;
                                        break;
                                }
                            }

                            ActiveQMembersData.Rows.Add(QMembers);
                        }



                    }
                }

                currentPage++;
            }

            return ActiveQMembersData;
        }
        public DataTable HeadCountForecast(DataTable SchedDetails)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            DataTable HeadcountForecastData = DBUtil.CreateInMemTable("headcountforecastdata");



            foreach (DataRow Schedule in SchedDetails.Rows)
            {

                Console.WriteLine(URI + "/api/v2/workforcemanagement/businessunits/"
                                                                             + Schedule["businessunitid"] + "/weeks/" + String.Format("{0:yyyy-MM-dd}", (DateTime)Schedule["weekdate"])
                                                                             + "/schedules/"
                                                                             + Schedule["scheduleid"]
                                                                             + "/headcountforecast"); ;



                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/businessunits/"
                                                                             + Schedule["businessunitid"] + "/weeks/" + String.Format("{0:yyyy-MM-dd}", (DateTime)Schedule["weekdate"])
                                                                             + "/schedules/"
                                                                             + Schedule["scheduleid"]
                                                                             + "/headcountforecast", GCApiKey);

                if (JsonString.Length > 30)
                {
                    if (JsonString.IndexOf("wfm.entityNotFound.schedule") > 0)
                    {

                    }
                    else
                    {
                        // Console.WriteLine(JsonString);

                        HeadCountFore.HeadCountForecast HeadCountData = new HeadCountFore.HeadCountForecast();

                        HeadCountData = JsonConvert.DeserializeObject<HeadCountFore.HeadCountForecast>(JsonString,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });

                        foreach (HeadCountFore.Entity Group in HeadCountData.result.entities)
                        {

                            for (int Counter = 0; Counter < Group.requiredPerInterval.Length - 1; Counter++)
                            {
                                try
                                {
                                    DataRow Forecast = HeadcountForecastData.NewRow();
                                    String PlanningGroup = String.Empty;

                                    if (Group.planningGroup == null)
                                        PlanningGroup = "Unknown";
                                    else
                                        PlanningGroup = Group.planningGroup.id;

                                    Forecast["keyid"] = Schedule["scheduleid"] + "|" + PlanningGroup + "|" + Counter;
                                    Forecast["businessunitid"] = Schedule["businessunitid"];
                                    Forecast["planninggroup"] = PlanningGroup;
                                    Forecast["scheduleid"] = Schedule["scheduleid"];
                                    Forecast["startdate"] = HeadCountData.result.referenceStartDate.AddMinutes(Counter * 15).ToUniversalTime();
                                    Forecast["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(HeadCountData.result.referenceStartDate.AddMinutes(Counter * 15).ToUniversalTime(), AppTimeZone); ;
                                    Forecast["weekdate"] = String.Format("{0:yyyy-MM-dd}", (DateTime)Schedule["weekdate"]);
                                    Forecast["requiredperinterval"] = Group.requiredPerInterval[Counter];
                                    Forecast["requiredwithoutshrinkageperinterval"] = Group.requiredWithoutShrinkagePerInterval[Counter];
                                    Forecast["updated"] = DateTime.UtcNow;

                                    HeadcountForecastData.Rows.Add(Forecast);
                                    Console.Write("A");

                                }
                                catch (System.Data.ConstraintException)
                                {
                                    Console.Write("D");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine(ex.ToString());
                                    // TODO: throw;
                                }

                            }
                        }


                    }
                }

            }

            return HeadcountForecastData;

        }

        public DataTable OfferedForecast(DataTable BusinessUnitDetails, string StartDate)
        {
            try
            {
                TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                DataTable OfferedForecastData = DBUtil.CreateInMemTable("offeredforecastdata");

                // Define Monday as the first day of the week
                DayOfWeek firstDayOfWeek = DayOfWeek.Monday;
                // Get today's date
                DateTime today = DateTime.Today;

                // Calculate the first day of the week 26 weeks ago
                DateTime twentySixWeeksAgo = today.AddDays(-26 * 7);
                DateTime firstDayOfWeek26WeeksAgo = GetFirstDayOfWeek(twentySixWeeksAgo, firstDayOfWeek);

                // Calculate the first day of the week for the day one year ago
                DateTime oneYearAgo = today.AddYears(-1);
                DateTime firstDayOfWeekOneYearAgo = GetFirstDayOfWeek(oneYearAgo, firstDayOfWeek);

                bool historicalSync = false;

                // Parse the input datetime string
                if (DateTime.TryParse(StartDate, out DateTime inputDate))
                {
                    // Compare only the date part
                    if (inputDate.Date < firstDayOfWeek26WeeksAgo.Date)
                    {
                        historicalSync = true;
                        Console.WriteLine($"[INFO] Performing Historical Sync");
                    }
                    else
                    {
                        historicalSync = false;
                        Console.WriteLine($"[INFO] Performing Recent Sync");
                    }
                }
                else
                {
                    Console.WriteLine("Invalid datetime string.");
                }

                foreach (DataRow BusinessUnit in BusinessUnitDetails.Rows)
                {
                    string businessUnitId = BusinessUnit["id"].ToString();

                    IEnumerable<DateTime> dateRange = historicalSync
                        ? GetDateRange(firstDayOfWeekOneYearAgo, firstDayOfWeek26WeeksAgo)
                        : new[] { DateTime.MinValue }; // Single iteration for "recent" case

                    HashSet<string> oldForecastIds = new HashSet<string>();

                    foreach (DateTime date in dateRange)
                    {
                        string RequestString = historicalSync
                            ? $"{URI}/api/v2/workforcemanagement/businessunits/{businessUnitId}/weeks/{date:yyyy-MM-dd}/shorttermforecasts"
                            : $"{URI}/api/v2/workforcemanagement/businessunits/{businessUnitId}/weeks/recent/shorttermforecasts";

                        // Determine the week date or "Recent" based on historicalSync flag
                        string weekDateOrRecent = historicalSync ? date.ToString("yyyy-MM-dd") : "Recent";

                        Console.WriteLine($"[REQUEST] Short-term forecast data for Business Unit ID: {businessUnitId} for week: {weekDateOrRecent}");

                        string jsonStringShortTerm = JsonActions.JsonReturnString(RequestString, GCApiKey);
                        
                        if (jsonStringShortTerm != null)
                        {
                            if (jsonStringShortTerm.Length > 30)
                            {
                                Console.WriteLine($"[INFO] Retrieved short-term forecast data for Business Unit ID: {businessUnitId} for week: {weekDateOrRecent}");

                                ShortTermFore.ShortTermForeCast shortTermForecastData = JsonConvert.DeserializeObject<ShortTermFore.ShortTermForeCast>(jsonStringShortTerm,
                                    new JsonSerializerSettings
                                    {
                                        NullValueHandling = NullValueHandling.Ignore
                                    });

                                if (shortTermForecastData != null)
                                {
                                    foreach (var entity in shortTermForecastData.Entities.OrderBy(e => DateTime.Parse(e.WeekDate)))
                                    {
                                        if (entity.WeekCount > 6)
                                        {
                                            Console.WriteLine($"[WARNING] WeekCount ({entity.WeekCount}) exceeds the supported limit for Forecast ID: {entity.Id} in Business Unit ID: {businessUnitId}. Skipping this entity.");
                                            continue;
                                        }

                                        if(entity.WeekCount > 1)
                                        {
                                            if (oldForecastIds.Contains(entity.Id))
                                            {
                                                Console.WriteLine($"[WARNING] Already processed Forecast ID: {entity.Id} with WeekCount ({entity.WeekCount}) in Business Unit ID: {businessUnitId}. Skipping this entity.");  
                                                continue;
                                            }
                                            else
                                            {
                                                oldForecastIds.Add(entity.Id);
                                            }
                                        }

                                        bool isDstOld = AppTimeZone.IsDaylightSavingTime(DateTime.Parse(entity.WeekDate));
                                        DateTime EndDate = DateTime.Parse(entity.WeekDate).AddDays((int) entity.WeekCount * 7);
                                        DateTime dstPreviousDate = new DateTime();
                                        bool dstChange = false;
                                        int dayCounter = 0;
                                        DateTime dtOld = new DateTime();

                                        for (var dt = DateTime.Parse(entity.WeekDate); dt <= EndDate; dt = dt.AddDays(1))
                                        {
                                            // Check for DST change on each date
                                            bool isDstNew = AppTimeZone.IsDaylightSavingTime(dt);
                                            // Console.WriteLine($"{dt:dd/MM/yyyy HH:mm:ss} - DST: {isDstNew}");
                                            dtOld = dt;

                                            if(isDstNew != isDstOld)
                                            {
                                                Console.WriteLine($"DST condition for Date {dt:dd/MM/yyyy HH:mm:ss} has changed.");
                                                dstPreviousDate = dt.AddDays(-1);
                                                dstChange = true;
                                                // EndDateDTOld = dt;
                                                break;
                                            }

                                            isDstOld =isDstNew;
                                            dayCounter++;
                                            // EndDateDTOld = dt;
                                        }
                                        dayCounter--;

                                        double StartOffset = AppTimeZone.GetUtcOffset(dstPreviousDate).TotalMinutes;
                                        double EndOffset = AppTimeZone.GetUtcOffset(dtOld).TotalMinutes;

                                        double offsetDifference = EndOffset - StartOffset;
                                        int RemoveHrs = (int) (offsetDifference/60);

                                        for (int week = 1; week <= entity.WeekCount; week++)
                                        {
                                            string urlToSend = $"{URI}/api/v2/workforcemanagement/businessunits/{businessUnitId}/weeks/{entity.WeekDate}/shorttermforecasts/{entity.Id}/data?weekNumber={week}";

                                            Console.WriteLine($"[REQUEST] Forecast Request - Business Unit ID: {businessUnitId}, Week Date: {entity.WeekDate}, Week Number: {week}, Forecast ID: {entity.Id}");

                                            string JsonString = JsonActions.JsonReturnString(urlToSend, GCApiKey);

                                            if (JsonString != null)
                                            {
                                                if (JsonString.Length > 30)
                                                {
                                                    OfferedFore.OfferedForeCast OfferedData = new OfferedFore.OfferedForeCast();

                                                    OfferedData = JsonConvert.DeserializeObject<OfferedFore.OfferedForeCast>(JsonString,
                                                            new JsonSerializerSettings
                                                            {
                                                                NullValueHandling = NullValueHandling.Ignore
                                                            });
                                                            
                                                    
                                                    if (OfferedData != null && OfferedData.result != null)
                                                    {
                                                        foreach (OfferedFore.Planninggroup PlanningGrp in OfferedData.result.planningGroups)
                                                        {
                                                            DateTime CalculatedStartDate = OfferedData.result.referenceStartDate.AddDays(7 * (week-1));

                                                            for (int Counter = 0; Counter <= PlanningGrp.offeredPerInterval.Length - 1; Counter++)
                                                            {
                                                                String PlanningGroup = String.Empty;
                                                                if (PlanningGrp.planningGroupId == null)
                                                                    PlanningGroup = "Unknown";
                                                                else
                                                                    PlanningGroup = PlanningGrp.planningGroupId;

                                                                string PrimaryKey = BusinessUnit["id"] + "|" + PlanningGroup + "|" + Counter + "|" + week + "|" + entity.Id;

                                                                DataRow[] DRCheckRow = OfferedForecastData.Select(" keyid = '" + PrimaryKey + "'");

                                                                if (DRCheckRow.Count() == 0)
                                                                {
                                                                    DataRow Forecast = OfferedForecastData.NewRow();
                                                                    Forecast["keyid"] = PrimaryKey;
                                                                    Forecast["businessunitid"] = BusinessUnit["id"];
                                                                    Forecast["planninggroup"] = PlanningGroup;
                                                                    // TODO Decommission Schedule ID
                                                                    Forecast["scheduleid"] = "";
                                                                    Forecast["shorttermforecastid"] = entity.Id;

                                                                    DateTime IncrementalCalculatedStartDate = CalculatedStartDate.AddMinutes(Counter * 15);

                                                                    if (DateTime.Parse(entity.WeekDate).AddDays(week * 7) >= dstPreviousDate && dstChange == true)
                                                                    {
                                                                        IncrementalCalculatedStartDate = IncrementalCalculatedStartDate.AddHours(-RemoveHrs);
                                                                    }

                                                                    Forecast["startdate"] = IncrementalCalculatedStartDate;

                                                                    DateTimeOffset StartDateForecastOffset = DateTime.SpecifyKind(IncrementalCalculatedStartDate, DateTimeKind.Utc);
                                                                    DateTime ConvertedLocalStartDate = StartDateForecastOffset.ToOffset(AppTimeZone.GetUtcOffset(IncrementalCalculatedStartDate)).DateTime;

                                                                    // DST Change will result in an additional hour of data being returned which will bleed into the next week
                                                                    // Logic will check and skip this additional hour
                                                                    if(DateTime.Parse(entity.WeekDate).AddDays((week) * 7).Date==ConvertedLocalStartDate.Date && dstChange==true)
                                                                    {
                                                                        break;
                                                                    }

                                                                    Forecast["startdateltc"] = ConvertedLocalStartDate;

                                                                    Forecast["weekdate"] = (entity.WeekDate);
                                                                    Forecast["week"] = week;
                                                                    Forecast["avghandleperinterval"] = PlanningGrp.averageHandleTimeSecondsPerInterval[Counter];
                                                                    Forecast["offeredperinterval"] = PlanningGrp.offeredPerInterval[Counter];
                                                                    Forecast["canUseForScheduling"] = entity.canUseForScheduling;
                                                                    Forecast["updated"] = DateTime.UtcNow;

                                                                    OfferedForecastData.Rows.Add(Forecast);
                                                                }
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        Console.WriteLine($"[WARNING] No Forecast Data for Forecast ID: {entity.Id} with WeekCount ({entity.WeekCount}) in Business Unit ID: {businessUnitId}. Skipping this entity.");
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    Console.WriteLine($"[WARNING] No short-term forecast data available for Business Unit ID: {businessUnitId} for week: {weekDateOrRecent}");
                                }
                            }
                            else
                            {
                                Console.WriteLine($"[WARNING] No short-term forecast data available for Business Unit ID: {businessUnitId} for week: {weekDateOrRecent}");
                            }
                        }
                    }
                }

                return OfferedForecastData;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"OfferedForecast Error: {ex.Message}");
                throw;
            }
        }        
        
        public DataTable GetScheduleDetailsFromCC(string StartDate)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            DataTable WFMScheduleDetails = DBUtil.CreateInMemTable("scheduledetails");

            DataTable BUUnits = DBUtil.GetSQLTableData("select * from buDetails", "buDetails");

            DateTime CalcDateDiffUTC = DateTime.Now.ToUniversalTime().AddDays(-180);

            // Define Monday as the first day of the week
            DayOfWeek firstDayOfWeek = DayOfWeek.Monday;
            // Get today's date
            DateTime today = DateTime.Today;

            // Calculate the first day of the week 26 weeks ago
            DateTime twentySixWeeksAgo = today.AddDays(-26 * 7);
            DateTime firstDayOfWeek26WeeksAgo = GetFirstDayOfWeek(twentySixWeeksAgo, firstDayOfWeek);

            // Calculate the first day of the week for the day one year ago
            DateTime oneYearAgo = today.AddYears(-1);
            DateTime firstDayOfWeekOneYearAgo = GetFirstDayOfWeek(oneYearAgo, firstDayOfWeek);

            bool historicalSync = false;

            // Parse the input datetime string
            if (DateTime.TryParse(StartDate, out DateTime inputDate))
            {
                // Compare only the date part
                if (inputDate.Date < firstDayOfWeek26WeeksAgo.Date)
                {
                    historicalSync = true;
                    Console.WriteLine($"[INFO] Performing Historical Sync");
                }
                else
                {
                    historicalSync = false;
                    Console.WriteLine($"[INFO] Performing Recent Sync");
                }
            }
            else
            {
                Console.WriteLine("Invalid datetime string.");
            }

            foreach (DataRow DRBUUnit in BUUnits.Rows)
            {
                IEnumerable<DateTime> dateRange = historicalSync
                    ? GetDateRange(firstDayOfWeekOneYearAgo, firstDayOfWeek26WeeksAgo)
                    : new[] { DateTime.MinValue }; // Single iteration for "recent" case

                foreach (DateTime date in dateRange)
                {
                    string RequestString = historicalSync
                        ? $"{URI}/api/v2/workforcemanagement/businessunits/{DRBUUnit["id"]}/weeks/{date:yyyy-MM-dd}/schedules"
                        : $"{URI}/api/v2/workforcemanagement/businessunits/{DRBUUnit["id"]}/weeks/recent/schedules";

                    // Determine the week date or "Recent" based on historicalSync flag
                    string weekDateOrRecent = historicalSync ? date.ToString("yyyy-MM-dd") : "Recent";

                    Console.WriteLine($"[REQUEST]  Schedule Request -Business Unit ID: {DRBUUnit["id"]} for week: {weekDateOrRecent}");

                    string JsonString = JsonActions.JsonReturnString(RequestString, GCApiKey);
                    
                    if (JsonString != null)
                    {
                        if (JsonString.Length > 30)
                        {
                            WFMSchedDets.Schedules ScheduleDetails = new WFMSchedDets.Schedules();

                            ScheduleDetails = JsonConvert.DeserializeObject<WFMSchedDets.Schedules>(JsonString,
                                        new JsonSerializerSettings
                                        {
                                            NullValueHandling = NullValueHandling.Ignore
                                        });

                            foreach (WFMSchedDets.Entity SchedDets in ScheduleDetails.entities)
                            {
                                string PrimaryKey = DRBUUnit["id"] + "|" + SchedDets.id + "|" + SchedDets.weekDate + "|" + SchedDets.weekCount;

                                DataRow[] DRCheckRow = WFMScheduleDetails.Select(" keyid = '" + PrimaryKey + "'");

                                if (DRCheckRow.Count() == 0)
                                {
                                    DataRow DRSchedDets = WFMScheduleDetails.NewRow();
                                    DRSchedDets["keyid"] = PrimaryKey;

                                    DRSchedDets["businessunitid"] = DRBUUnit["id"];
                                    DRSchedDets["scheduleid"] = SchedDets.id;
                                    DRSchedDets["weekdate"] = SchedDets.weekDate;
                                    DRSchedDets["weekcount"] = SchedDets.weekCount;
                                    DRSchedDets["published"] = SchedDets.published;
                                    DRSchedDets["description"] = SchedDets.description;


                                    if (SchedDets.shortTermForecast != null)
                                        DRSchedDets["shorttermforecastid"] = SchedDets.shortTermForecast.id;
                                    DRSchedDets["modifiedby"] = SchedDets.metadata.modifiedBy.id;

                                    if (SchedDets.generationResults != null)
                                    {
                                        DRSchedDets["genresultsfailed"] = SchedDets.generationResults.failed;
                                        DRSchedDets["genresultsrunid"] = SchedDets.generationResults.runId;
                                    }

                                    WFMScheduleDetails.Rows.Add(DRSchedDets);
                                }
                            }
                        }
                    }
                }
            }

            return WFMScheduleDetails;
        }

        static DateTime GetFirstDayOfWeek(DateTime date, DayOfWeek firstDayOfWeek)
        {
            int offset = (7 + (date.DayOfWeek - firstDayOfWeek)) % 7;
            return date.AddDays(-offset).Date;
        }

        IEnumerable<DateTime> GetDateRange(DateTime start, DateTime end)
        {
            for (DateTime date = start; date <= end; date = date.AddDays(7))
            {
                yield return date;
            }
        }

        public DataTable GetTimeOffDataFromGC()
        {

            DataTable timeoffrequestData = DBUtil.CreateInMemTable("timeoffrequestData");

            DataTable muUnits = DBUtil.GetSQLTableData("select * from muDetails", "muDetails");

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            DateTime CalcDateDiffUTC = DateTime.UtcNow;
            DateTime CalcDateDiff = TimeZoneInfo.ConvertTimeFromUtc(CalcDateDiffUTC, AppTimeZone);

            double AddHours = (CalcDateDiff - CalcDateDiffUTC).TotalHours;

            Console.WriteLine("AddHours {0}", AddHours);
            TimeSpan timeDifference = DateTime.UtcNow - WFMScheduleLastUpdate;

            // Determine which query to use
            string userQuery;
            if (timeDifference.TotalDays <= 30)
            {
                // Use the complex query
                userQuery = @"
                    SELECT *
                    FROM userDetails
                    WHERE state != 'deleted' 
                       OR (state = 'deleted' AND updated >= NOW() - INTERVAL '30 days')";
            }
            else
            {
                // Use the simpler query
                userQuery = "SELECT * FROM userDetails";
            }

            // Fetch the users using the selected query
            DataTable Users = DBUtil.GetSQLTableData(userQuery, "userDetails");

            int RunCounter = 0;
            foreach (DataRow ManagementUnit in muUnits.Rows)
            {
                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits/" + ManagementUnit["id"] + "/users", GCApiKey);

                ManUnitUsers.ManagementUnit ManUnit = new ManUnitUsers.ManagementUnit();

                ManUnit = JsonConvert.DeserializeObject<ManUnitUsers.ManagementUnit>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });
                //Console.WriteLine("\nManagement Unit :{0}\n", ManagementUnit["id"]);
                //Console.WriteLine("Json            :{0}", JsonString);
                //if (ManUnit.entities != null)
                //    Console.WriteLine("User Count      :{0}", ManUnit.entities.Count());
                if (ManUnit.entities != null)
                {
                    foreach (ManUnitUsers.Entity UserDets in ManUnit.entities)
                    {
                        Console.Write("\nUser:");

                        if (RunCounter == 20)
                        {
                            GCUtilities.GetGCAPIKey();
                            Console.Write("\nChanging Keys\n");
                            GCApiKey = GCUtilities.GCApiKey;
                            RunCounter = 0;
                        }

                        System.Threading.Thread.Sleep(350);

                        JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits/" + ManagementUnit["id"] + "/users/" + UserDets.id + "/timeoffrequests", GCApiKey);
                        //Console.WriteLine("Json Returned           :{0}", JsonString);
                        TimeOffReq.TimeOff TimeOffRequests = new TimeOffReq.TimeOff();
                        RunCounter++;

                        TimeOffRequests = JsonConvert.DeserializeObject<TimeOffReq.TimeOff>(JsonString,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });

                        foreach (TimeOffReq.Timeoffrequest Request in TimeOffRequests.timeOffRequests)
                        {
                            Console.Write("R");
                            if (Request.isFullDayRequest == true)
                            {

                                foreach (string DayOffDate in Request.fullDayManagementUnitDates)
                                {
                                    Console.Write("F");
                                    DataRow TimeOffReqRow = timeoffrequestData.NewRow();

                                    TimeOffReqRow["id"] = Request.id;
                                    TimeOffReqRow["userid"] = Request.user.id;
                                    TimeOffReqRow["isfulldayrequest"] = Request.isFullDayRequest;
                                    TimeOffReqRow["status"] = Request.status;

                                    if (Request.notes.Length > 0)
                                    {
                                        if (Request.notes.Length < 400)
                                            TimeOffReqRow["notes"] = Request.notes;
                                        else
                                            TimeOffReqRow["notes"] = Request.notes.Substring(0, 400);
                                    }


                                    DateTime DummyStartTime = DateTime.Parse(DayOffDate + "T00:00:00.000Z").AddHours(AddHours * -1).ToUniversalTime();
                                    TimeOffReqRow["startdate"] = DummyStartTime;
                                    TimeOffReqRow["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(DummyStartTime, AppTimeZone);
                                    //TimeOffReqRow["startdateltc"] = DateTime.Parse(DayOffDate + "T00:00:00.000Z").AddHours(AddHours);

                                    TimeOffReqRow["timeoffduration"] = Request.dailyDurationMinutes;
                                    TimeOffReqRow["submittedbyid"] = Request.submittedBy.id;
                                    TimeOffReqRow["submittedate"] = Request.submittedDate;
                                    if (Request.reviewedBy != null)
                                    {
                                        TimeOffReqRow["reviewedbyid"] = Request.reviewedBy.id;
                                        TimeOffReqRow["revieweddate"] = Request.reviewedDate;
                                    }
                                    TimeOffReqRow["modifiedbyid"] = Request.modifiedBy.id;
                                    TimeOffReqRow["modifieddate"] = Request.modifiedDate;

                                    TimeOffReqRow["keyid"] = Request.id + "|F|" + DayOffDate + "T00:00:00.000Z"
                                        ;

                                    timeoffrequestData.Rows.Add(TimeOffReqRow);

                                }
                            }
                            else
                            {
                                foreach (DateTime PartDayOffDate in Request.partialDayStartDateTimes)
                                {
                                    Console.Write("P");
                                    DataRow TimeOffReqRow = timeoffrequestData.NewRow();

                                    TimeOffReqRow["id"] = Request.id;
                                    TimeOffReqRow["userid"] = Request.user.id;
                                    TimeOffReqRow["isfulldayrequest"] = Request.isFullDayRequest;
                                    TimeOffReqRow["status"] = Request.status;

                                    if (Request.notes.Length > 0)
                                    {
                                        if (Request.notes.Length < 400)
                                            TimeOffReqRow["notes"] = Request.notes;
                                        else
                                            TimeOffReqRow["notes"] = Request.notes.Substring(0, 400);
                                    }

                                    TimeOffReqRow["startdate"] = PartDayOffDate.AddHours(AddHours * -1).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                                    TimeOffReqRow["startdateltc"] = PartDayOffDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                                    TimeOffReqRow["timeoffduration"] = Request.dailyDurationMinutes;
                                    TimeOffReqRow["submittedbyid"] = Request.submittedBy.id;
                                    TimeOffReqRow["submittedate"] = Request.submittedDate;
                                    if (Request.reviewedBy != null)
                                    {
                                        TimeOffReqRow["reviewedbyid"] = Request.reviewedBy.id;
                                        TimeOffReqRow["revieweddate"] = Request.reviewedDate;
                                    }

                                    TimeOffReqRow["modifiedbyid"] = Request.modifiedBy.id;
                                    TimeOffReqRow["modifieddate"] = Request.modifiedDate;

                                    TimeOffReqRow["keyid"] = Request.id + "|P|" + PartDayOffDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");

                                    timeoffrequestData.Rows.Add(TimeOffReqRow);

                                }
                                //Console.ReadKey();
                            }
                        }
                    }
                }
                else
                {
                    Console.Write("MUNU");
                }
            }
            //timeoffrequestData.WriteXml("TimeOffRequests.xml");
            //Console.WriteLine("Returning Time Off Data");
            //Console.ReadKey();
            Console.Write("\n");
            return timeoffrequestData;
        }

        public DataSet GetScheduleDataFromGC(String StartDate, String EndDate)
        {

            WFMScheduleLastUpdate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            DataSet DSTemp = new DataSet();

            DataTable scheduleData = DBUtil.CreateInMemTable("scheduleData");
            DataTable timeoffData = DBUtil.CreateInMemTable("timeoffData");

            DataTable BusinessUnits = DBUtil.GetSQLTableData("select * from buDetails", "buDetails");
            DataTable Users = DBUtil.GetSQLTableData("select * from userDetails", "userDetails");

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();


            int MaxAgents = 499;
            int CurrentPage = 1;
            int TotalPages = 0;

            Console.WriteLine("Total Agents {0}", Users.Rows.Count);


            if (Users.Rows.Count % MaxAgents == 0)
            {
                Console.WriteLine("Equal Division Pages is not adding one");
                TotalPages = (Users.Rows.Count / MaxAgents);
            }
            else
            {
                Console.WriteLine("Not Equal Division Pages adding one");
                TotalPages = (Users.Rows.Count / MaxAgents) + 1;

            }


            DateTime NewStartDate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();


            DateTime NewEndDate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();


            while (CurrentPage <= TotalPages)

            {
                GCUtilities.GetGCAPIKey();
                GCApiKey = GCUtilities.GCApiKey;

                StringBuilder Userlist = new StringBuilder();
                DataTable dtTemp = Users.Rows.Cast<System.Data.DataRow>().Skip((CurrentPage - 1) * MaxAgents).Take(MaxAgents).CopyToDataTable();

                int Counter = 0;
                foreach (DataRow User in dtTemp.Rows)
                {
                    Userlist.Append(" \"" + User["id"] + "\",");
                    Counter++;
                }

                if (Users.Rows.Count > 0)
                {
                    Userlist.Length = Userlist.Length - 1;
                }

                NewStartDate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();
                NewEndDate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

                foreach (DataRow BusinessUnit in BusinessUnits.Rows)
                {

                    string RequestBody = "{ " +
                        " \"startDate\": \"" + NewStartDate.ToString("yyyy-MM-ddTHH:00:00.000Z") + "\"," +
                        " \"endDate\": \"" + NewEndDate.ToString("yyyy-MM-ddTHH:00:00.000Z") + "\"," +
                        " \"userIds\": [" +
                        " " + Userlist.ToString() + " " +
                        " ]" +
                        " }";



                    Console.WriteLine("\nFirst Pass BU: {0}", BusinessUnit["id"]);
                    Console.WriteLine("Starting: StartTime {0} End Time {1}", NewStartDate, NewEndDate);

                    string JsonString = string.Empty;
                    JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/businessunits/" + BusinessUnit["id"] + "/agentschedules/search", GCApiKey, RequestBody);

                    System.Threading.Thread.Sleep(2000);

                    if (JsonString != "{}")
                    {
                        //Console.WriteLine("JSON {0}",JsonString);
                        WFMSched.WFMSchedule WFMScheduleData = new WFMSched.WFMSchedule();

                        WFMScheduleData = JsonConvert.DeserializeObject<WFMSched.WFMSchedule>(JsonString,
                                       new JsonSerializerSettings
                                       {
                                           NullValueHandling = NullValueHandling.Ignore
                                       });


                        foreach (WFMSched.Agentschedule Agent in WFMScheduleData.result.agentSchedules)
                        {

                            string UserId = Agent.user.id;

                            foreach (WFMSched.Shift Shift in Agent.shifts)
                            {
                                string ShiftId = Shift.id;
                                DateTime ShiftStartDate = Shift.startDate;
                                int ShiftLength = Shift.lengthMinutes * 60;

                                string ScheduleId = Shift.schedule.id;
                                foreach (WFMSched.Activity Activity in Shift.activities)
                                {

                                    try
                                    {
                                        DataRow WFMShift = scheduleData.NewRow();

                                        WFMShift["keyid"] = UserId + "|" + ShiftId + "|" + Activity.startDate + "|" + Activity.lengthMinutes;

                                        WFMShift["userid"] = UserId;
                                        WFMShift["buid"] = BusinessUnit["id"];
                                        WFMShift["scheduleid"] = ScheduleId;
                                        WFMShift["shiftid"] = ShiftId;
                                        WFMShift["shiftstartdate"] = ShiftStartDate;
                                        WFMShift["shiftstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ShiftStartDate.ToUniversalTime(), AppTimeZone);             
                                        WFMShift["shiftlengthtime"] = ShiftLength;
                                        WFMShift["activitystartdate"] = Activity.startDate;

                                        if (Activity.startDate < NewStartDate)
                                            NewStartDate = Activity.startDate;

                                        if (Activity.startDate.AddMinutes(Activity.lengthMinutes) > NewEndDate)
                                            NewEndDate = Activity.startDate.AddMinutes(Activity.lengthMinutes);

                                        WFMShift["activitystartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Activity.startDate.ToUniversalTime(), AppTimeZone);
                                        WFMShift["activitylengthtime"] = Activity.lengthMinutes * 60;
                                        WFMShift["activitydescription"] = Activity.description;
                                        WFMShift["activitycodeid"] = Activity.activityCodeId;
                                        WFMShift["activitypaid"] = Activity.paid;
                                        WFMShift["shiftmanuallyeditted"] = Shift.manuallyEdited;

                                        scheduleData.Rows.Add(WFMShift);
                                        Console.Write("S");
                                    }
                                    catch (System.Data.ConstraintException)
                                    {
                                        Console.Write("D");
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine(ex.ToString());
                                        // TODO: throw;
                                    }
                                }

                                foreach (WFMSched.Fulldaytimeoffmarker DayOff in Agent.fullDayTimeOffMarkers)
                                {


                                    try
                                    {
                                        DataRow TimeOff = timeoffData.NewRow();
                                        TimeOff["keyid"] = UserId + "|" + DayOff.businessUnitDate + "|" + DayOff.activityCodeId + "|" + DayOff.lengthMinutes;

                                        TimeOff["userid"] = UserId;
                                        TimeOff["businessunitdate"] = DayOff.businessUnitDate;
                                        TimeOff["length"] = DayOff.lengthMinutes * 60;
                                        TimeOff["description"] = DayOff.description;
                                        TimeOff["activitycode"] = DayOff.activityCodeId;
                                        TimeOff["paid"] = DayOff.paid;
                                        TimeOff["timeoffrequestid"] = DayOff.timeOffRequestId;

                                        TimeOff["isfulldayrequest"] = true;
                                        timeoffData.Rows.Add(TimeOff);
                                        Console.Write("T");
                                    }
                                    catch (System.Data.ConstraintException)
                                    {
                                        Console.Write("D");
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine(ex.ToString());
                                        // TODO: throw;
                                    }
                                }
                                if (Shift.startDate < NewStartDate)
                                    NewStartDate = Shift.startDate;

                                if (Shift.startDate.AddMinutes(Shift.lengthMinutes) > NewEndDate)
                                    NewEndDate = Shift.startDate.AddMinutes(Shift.lengthMinutes);
                            }
                        }

                    }

                    JsonString = string.Empty;

                    Console.WriteLine("\nSecond Pass BU:{0}", BusinessUnit["id"]);
                    Console.WriteLine("\nStarting: StartTime {0} End Time {1}", NewStartDate, NewEndDate);
                    RequestBody = "{ " +
                        " \"startDate\": \"" + NewStartDate.ToString("yyyy-MM-ddTHH:00:00.000Z") + "\"," +
                        " \"endDate\": \"" + NewEndDate.ToString("yyyy-MM-ddTHH:00:00.000Z") + "\"," +
                        " \"userIds\": [" +
                        " " + Userlist.ToString() + " " +
                        " ]" +
                        " }";


                    JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/businessunits/" + BusinessUnit["id"] + "/agentschedules/search", GCApiKey, RequestBody);

                    if (JsonString != "{}")
                    {

                        //Console.WriteLine("JSON {0}", JsonString);
                        WFMSched.WFMSchedule WFMScheduleData = new WFMSched.WFMSchedule();

                        WFMScheduleData = JsonConvert.DeserializeObject<WFMSched.WFMSchedule>(JsonString,
                                       new JsonSerializerSettings
                                       {
                                           NullValueHandling = NullValueHandling.Ignore
                                       });

                        foreach (WFMSched.Agentschedule Agent in WFMScheduleData.result.agentSchedules)
                        {
                            Console.Write("A");

                            string UserId = Agent.user.id;

                            foreach (WFMSched.Shift Shift in Agent.shifts)
                            {
                                string ShiftId = Shift.id;
                                DateTime ShiftStartDate = Shift.startDate;
                                int ShiftLength = Shift.lengthMinutes * 60;


                                string ScheduleId = Shift.schedule.id;
                                foreach (WFMSched.Activity Activity in Shift.activities)
                                {

                                    try
                                    {
                                        DataRow WFMShift = scheduleData.NewRow();

                                        WFMShift["keyid"] = UserId + "|" + ShiftId + "|" + Activity.startDate + "|" + Activity.lengthMinutes;

                                        WFMShift["userid"] = UserId;
                                        WFMShift["buid"] = BusinessUnit["id"];
                                        WFMShift["scheduleid"] = ScheduleId;
                                        WFMShift["shiftid"] = ShiftId;
                                        WFMShift["shiftstartdate"] = ShiftStartDate;
                                        WFMShift["shiftstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ShiftStartDate.ToUniversalTime(), AppTimeZone);
                                        WFMShift["shiftlengthtime"] = ShiftLength;
                                        WFMShift["activitystartdate"] = Activity.startDate;

                                        if (Activity.startDate < NewStartDate)
                                            NewStartDate = Activity.startDate;

                                        if (Activity.startDate.AddMinutes(Activity.lengthMinutes) > NewEndDate)
                                            NewEndDate = Activity.startDate.AddMinutes(Activity.lengthMinutes);

                                        WFMShift["activitystartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Activity.startDate.ToUniversalTime(), AppTimeZone);
                                        WFMShift["activitylengthtime"] = Activity.lengthMinutes * 60;
                                        WFMShift["activitydescription"] = Activity.description;
                                        WFMShift["activitycodeid"] = Activity.activityCodeId;
                                        WFMShift["activitypaid"] = Activity.paid;
                                        WFMShift["shiftmanuallyeditted"] = Shift.manuallyEdited;

                                        scheduleData.Rows.Add(WFMShift);
                                        Console.Write("S");
                                    }
                                    catch (System.Data.ConstraintException)
                                    {
                                        Console.Write("D");
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine(ex.ToString());
                                        // TODO: throw;
                                    }
                                }

                                foreach (WFMSched.Fulldaytimeoffmarker DayOff in Agent.fullDayTimeOffMarkers)
                                {


                                    try
                                    {
                                        DataRow TimeOff = timeoffData.NewRow();
                                        TimeOff["keyid"] = UserId + "|" + DayOff.businessUnitDate + "|" + DayOff.activityCodeId + "|" + DayOff.lengthMinutes;

                                        TimeOff["userid"] = UserId;
                                        TimeOff["businessunitdate"] = DayOff.businessUnitDate;
                                        TimeOff["length"] = DayOff.lengthMinutes * 60;
                                        TimeOff["description"] = DayOff.description;
                                        TimeOff["activitycode"] = DayOff.activityCodeId;
                                        TimeOff["paid"] = DayOff.paid;
                                        TimeOff["timeoffrequestid"] = DayOff.timeOffRequestId;

                                        timeoffData.Rows.Add(TimeOff);
                                        Console.Write("T");
                                    }
                                    catch (System.Data.ConstraintException)
                                    {
                                        Console.Write("D");
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine(ex.ToString());
                                        // TODO: throw;
                                    }
                                }
                            }
                        }

                    }

                }


                CurrentPage++;
            }

            Console.WriteLine("\nEnding  : StartTime {0} End Time {1}", NewStartDate, NewEndDate);

            DBUtil.DeleteSchedData(NewStartDate, NewEndDate);

            DSTemp.Tables.Add(scheduleData);
            DSTemp.Tables.Add(timeoffData);

            return DSTemp;


        }

        public DataSet GetAdherenceDataFromGC(String StartDate, String EndDate, bool dstChange)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataSet DSTemp = new DataSet();
            Utils UCAUtils = new Utils();
            Errors = false;

            Console.WriteLine("Creating The Listening Channel");
            DownloadTable = CreateDownloadTable();
            ManagementDownloadMap = CreateManagementUnitDownloadMap();

            var NotChannelOutput = CreateNotChannel();
            Boolean ChannelOpened = NotChannelOutput.Item1;
            string SocketID = NotChannelOutput.Item2;

            Console.WriteLine("Allowing The Listening Channel To Form");

            System.Threading.Thread.Sleep(5000);
            CanContinue = false;

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            if (ChannelOpened == true)
            {
                Console.WriteLine("Channel Opened : {0}", ChannelOpened);
            }
            else
            {
                Console.WriteLine("Channel Opened Failed: Returning Null for a Table");
                return null;
            }

            DataTable ManUnits = DBUtil.GetSQLTableData("select * from muDetails", "muDetails");

            //int MaxUsers = 50;
            int CurrentPage = 1;
            int TotalPages = 1;
            TotalResponses = 0;

            if (ManUnits.Rows.Count > 0)
            {
                TotalPages = (ManUnits.Rows.Count);
                TotalResponses = TotalPages;

                foreach (DataRow ManUnit in ManUnits.Rows)
                {
                    string RequestBody = "{  \"items\": [{" +
                            " \"managementUnitId\": \"" + ManUnit["id"] + "\"," +
                            " \"startDate\": \"" + StartDate + "\"," +
                            " \"endDate\": \"" + EndDate + "\"," +          
                            " \"includeExceptions\": \"true\"," +
                            " \"includeActuals\": \"true\"" +
                            " }]," + 
                            "  \"timeZone\": \"" + ManUnit["timezone"] + "\"}";
                    
                    string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/adherence/historical/bulk", GCApiKey, RequestBody);

                    Console.WriteLine("Requesting Adherence Data Between {0} - {1} for: {2} ({3}) ({4}/{5})", StartDate, EndDate, ManUnit["name"],ManUnit["id"],CurrentPage,TotalPages);

                    if (JsonString == "{}" || JsonString == null)
                    {
                        Console.WriteLine("No Data returned, skipping management unit: {0} ({1})", ManUnit["name"], ManUnit["id"]);
                        TotalResponses--;
                        continue;
                        // Console.WriteLine("Nothing to Find Here");
                    }

                    AdherenceJobRequest AdhJobData = new AdherenceJobRequest();

                    AdhJobData = JsonConvert.DeserializeObject<AdherenceJobRequest>(JsonString,
                          new JsonSerializerSettings
                          {
                              NullValueHandling = NullValueHandling.Ignore
                          });

                  //  Console.WriteLine("Processing:{0}", AdhJobData.job.id);

                    DataRow ManDownMap = ManagementDownloadMap.NewRow();

                    ManDownMap["Managementid"] = ManUnit["id"];
                    if(AdhJobData.job == null)
                    {
                        ManDownMap["JobId"] = "";
                    }
                    else
                    {
                        ManDownMap["JobId"] = AdhJobData.job.id;
                    }
                    ManDownMap["adherenceExceptionThresholdSeconds"] = ManUnit["adherenceExceptionThresholdSeconds"];

                    ManagementDownloadMap.Rows.Add(ManDownMap);

                    CurrentPage++;
                }

            }
            else
            {
                throw new Exception(@"Adherence Sync Failed: There are no Management Units in Fact Data");
            }

            while (TotalResponses > 0)
            {
                Console.WriteLine("Waiting {0} Total Responses We Are Waiting For : {1}", DateTime.Now, TotalResponses);
                System.Threading.Thread.Sleep(5000);
            }
            Console.WriteLine("Received Last Result");

            Console.WriteLine("Final Wait for Buffers");
            System.Threading.Thread.Sleep(5000);

            //ManagementDownloadMap.WriteXml("ManagementDownloadMap.xml");
            Console.WriteLine("Number of Downloads Available:{0}", DownloadTable.Rows.Count);

            //if (TotalPages > DownloadTable.Rows.Count)
            //{
            //    Errors = true;
            //    Console.WriteLine("There has been an Error Processing Start Date:{0}", StartDate);
            //    return null;
            //}

            Console.WriteLine("Creating Adherence Day In Mem");
            DataTable AdherenceDay = DBUtil.CreateInMemTable("adherencedayData");
            Console.WriteLine("Creating Adherence Exc In Mem");
            DataTable AdherenceExc = DBUtil.CreateInMemTable("adherenceexcData");
            Console.WriteLine("Creating Adherence Act In Mem");
            DataTable AdherenceAct = DBUtil.CreateInMemTable("adherenceactData");

            string JsonAdhString = string.Empty;

            WebClient Client = new WebClient();
            Client.Headers[HttpRequestHeader.AcceptEncoding] = "gzip";

            if (DownloadTable.Rows.Count > 0)
            {
                foreach (DataRow DownLoadRow in DownloadTable.Rows)
                {
                    Console.WriteLine("\nWorking on Download ID {0}", DownLoadRow["id"]);

                    int Tolerance = 0;

                    DataRow ManLookup = ManagementDownloadMap.Select("jobid='" + DownLoadRow["id"] + "'").FirstOrDefault();

                    if (ManLookup != null)
                    {
                        Tolerance = (int)ManLookup["adherenceExceptionThresholdSeconds"];
                    }
                    else
                    {
                        Tolerance = 0;
                    }

                    GZipStream ResponseStream = new GZipStream(Client.OpenRead(DownLoadRow["downloadURl"].ToString()), CompressionMode.Decompress);
                    StreamReader WebReader = new StreamReader(ResponseStream);
                    String JsonString = WebReader.ReadToEnd();
                    ResponseStream.Dispose();
                    WebReader.Dispose();

                    Adh.AdherenceData AdhData = new Adh.AdherenceData();
                    AdhData = JsonConvert.DeserializeObject<Adh.AdherenceData>(JsonString,
                          new JsonSerializerSettings
                          {
                              NullValueHandling = NullValueHandling.Ignore
                          });

                    Console.WriteLine("Processing:{0}", AdhData.managementUnitId);

                    foreach (Adh.Users UserData in AdhData.userResults)
                    {
                        try
                        {
                            Console.Write("#");

                            if (UserData.dayMetrics == null)
                            {

                            }   
                            else
                            {
                                Boolean FirstdayMetrics = false;
                                DateTime StartDateAdherence = AdhData.startDate;

                                if (StartDateAdherence > DateTime.Parse(EndDate))
                                {
                                    Console.WriteLine($"Date {StartDateAdherence.ToString()} is ahead of querying date {EndDate}. Skip processing data.");
                                    continue;
                                }

                                foreach (Adh.Daymetric UserDay in UserData.dayMetrics)
                                {
                                    //Console.WriteLine($"Currently processing date: {StartDateAdherence.ToString()}");

                                    if (dstChange && (StartDateAdherence == DateTime.Parse(EndDate).ToUniversalTime() || StartDateAdherence == DateTime.Parse(EndDate).AddDays(-1).ToUniversalTime()))
                                    {
                                        continue;
                                    }

                                    DataRow AdhDayUser = AdherenceDay.NewRow();

                                    AdhDayUser["UserId"] = UserData.userId;

                                    AdhDayUser["impact"] = UserData.impact;


                                    if (FirstdayMetrics == false)
                                    {
                                        FirstdayMetrics = true;
                                        Console.WriteLine("StartDate:{0} OffsetSecs:{1} UTC:{2}", AdhData.startDate, UserDay.dayStartOffsetSeconds, AdhData.startDate.ToUniversalTime());
                                        
                                        AdhDayUser["dayStartOffsetSecs"] = UserDay.dayStartOffsetSeconds;
                                    }
                                    AdhDayUser["adherencePerc"] = UserDay.adherencePercentage;
                                    AdhDayUser["adherenceScheduleSecs"] = UserDay.adherenceScheduleSeconds;
                                    AdhDayUser["conformPerc"] = UserDay.conformancePercentage;
                                    AdhDayUser["conformanceScheduleSecs"] = UserDay.conformanceScheduleSeconds;
                                    AdhDayUser["conformanceActualSecs"] = UserDay.conformanceActualSeconds;
                                    AdhDayUser["exceptionCount"] = UserDay.exceptionCount;
                                    AdhDayUser["exceptionDurationSecs"] = UserDay.exceptionDurationSeconds;
                                    AdhDayUser["impactSeconds"] = UserDay.impactSeconds;
                                    AdhDayUser["scheduleLengthSecs"] = UserDay.scheduleLengthSeconds;
                                    AdhDayUser["actualLengthSecs"] = UserDay.actualLengthSeconds;
                                    DateTimeOffset StartDateAdherenceOffset = DateTime.SpecifyKind(StartDateAdherence, DateTimeKind.Utc);
                                    AdhDayUser["startdateltc"] = StartDateAdherenceOffset.ToOffset(AppTimeZone.GetUtcOffset(StartDateAdherence)).DateTime;
                                    AdhDayUser["startDate"] = StartDateAdherence;

                                    string DayUserKeyData = UserData.userId + "|" + StartDateAdherence + "|";
                                    string compositeKey = UCAUtils.GetSHA256CompositeKey(DayUserKeyData);
                                    AdhDayUser["keyid"] = "v1_"+compositeKey;
                                    AdherenceDay.Rows.Add(AdhDayUser);
                                    StartDateAdherence = StartDateAdherence.AddDays(1);
                                }

                                StartDateAdherence = AdhData.startDate;

                                foreach (Adh.Exceptioninfo Exception in UserData.exceptionInfo)
                                {

                                    if ((Exception.endOffsetSeconds - Exception.startOffsetSeconds) > Tolerance)
                                    {
                                        DataRow UserExp = AdherenceExc.NewRow();
                                        UserExp["userid"] = UserData.userId;
                                        UserExp["startdate"] = StartDateAdherence.AddSeconds(Exception.startOffsetSeconds);
                                        DateTimeOffset ExceptionStartDateAdherenceOffset = DateTime.SpecifyKind(StartDateAdherence.AddSeconds(Exception.startOffsetSeconds), DateTimeKind.Utc);
                                        UserExp["startdateltc"] = ExceptionStartDateAdherenceOffset.ToOffset(AppTimeZone.GetUtcOffset(StartDateAdherence.AddSeconds(Exception.startOffsetSeconds))).DateTime;
                                        UserExp["enddate"] = StartDateAdherence.AddSeconds(Exception.endOffsetSeconds);
                                        DateTimeOffset ExceptionEndDateAdherenceOffset = DateTime.SpecifyKind(StartDateAdherence.AddSeconds(Exception.endOffsetSeconds), DateTimeKind.Utc);
                                        UserExp["enddateltc"] = ExceptionEndDateAdherenceOffset.ToOffset(AppTimeZone.GetUtcOffset(StartDateAdherence.AddSeconds(Exception.endOffsetSeconds))).DateTime;
                                        UserExp["durationsecs"] = (Exception.endOffsetSeconds - Exception.startOffsetSeconds) - Tolerance;
                                        UserExp["actualdurationsecs"] = (Exception.endOffsetSeconds - Exception.startOffsetSeconds);
                                        UserExp["tolerance"] = Tolerance;
                                        UserExp["scheduledActivityCategory"] = Exception.scheduledActivityCategory;
                                        UserExp["actualActivityCategory"] = Exception.actualActivityCategory;
                                        UserExp["systemPresence"] = Exception.systemPresence;
                                        UserExp["routingStatus"] = Exception.routingStatus;
                                        UserExp["impact"] = Exception.impact;

                                        string exceptionKeyData = UserData.userId + "|" + UserExp["startdate"] + "|" +UserExp["enddate"];
                                        string compositeKeyException = UCAUtils.GetSHA256CompositeKey(exceptionKeyData);
                                        UserExp["keyid"] = "v1_"+compositeKeyException;
                                        AdherenceExc.Rows.Add(UserExp);
                                    }
                                    else
                                    {

                                    }

                                }

                                foreach (Adh.Actual Actual in UserData.actuals)
                                {

                                    if (Actual.endOffsetSeconds - Actual.startOffsetSeconds > 0)
                                    {
                                        DataRow UserAct = AdherenceAct.NewRow();
                                        UserAct["userid"] = UserData.userId;
                                        UserAct["startdate"] = StartDateAdherence.AddSeconds(Actual.startOffsetSeconds);
                                        UserAct["enddate"] = StartDateAdherence.AddSeconds(Actual.endOffsetSeconds);
                                        DateTimeOffset ActualStartDateAdherenceOffset = DateTime.SpecifyKind(StartDateAdherence.AddSeconds(Actual.startOffsetSeconds), DateTimeKind.Utc);
                                        UserAct["startdateltc"] = ActualStartDateAdherenceOffset.ToOffset(AppTimeZone.GetUtcOffset(StartDateAdherence.AddSeconds(Actual.startOffsetSeconds))).DateTime;
                                        DateTimeOffset ActualEndDateAdherenceOffset = DateTime.SpecifyKind(StartDateAdherence.AddSeconds(Actual.endOffsetSeconds), DateTimeKind.Utc);
                                        UserAct["enddateltc"] = ActualEndDateAdherenceOffset.ToOffset(AppTimeZone.GetUtcOffset(StartDateAdherence.AddSeconds(Actual.endOffsetSeconds))).DateTime;
                                        UserAct["durationsecs"] = Actual.endOffsetSeconds - Actual.startOffsetSeconds;
                                        UserAct["actualActivityCategory"] = Actual.actualActivityCategory;

                                        string actualKeyData = UserData.userId + "|" + UserAct["startdate"] + "|" +UserAct["enddate"];
                                        string compositeActualKey = UCAUtils.GetSHA256CompositeKey(actualKeyData);
                                        UserAct["keyid"] = "v1_"+compositeActualKey;
                                        AdherenceAct.Rows.Add(UserAct);
                                    }

                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.ToString());
                        }
                    }
                }
            }

            Console.WriteLine("\nMerging Adherence Data");

            DSTemp.Tables.Add(AdherenceDay);
            DSTemp.Tables.Add(AdherenceExc);
            DSTemp.Tables.Add(AdherenceAct);

            // Unsubscribe from channel
            HttpResponseMessage Response = null;
            HttpClientHandler Handler = new HttpClientHandler();
            HttpClient ClientHTTP = new HttpClient(Handler);
            ClientHTTP.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", GCApiKey);
            try
            {
                Response = ClientHTTP.DeleteAsync(URI + "/api/v2/notifications/channels/" + SocketID + "/subscriptions").GetAwaiter().GetResult();

                Console.WriteLine("Deleted subscriptions from {0}", SocketID);
            }
            catch
            {
                Console.WriteLine("Failed to deleted subscriptions from {0}", SocketID);
            }
            
            return DSTemp;
        }

        private DataTable CreateScheduleData()
        {
            DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
            DBUtil.Initialize();

            DataTable DTTemp = DBUtil.GetSQLTableData("Select top (0) * from scheduleData", "scheduleData").Clone();

            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["keyid"] };

            return DTTemp;
        }

        private DataTable CreateTimeOffData()
        {
            DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
            DBUtil.Initialize();

            DataTable DTTemp = DBUtil.GetSQLTableData("Select top (0) * from timeoffData", "timeoff").Clone();

            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["keyid"] };

            return DTTemp;
        }

        private DataTable CreateDownloadTable()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("downloadUrl", typeof(String));
            DTTemp.TableName = "DownloadTable";

            //DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["id"] };
            return DTTemp;
        }

        private (Boolean, string) CreateNotChannel()
        {
            Boolean Successful = true;
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            Console.WriteLine("\nCreating Channel - To Listen For Adherence Readiness");

            WebSocketDetail WSSocketAdh = new WebSocketDetail();

            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/notifications/channels", GCApiKey, "");

            WSSocketAdh = JsonConvert.DeserializeObject<WebSocketDetail>(JsonString,
                   new JsonSerializerSettings
                   {
                       NullValueHandling = NullValueHandling.Ignore
                   });

            //Channel Created - Create Subscription
            //v2.users.b37b693d-5fec-4f34-bb87-98e422015415.workforcemanagement.historicaladherencequery


            Console.WriteLine("Setting Subscription For {0}", OAuthUser);

            string JSONBodyString = " \n[{ \"id\": \"v2.users." + OAuthUser + ".workforcemanagement.adherence.historical.bulk\"}]";
            string URL = URI + "/api/v2/notifications/channels/" + WSSocketAdh.id + "/subscriptions";

            //Console.WriteLine("URL Soc: {0}", URL);

            Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WSSocketAdh.id, GCApiKey.Substring(0, 6));

            JsonString = JsonActions.JsonReturnString(URL,
                                                      GCApiKey,
                                                      JSONBodyString);


            WSSUserActSocket(WSSocketAdh.connectUri.ToString(), "Adherence");

            return (Successful, WSSocketAdh.id);
        }

        private async void WSSUserActSocket(string SocketAddress, string ThreadName)
        {

            if (SocketAdh != null)
            {
                try
                {
                    await SocketAdh.CloseOutputAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                }
                SocketAdh.Dispose();
            }

            SocketAdh = new ClientWebSocket();
            SocketAdh.Options.KeepAliveInterval = TimeSpan.Zero;

            while (true)
            {

                Console.WriteLine("\nAct  WebSocket: {0} Before Connect", ThreadName);
                await SocketAdh.ConnectAsync(new Uri(SocketAddress), CancellationToken.None);
                Console.WriteLine("\nAct  WebSocket: {0} After  Connect", ThreadName);
                try
                {
                    await Receive(SocketAdh, ThreadName);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                    await SocketAdh.CloseOutputAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                }
            }
        }

        private async Task Receive(ClientWebSocket socket, String ThreadName)
        {

            var buffer = new ArraySegment<byte>(new byte[2048]);
            DownloadTable Notification = new DownloadTable();

            do
            {
                WebSocketReceiveResult result;
                using (var ms = new MemoryStream())
                {
                    do
                    {
                        result = await socket.ReceiveAsync(buffer, CancellationToken.None);
                        ms.Write(buffer.Array, buffer.Offset, result.Count);
                    } while (!result.EndOfMessage);

                    if (result.MessageType == WebSocketMessageType.Close)
                    {
                        Console.WriteLine("WebSocket {0} Closing", ThreadName);
                        break;
                    }

                    ms.Seek(0, SeekOrigin.Begin);
                    using (var reader = new StreamReader(ms, Encoding.UTF8))
                    {
                        string JsonString = await reader.ReadToEndAsync();

                        if (JsonString.IndexOf("WebSocket Heartbeat") > 0)
                        {
                            Console.Write("Hrt:");
                        }
                        else
                        {
                            Notification = JsonConvert.DeserializeObject<DownloadTable>(JsonString,
                                                        new JsonSerializerSettings
                                                        {
                                                            NullValueHandling = NullValueHandling.Ignore
                                                        });

                            string NotificationType = Notification.topicName.ToLower().Split('.')[3];
                            //Console.WriteLine("NotificationType:{0} Total Responses Left {1}", NotificationType, TotalResponses);
                            //Console.WriteLine("JSON Returned : \n{0}", JsonString);

                            if (JsonString.IndexOf("downloadUrl") > 0)
                            {
                                foreach (string DownloadURL in Notification.eventBody.downloadUrls)
                                {
                                    DataRow DownloadRow = DownloadTable.NewRow();

                                    DownloadRow["id"] = Notification.eventBody.id;
                                    DownloadRow["downloadUrl"] = DownloadURL;
                                    DownloadTable.Rows.Add(DownloadRow);
                                }
                            }
                            else
                            {
                                Console.WriteLine("No Download:");
                                //Console.WriteLine("JSON Returned : \n{0}", JsonString);
                            }
                            TotalResponses--;
                        }

                    }
                }
            } while (true);
        }

        private DataTable CreateManagementUnitDownloadMap()
        {

            DataTable DTTemp = new DataTable();

            DTTemp.Columns.Add("Managementid", typeof(String));
            DTTemp.Columns.Add("jobid", typeof(String));
            DTTemp.Columns.Add("adherenceExceptionThresholdSeconds", typeof(int));
            DTTemp.TableName = "ManagementUnitDownloadMap";

            return DTTemp;

        }
    }



    // public class AdherenceJobRequest
    // {
    //     public string id { get; set; }
    //     public string queryState { get; set; }
    // }
    public class AdherenceJobRequest
    {
        public Job job { get; set; }
        public List<string> downloadUrls { get; set; }
    }

    public class Job
    {
        public string id { get; set; }
        public string status { get; set; }
        public string selfUri { get; set; }
    }

    public class DownloadTable
    {
        public string topicName { get; set; }
        public string version { get; set; }
        public Eventbody eventBody { get; set; }
        public Metadata metadata { get; set; }
    }

    public class Eventbody
    {
        public string id { get; set; }
        public string downloadUrl { get; set; }
        public string[] downloadUrls { get; set; }
        public string queryState { get; set; }
    }

    public class Metadata
    {
        public string CorrelationId { get; set; }
    }





}
// spell-checker: ignore: onqueueusers oactiveusers omemberusers oonqueueusers ooffqueueusers businessunitid drbu
// spell-checker: ignore: jobid perc acti managementid shiftid buid avghandleperinterval modifiedby genresultsrunid
// spell-checker: ignore: isfulldayrequest submittedbyid reviewedbyid modifiedbyid munu activitystartdateltc
// spell-checker: ignore: timeoffrequestid
