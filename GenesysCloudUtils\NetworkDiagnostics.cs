using System;
using System.Net;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    public class NetworkDiagnostics
    {
        private readonly ILogger _logger;

        public NetworkDiagnostics(ILogger logger)
        {
            _logger = logger;
        }

        public async Task<bool> CheckConnectivityToHost(string host, int port = 443, int timeoutMs = 5000)
        {
            try
            {
                _logger?.LogInformation($"Checking connectivity to {host}:{port}");
                
                // First try to resolve DNS
                var hostEntry = await Dns.GetHostEntryAsync(host);
                _logger?.LogInformation($"DNS resolved {host} to {string.Join(", ", hostEntry.AddressList.Select(a => a.ToString()))}");
                
                // Then try to ping
                using (var ping = new Ping())
                {
                    foreach (var address in hostEntry.AddressList)
                    {
                        try
                        {
                            var reply = await ping.SendPingAsync(address, timeoutMs);
                            _logger?.LogInformation($"Ping to {address}: {reply.Status}, time = {reply.RoundtripTime}ms");
                            
                            if (reply.Status == IPStatus.Success)
                            {
                                // If we can ping and the round trip time is high, warn about latency
                                if (reply.RoundtripTime > 200)
                                {
                                    _logger?.LogWarning($"High latency detected: {reply.RoundtripTime}ms to {host}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogWarning(ex, $"Failed to ping {address}");
                        }
                    }
                }
                
                // Finally try a TCP connection to the specific port
                using (var client = new System.Net.Sockets.TcpClient())
                {
                    var connectTask = client.ConnectAsync(host, port);
                    var timeoutTask = Task.Delay(timeoutMs);
                    
                    if (await Task.WhenAny(connectTask, timeoutTask) == connectTask)
                    {
                        // Connection successful
                        _logger?.LogInformation($"Successfully established TCP connection to {host}:{port}");
                        return true;
                    }
                    else
                    {
                        // Connection timed out
                        _logger?.LogWarning($"TCP connection to {host}:{port} timed out after {timeoutMs}ms");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"Error checking connectivity to {host}:{port}");
                return false;
            }
        }

        public void LogNetworkInterfaces()
        {
            try
            {
                _logger?.LogInformation("Logging network interface information");
                
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();
                foreach (var nic in interfaces)
                {
                    if (nic.OperationalStatus == OperationalStatus.Up)
                    {
                        var ipProps = nic.GetIPProperties();
                        _logger?.LogInformation($"Interface: {nic.Name}, Type: {nic.NetworkInterfaceType}, Status: {nic.OperationalStatus}");
                        
                        foreach (var addr in ipProps.UnicastAddresses)
                        {
                            _logger?.LogInformation($"  Address: {addr.Address}, Subnet: {addr.IPv4Mask}");
                        }
                        
                        if (ipProps.GatewayAddresses.Count > 0)
                        {
                            foreach (var gateway in ipProps.GatewayAddresses)
                            {
                                _logger?.LogInformation($"  Gateway: {gateway.Address}");
                            }
                        }
                        
                        if (ipProps.DnsAddresses.Count > 0)
                        {
                            foreach (var dns in ipProps.DnsAddresses)
                            {
                                _logger?.LogInformation($"  DNS: {dns}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error logging network interfaces");
            }
        }
    }
}
