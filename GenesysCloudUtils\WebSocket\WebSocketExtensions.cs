using System;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace GenesysCloudUtils.WebSocket
{
    public static class WebSocketExtensions
    {
        /// <summary>
        /// Sends a string message through the WebSocket
        /// </summary>
        public static async Task SendStringAsync(this ClientWebSocket webSocket, string message, CancellationToken cancellationToken = default)
        {
            if (webSocket.State != WebSocketState.Open)
                throw new InvalidOperationException("WebSocket is not in the Open state");
                
            var messageBytes = Encoding.UTF8.GetBytes(message);
            await webSocket.SendAsync(
                new ArraySegment<byte>(messageBytes),
                WebSocketMessageType.Text,
                true, // endOfMessage
                cancellationToken);
        }
        
        /// <summary>
        /// Receives a complete message from the WebSocket as a string
        /// </summary>
        public static async Task<string> ReceiveStringAsync(this ClientWebSocket webSocket, CancellationToken cancellationToken = default)
        {
            var buffer = new byte[8192];
            var stringBuilder = new StringBuilder();
            
            WebSocketReceiveResult result;
            do
            {
                result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);
                
                if (result.MessageType == WebSocketMessageType.Close)
                {
                    return null;
                }
                
                stringBuilder.Append(Encoding.UTF8.GetString(buffer, 0, result.Count));
                
            } while (!result.EndOfMessage);
            
            return stringBuilder.ToString();
        }
    }
}
