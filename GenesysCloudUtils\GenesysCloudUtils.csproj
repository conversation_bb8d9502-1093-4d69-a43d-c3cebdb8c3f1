<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RuntimeIdentifiers>win-x64;linux-x64;linux-musl-x64</RuntimeIdentifiers>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <RestorePackagesWithLockFile>false</RestorePackagesWithLockFile>
    <RestoreLockedMode Condition="'$(ContinuousIntegrationBuild)' == 'true'">true</RestoreLockedMode>
    <AnalysisLevel>latest</AnalysisLevel>
    <NoWarn>CS8632</NoWarn>
    <!--
    CS8632  The annotation for nullable reference types should only be used in code within a '#nullable' annotations context
    -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ChilkatDnCore" Version="9.5.0.91" />
    <!--
    TODO: Remove or check references below when updating Chilkat library to see if NU1605 error change
    -->
    <PackageReference Include="System.Diagnostics.Tracing" Version="4.3.*" />
    <PackageReference Include="System.Net.NameResolution" Version="4.3.*" />
    <PackageReference Include="System.Net.Primitives" Version="4.3.*" />
    <PackageReference Include="System.Threading.ThreadPool" Version="4.3.*" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="7.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.WebSockets.Client" Version="4.3.2" />
    <PackageReference Include="PureCloudPlatform.Client.V2" Version="154.0.0">
      <!--
        TODO: Requires ini-parser 2.5.2 which is incompatible with net6.0
      -->
    </PackageReference>
    <PackageReference Include="Polly" Version="7.2.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DBUtils\DBUtils.csproj" />
    <ProjectReference Include="..\StandardUtils\StandardUtils.csproj" />
    <!-- <ProjectReference Include="..\GCRealTime\GCRealTime.csproj" /> -->
    <Compile Remove="Extensions\DBUtilsExtensions.cs" />
    <Compile Remove="MultiThread.cs" />
  </ItemGroup>

  <!-- New folder structure for WebSockets -->
  <ItemGroup>
    <Folder Include="WebSocket\" />
  </ItemGroup>

</Project>
