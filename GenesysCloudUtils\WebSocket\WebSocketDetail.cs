using System;

namespace GenesysCloudUtils.WebSocket
{
    /// <summary>
    /// Represents details of a web socket connection for realtime monitoring
    /// </summary>
    public class WebSocketDetail
    {
        /// <summary>
        /// The notification channel ID
        /// </summary>
        public string id { get; set; }
        
        /// <summary>
        /// The WebSocket connect URI
        /// </summary>
        public string connectUri { get; set; }
        
        /// <summary>
        /// A friendly name for identifying this websocket
        /// </summary>
        public string ReportName { get; set; }
        
        /// <summary>
        /// When this channel was created
        /// </summary>
        public DateTime Created { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// When this channel expires
        /// </summary>
        public DateTime Expires { get; set; } = DateTime.UtcNow.AddHours(1);
        
        /// <summary>
        /// Returns true if the channel needs to be refreshed (less than 10 minutes before expiration)
        /// </summary>
        public bool NeedsRefresh => DateTime.UtcNow > Expires.AddMinutes(-10);
        
        /// <summary>
        /// Returns true if the channel has expired
        /// </summary>
        public bool IsExpired => DateTime.UtcNow > Expires;
        
        public override string ToString()
        {
            return $"WebSocketDetail{{id={id}, name={ReportName}, created={Created}, expires={Expires}}}";
        }
    }
}
