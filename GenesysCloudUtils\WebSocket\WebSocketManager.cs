using System;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils.WebSocket
{
    public class WebSocketManager : IWebSocketManager
    {
        private readonly ILogger _logger;
        private readonly string _threadName;
        private readonly Action<string, string> _messageHandler;
        private readonly JsonUtils _jsonUtils;
        private ClientWebSocket _webSocket;
        private CancellationTokenSource _cancellationTokenSource;
        private bool _isRunning;
        private bool _disposedValue;
        private DateTime _lastPingTime = DateTime.MinValue;
        private readonly TimeSpan _pingInterval = TimeSpan.FromSeconds(30);
        
        public string ChannelId => WebSocketDetail?.id;
        public bool IsConnected => _webSocket?.State == WebSocketState.Open;
        public WebSocketDetail WebSocketDetail { get; private set; }
        public DateTime LastDataReceived { get; private set; } = DateTime.UtcNow;
        public bool ShouldExit { get; set; }

        public WebSocketManager(
            ILogger logger,
            WebSocketDetail detail,
            string threadName,
            Action<string, string> messageHandler,
            JsonUtils jsonUtils)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            WebSocketDetail = detail ?? throw new ArgumentNullException(nameof(detail));
            _threadName = threadName ?? "WebSocket";
            _messageHandler = messageHandler ?? throw new ArgumentNullException(nameof(messageHandler));
            _jsonUtils = jsonUtils ?? throw new ArgumentNullException(nameof(jsonUtils));
        }

        public void StartWebSocketConnection()
        {
            _logger?.LogInformation("Starting WebSocket connection for channel {ChannelId}", WebSocketDetail.id);
            
            if (_isRunning)
            {
                _logger?.LogWarning("WebSocket already running for channel {ChannelId}", WebSocketDetail.id);
                return;
            }
            
            _isRunning = true;
            _cancellationTokenSource = new CancellationTokenSource();
            
            // Start the connection in a background task
            Task.Run(async () => await RunWebSocketConnectionAsync(_cancellationTokenSource.Token))
                .ContinueWith(t => 
                {
                    if (t.IsFaulted)
                    {
                        _logger?.LogError(t.Exception, "WebSocket connection task faulted for channel {ChannelId}", WebSocketDetail.id);
                    }
                });
        }

        public void StopWebSocketConnection()
        {
            _logger?.LogInformation("Stopping WebSocket connection for channel {ChannelId}", WebSocketDetail.id);
            
            _isRunning = false;
            ShouldExit = true;
            _cancellationTokenSource?.Cancel();
            
            try
            {
                if (_webSocket != null && 
                    (_webSocket.State == WebSocketState.Open || _webSocket.State == WebSocketState.Connecting))
                {
                    // Try to close the connection properly
                    _webSocket.CloseAsync(
                        WebSocketCloseStatus.NormalClosure, 
                        "Closing connection", 
                        CancellationToken.None).Wait(5000);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error closing WebSocket for channel {ChannelId}", WebSocketDetail.id);
            }
            
            _webSocket?.Dispose();
            _webSocket = null;
        }

        public async Task SendAsync(string data)
        {
            if (_webSocket?.State != WebSocketState.Open)
            {
                throw new InvalidOperationException("WebSocket is not connected");
            }
            
            byte[] buffer = Encoding.UTF8.GetBytes(data);
            await _webSocket.SendAsync(
                new ArraySegment<byte>(buffer),
                WebSocketMessageType.Text,
                true,
                CancellationToken.None);
        }

        public void SendHeartbeat()
        {
            if (_webSocket?.State == WebSocketState.Open)
            {
                try
                {
                    _messageHandler("WebSocket Heartbeat", _threadName);
                    string pingMessage = "{\"message\":\"ping\"}";
                    SendAsync(pingMessage).Wait();
                    _lastPingTime = DateTime.UtcNow;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error sending heartbeat for channel {ChannelId}", WebSocketDetail.id);
                }
            }
        }

        private async Task RunWebSocketConnectionAsync(CancellationToken cancellationToken)
        {
            int retryCount = 0;
            const int maxRetries = 10;
            TimeSpan initialRetryDelay = TimeSpan.FromSeconds(1);
            string logPrefix = $"[WebSocket:{_threadName}]";
            
            while (_isRunning && !cancellationToken.IsCancellationRequested && !ShouldExit)
            {
                try
                {
                    _webSocket = new ClientWebSocket();
                    
                    _logger?.LogInformation("{Prefix} Connecting to WebSocket: {Url}", logPrefix, WebSocketDetail.connectUri);
                    await _webSocket.ConnectAsync(new Uri(WebSocketDetail.connectUri), cancellationToken);
                    
                    _logger?.LogInformation("{Prefix} WebSocket connected for channel {ChannelId}", logPrefix, WebSocketDetail.id);
                    retryCount = 0; // Reset retry count on successful connection
                    
                    // Start a heartbeat ping task
                    Task heartbeatTask = Task.Run(async () => await SendHeartbeatsAsync(cancellationToken));
                    
                    // Handle incoming messages
                    await ReceiveMessagesAsync(cancellationToken);
                    
                    // If we get here, the connection was closed
                    _logger?.LogInformation("{Prefix} WebSocket connection closed for channel {ChannelId}", logPrefix, WebSocketDetail.id);
                }
                catch (OperationCanceledException)
                {
                    // Normal cancellation
                    _logger?.LogInformation("{Prefix} WebSocket operation cancelled for channel {ChannelId}", logPrefix, WebSocketDetail.id);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "{Prefix} WebSocket error for channel {ChannelId}", logPrefix, WebSocketDetail.id);
                    
                    if (_isRunning && !cancellationToken.IsCancellationRequested && !ShouldExit)
                    {
                        retryCount++;
                        if (retryCount > maxRetries)
                        {
                            _logger?.LogError("{Prefix} Maximum retry count reached ({Count}). Giving up on channel {ChannelId}",
                                logPrefix, retryCount, WebSocketDetail.id);
                            break;
                        }
                        
                        TimeSpan delay = TimeSpan.FromMilliseconds(
                            initialRetryDelay.TotalMilliseconds * Math.Pow(2, retryCount - 1));
                            
                        _logger?.LogInformation("{Prefix} Retrying connection in {Delay}ms. Attempt {Count}/{MaxRetries}",
                            logPrefix, delay.TotalMilliseconds, retryCount, maxRetries);
                            
                        await Task.Delay(delay, cancellationToken);
                    }
                }
                finally
                {
                    _webSocket?.Dispose();
                    _webSocket = null;
                }
            }
        }

        private async Task ReceiveMessagesAsync(CancellationToken cancellationToken)
        {
            byte[] buffer = new byte[4096];
            StringBuilder messageBuilder = new StringBuilder();
            string logPrefix = $"[WebSocket:{_threadName}]";
            
            while (_webSocket.State == WebSocketState.Open && !cancellationToken.IsCancellationRequested && !ShouldExit)
            {
                WebSocketReceiveResult result = await _webSocket.ReceiveAsync(
                    new ArraySegment<byte>(buffer), cancellationToken);
                
                LastDataReceived = DateTime.UtcNow;
                
                if (result.MessageType == WebSocketMessageType.Close)
                {
                    _logger?.LogInformation("{Prefix} WebSocket close message received for channel {ChannelId}", logPrefix, WebSocketDetail.id);
                    await _webSocket.CloseAsync(
                        WebSocketCloseStatus.NormalClosure,
                        "Closing",
                        CancellationToken.None);
                    break;
                }
                
                string message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                messageBuilder.Append(message);
                
                if (result.EndOfMessage)
                {
                    string fullMessage = messageBuilder.ToString();
                    messageBuilder.Clear();
                    
                    // Process the message
                    try
                    {
                        _messageHandler(fullMessage, _threadName);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "{Prefix} Error processing WebSocket message for channel {ChannelId}", logPrefix, WebSocketDetail.id);
                    }
                }
            }
        }

        private async Task SendHeartbeatsAsync(CancellationToken cancellationToken)
        {
            string logPrefix = $"[WebSocket:{_threadName}]";
            try
            {
                while (_webSocket.State == WebSocketState.Open && !cancellationToken.IsCancellationRequested && !ShouldExit)
                {
                    // Send ping if it's time
                    if (DateTime.UtcNow - _lastPingTime >= _pingInterval)
                    {
                        _logger?.LogDebug("{Prefix} Sending heartbeat", logPrefix);
                        SendHeartbeat();
                    }
                    
                    await Task.Delay(1000, cancellationToken); // Check every second
                }
            }
            catch (OperationCanceledException)
            {
                // Normal cancellation
                _logger?.LogDebug("{Prefix} Heartbeat task canceled", logPrefix);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "{Prefix} Error in heartbeat task for channel {ChannelId}", logPrefix, WebSocketDetail.id);
            }
        }

        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposedValue)
            {
                if (disposing)
                {
                    StopWebSocketConnection();
                    _cancellationTokenSource?.Dispose();
                }
                
                _disposedValue = true;
            }
        }
    }
}
