using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Extensions.Logging;

namespace GCRealTime.Core
{
    public class TopicManager
    {
        private readonly ILogger _logger;

        public TopicManager(ILogger logger)
        {
            _logger = logger;
        }

        public List<string> BuildUserActivityTopics(DataTable userTable)
        {
            List<string> topics = new List<string>();
            foreach (DataRow row in userTable.Rows)
            {
                topics.Add($"v2.users.{row["id"]}.activity");
            }
            _logger?.LogInformation($"Created {topics.Count} user activity topics");
            return topics;
        }

        public List<string> BuildUserAdherenceTopics(DataTable userTable)
        {
            List<string> topics = new List<string>();
            foreach (DataRow row in userTable.Rows)
            {
                topics.Add($"v2.users.{row["id"]}.workforcemanagement.adherence");
            }
            _logger?.LogInformation($"Created {topics.Count} user adherence topics");
            return topics;
        }

        public List<string> BuildUserCallStatsTopics(DataTable userTable)
        {
            List<string> topics = new List<string>();
            foreach (DataRow row in userTable.Rows)
            {
                topics.Add($"v2.users.{row["id"]}.conversationsummary");
            }
            _logger?.LogInformation($"Created {topics.Count} user call stats topics");
            return topics;
        }

        public List<string> BuildUserCallDetailsTopics(DataTable userTable)
        {
            List<string> topics = new List<string>();
            foreach (DataRow row in userTable.Rows)
            {
                topics.Add($"v2.users.{row["id"]}.conversations");
            }
            _logger?.LogInformation($"Created {topics.Count} user call details topics");
            return topics;
        }

        public List<string> BuildQueueCallDetailsTopics(DataTable queueTable)
        {
            List<string> topics = new List<string>();
            foreach (DataRow row in queueTable.Rows)
            {
                topics.Add($"v2.routing.queues.{row["id"]}.conversations");
            }
            _logger?.LogInformation($"Created {topics.Count} queue call details topics");
            return topics;
        }

        public List<List<string>> ChunkTopics(List<string> topics, int maxTopicsPerChunk)
        {
            var result = new List<List<string>>();
            for (int i = 0; i < topics.Count; i += maxTopicsPerChunk)
            {
                result.Add(topics.Skip(i).Take(maxTopicsPerChunk).ToList());
            }
            _logger?.LogInformation($"Split {topics.Count} topics into {result.Count} chunks of max {maxTopicsPerChunk} topics each");
            return result;
        }
    }
}
