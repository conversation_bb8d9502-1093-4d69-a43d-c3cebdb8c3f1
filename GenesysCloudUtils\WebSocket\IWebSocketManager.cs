using System;
using System.Threading.Tasks;

namespace GenesysCloudUtils.WebSocket
{
    /// <summary>
    /// Interface for WebSocket management functionality
    /// </summary>
    public interface IWebSocketManager : IDisposable
    {
        /// <summary>
        /// Gets the channel ID associated with this WebSocket
        /// </summary>
        string ChannelId { get; }
        
        /// <summary>
        /// Gets a value indicating whether the WebSocket is currently connected
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// Gets or sets the WebSocket detail object
        /// </summary>
        WebSocketDetail WebSocketDetail { get; }
        
        /// <summary>
        /// Gets the last time data was received from the WebSocket
        /// </summary>
        DateTime LastDataReceived { get; }
        
        /// <summary>
        /// Gets or sets a value indicating whether the WebSocket should exit
        /// </summary>
        bool ShouldExit { get; set; }
        
        /// <summary>
        /// Starts the WebSocket connection
        /// </summary>
        void StartWebSocketConnection();
        
        /// <summary>
        /// Stops the WebSocket connection
        /// </summary>
        void StopWebSocketConnection();
        
        /// <summary>
        /// Sends data through the WebSocket
        /// </summary>
        /// <param name="data">The data to send</param>
        /// <returns>A task representing the send operation</returns>
        Task SendAsync(string data);
        
        /// <summary>
        /// Sends a heartbeat message through the WebSocket
        /// </summary>
        void SendHeartbeat();
    }
    

}
