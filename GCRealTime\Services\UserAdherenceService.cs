using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using GCRealTime.Core;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using GenesysCloudUtils;
using GenesysCloudUtils.WebSocket;
using StandardUtils; // Reference Utils class for datetime conversion

namespace GCRealTime.Services
{
    public class UserAdherenceService : BaseRealTimeService, IRealtimeService
    {
        // Use static counters to persist across instances/recoveries
        private static long _processedEventsCount = 0;
        private static DateTime _lastProcessedEventTime = DateTime.MinValue;
        private readonly object _statsLock = new object(); // Thread safety

        private DataTable _userDetails;
        private DataTable _adherenceData;
        private List<WebSocketDetail> _webSocketList = new List<WebSocketDetail>();
        private List<Task> _socketTasks = new List<Task>();
        private bool _writeUserAdherence = false;
        private readonly object _adherenceLock = new object();
        private bool _isRunning = false;
        private readonly ILogger _logger;
        private JsonUtils _jsonUtils;
        private int _restartAttempts = 0;
        private const int MAX_RESTART_ATTEMPTS = 3;
        private CancellationTokenSource _cts;

        public UserAdherenceService(ILogger logger, DBUtils.DBUtils dbAdapter) : base(logger, dbAdapter)
        {
            _logger = logger;
            _jsonUtils = new JsonUtils(logger);
        }

        public override void Initialize()
        {
            _logger?.LogInformation("[UserAdherence][Init] Beginning UserAdherenceService initialization");
            base.Initialize();

            // Log the current counter value on initialization
            _logger?.LogInformation("[UserAdherence][Init] Starting with existing event count: {EventCount}",
                _processedEventsCount);

            // Check if userDetails table exists - this is required
            if (!TableExists("userDetails"))
            {
                _logger?.LogError("[UserAdherence][Init] Required table 'userDetails' does not exist");
                throw new InvalidOperationException("Required table 'userDetails' does not exist");
            }

            _userDetails = GetUsers();

            if (_userDetails == null || _userDetails.Rows.Count == 0)
            {
                _logger?.LogWarning("[UserAdherence][Init] No active users found in userDetails table");
            }

            _adherenceData = CreateAdherenceTable();

            // Try to load existing data
            try
            {
                // Look for existing adherence data in the shared userRealTimeData table
                string query = "SELECT * FROM userRealTimeData WHERE adherenceState IS NOT NULL AND updated >= @cutoff";
                var cutoff = DateTime.UtcNow.AddHours(-12); // Only load recent data

                // Fix: Use a format supported by the database adapter
                // Instead of passing parameters, build the query with the date directly
                string formattedDate = cutoff.ToString("yyyy-MM-dd HH:mm:ss");
                string finalQuery = $"SELECT * FROM userRealTimeData WHERE adherenceState IS NOT NULL AND updated >= '{formattedDate}'";

                var existingData = DBAdapter.GetSQLTableData(finalQuery, "ExistingAdherenceData");
                if (existingData != null && existingData.Rows.Count > 0)
                {
                    _logger?.LogInformation("[UserAdherence][Init] Found {Count} existing adherence records", existingData.Rows.Count);
                    foreach (DataRow row in existingData.Rows)
                    {
                        _adherenceData.ImportRow(row);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "[UserAdherence][Init] Could not retrieve existing adherence data");
            }

            _logger?.LogInformation("[UserAdherence][Init] Adherence service initialized successfully");
        }

        public async Task InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("[UserAdherence][Init] Initializing UserAdherenceService asynchronously at {Time}", DateTime.UtcNow);
                await Task.Run(() => Initialize());
                _logger?.LogInformation("[UserAdherence][Init] Initialization complete at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][Init] Failed to initialize service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }

        public override void Start()
        {
            try
            {
                _logger?.LogInformation("[UserAdherence][Start] Starting UserAdherenceService");

                _webSocketList.Clear();
                WebSocketThreads.Clear();
                WebSocketManagers.Clear();
                _cts = new CancellationTokenSource();

                if (_userDetails == null || _userDetails.Rows.Count == 0)
                {
                    _logger?.LogWarning("[UserAdherence][Start] No users available - service will start but won't monitor any users");
                    _isRunning = true;
                    return; // Still allow the service to start, just without any users
                }

                // Split users into chunks if needed
                if (_userDetails.Rows.Count > MAX_TOPICS_PER_SUBSCRIPTION)
                {
                    var chunks = ChunkDataTable(_userDetails, MAX_TOPICS_PER_SUBSCRIPTION);
                    _logger?.LogInformation("[UserAdherence][Start] Splitting users into {Count} channels", chunks.Count);

                    int chunkIndex = 1;
                    foreach (var chunk in chunks)
                    {
                        try
                        {
                            string channelName = $"userAdherence-{chunkIndex}";
                            WebSocketDetail socket = CreateChannel(channelName);
                            if (socket == null)
                            {
                                _logger?.LogError("[UserAdherence][Start] Failed to create channel for chunk {ChunkIndex}", chunkIndex);
                                continue;
                            }

                            _webSocketList.Add(socket);

                            _logger?.LogInformation("[UserAdherence][Start] Creating subscriptions for chunk {ChunkIndex} with {Count} users",
                                chunkIndex, chunk.Rows.Count);

                            CreateUserAdherenceSubscriptionsForChunk(socket, chunk);

                            Thread thread = new Thread(() => CreateWebSocket(socket.connectUri, socket.id, channelName));
                            thread.Name = channelName;
                            thread.IsBackground = true;
                            thread.Start();
                            WebSocketThreads.Add(thread);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "[UserAdherence][Start] Error setting up channel for chunk {ChunkIndex}", chunkIndex);
                        }

                        chunkIndex++;
                    }
                }
                else
                {
                    const int maxChannelAttempts = 3;
                    WebSocketDetail socket = null;

                    for (int attempt = 1; attempt <= maxChannelAttempts; attempt++)
                    {
                        try
                        {
                            socket = CreateChannel("userAdherence");
                            if (socket == null)
                            {
                                if (attempt < maxChannelAttempts)
                                {
                                    _logger?.LogWarning("[UserAdherence][Start] Failed to create channel on attempt {Attempt}/{MaxAttempts}, retrying...",
                                        attempt, maxChannelAttempts);
                                    Thread.Sleep(2000 * attempt); // Increasing delay between attempts
                                    continue;
                                }

                                _logger?.LogError("[UserAdherence][Start] Failed to create channel after {MaxAttempts} attempts", maxChannelAttempts);
                                _isRunning = true; // Still mark as running, will try to recover during health checks
                                return; // Return without throwing to avoid crashing the app
                            }

                            break; // Successfully created the channel
                        }
                        catch (Exception ex)
                        {
                            if (attempt < maxChannelAttempts)
                            {
                                _logger?.LogWarning(ex, "[UserAdherence][Start] Error creating channel on attempt {Attempt}/{MaxAttempts}, retrying...",
                                    attempt, maxChannelAttempts);
                                Thread.Sleep(2000 * attempt);
                                continue;
                            }

                            _logger?.LogError(ex, "[UserAdherence][Start] Error setting up channel after {MaxAttempts} attempts", maxChannelAttempts);
                            _isRunning = true; // Still mark as running, will try to recover during health checks
                            return; // Return without throwing to avoid crashing the app
                        }
                    }

                    if (socket != null)
                    {
                        try
                        {
                            _webSocketList.Add(socket);

                            _logger?.LogInformation("[UserAdherence][Start] Creating subscriptions for {Count} users", _userDetails.Rows.Count);

                            CreateUserAdherenceSubscriptions(socket);

                            Thread thread = new Thread(() => CreateWebSocket(socket.connectUri, socket.id, "userAdherence"));
                            thread.Name = "userAdherence";
                            thread.IsBackground = true;
                            thread.Start();
                            WebSocketThreads.Add(thread);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "[UserAdherence][Start] Error setting up subscriptions");
                            // Don't throw - allow service to continue and retry during health checks
                        }
                    }
                }

                _isRunning = true;
                _logger?.LogInformation("[UserAdherence][Start] Adherence service started with {Count} WebSocket managers",
                    WebSocketManagers.Count);

                // Schedule periodic health checks to auto-recover
                Task.Run(async () =>
                {
                    while (_isRunning && !_cts.IsCancellationRequested)
                    {
                        try
                        {
                            // Wait 30 seconds between checks
                            await Task.Delay(30000, _cts.Token);

                            // Check if we need to recover
                            if (WebSocketManagers.Count == 0 || !WebSocketManagers.Any(m => m.IsConnected))
                            {
                                _logger?.LogWarning("[UserAdherence][Monitor] No active WebSocket connections, attempting recovery");
                                await RecoverFailedConnectionsAsync();
                            }
                        }
                        catch (TaskCanceledException)
                        {
                            // Normal during shutdown
                            break;
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "[UserAdherence][Monitor] Error in connection monitor");
                        }
                    }
                }, _cts.Token);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][Start] Unhandled exception in Start()");
                // Don't throw - mark as running but will attempt recovery in health checks
                _isRunning = true;
            }
        }

        public async Task StartAsync(CancellationToken token)
        {
            try
            {
                _logger?.LogInformation("[UserAdherence][Start] Starting UserAdherenceService asynchronously at {Time}", DateTime.UtcNow);
                await Task.Run(() => Start(), token);
                _isRunning = true;
                _logger?.LogInformation("[UserAdherence][Start] Started successfully at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][Start] Failed to start service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }

        public async Task StopAsync()
        {
            try
            {
                _logger?.LogInformation("[UserAdherence][Stop] Stopping UserAdherenceService asynchronously at {Time}", DateTime.UtcNow);
                _isRunning = false;
                _cts?.Cancel();

                await Task.WhenAll(_socketTasks);

                foreach (var manager in WebSocketManagers)
                {
                    try
                    {
                        manager.StopWebSocketConnection();
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[UserAdherence][Stop] Error stopping WebSocket manager");
                    }
                }

                WebSocketManagers.Clear();
                WebSocketThreads.Clear();
                _webSocketList.Clear();

                _logger?.LogInformation("[UserAdherence][Stop] Stopped successfully at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][Stop] Failed to stop service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }

        public async Task<bool> CheckHealthAsync()
        {
            try
            {
                _logger?.LogInformation("[UserAdherence][Health] Performing health check");

                if (!_isRunning)
                {
                    _logger?.LogWarning("[UserAdherence][Health] Service is not running");
                    return false;
                }

                bool hasActiveConnections = WebSocketManagers != null &&
                                           WebSocketManagers.Count > 0 &&
                                           WebSocketManagers.Any(m => m.IsConnected);

                if (!hasActiveConnections)
                {
                    _logger?.LogWarning("[UserAdherence][Health] No active WebSocket connections");

                    // Attempt restart by using the service's own recovery method
                    await RecoverFailedConnectionsAsync();

                    // Check if recovery was successful
                    hasActiveConnections = WebSocketManagers != null &&
                                         WebSocketManagers.Count > 0 &&
                                         WebSocketManagers.Any(m => m.IsConnected);

                    if (!hasActiveConnections)
                    {
                        _logger?.LogError("[UserAdherence][Health] Recovery failed - service remains unhealthy");
                        return false;
                    }

                    _logger?.LogInformation("[UserAdherence][Health] Recovery successful - service is now healthy");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][Health] Health check failed");
                return false;
            }
        }

        protected override void ReceiveData(string jsonString, string threadName)
        {
            try
            {
                using (_logger.BeginScope("Adherence/{ThreadName}", threadName))
                {
                    // Add a direct log entry to show what's being received
                    _logger?.LogDebug("[UserAdherence][Thread:{ThreadName}] Received data: {JsonPreview}",
                        threadName, jsonString.Length > 100 ? jsonString.Substring(0, 100) + "..." : jsonString);

                    // Always increment the event counter for any message received - with thread safety
                    lock (_statsLock)
                    {
                        _processedEventsCount++;
                        _lastProcessedEventTime = DateTime.UtcNow;

                        // Update base class properties immediately
                        EventsProcessed = _processedEventsCount;
                        LastActivity = _lastProcessedEventTime;
                    }

                    // Log an explicit event count message periodically (every 10 events)
                    if (_processedEventsCount % 10 == 0)
                    {
                        _logger?.LogInformation("[UserAdherence] Total events processed: {Count}, last activity: {LastActivity}",
                            _processedEventsCount, _lastProcessedEventTime);
                    }

                    if (jsonString.Contains("topicName") && jsonString.Contains("v2.users") &&
                        jsonString.Contains("workforcemanagement.adherence"))
                    {
                        ProcessAdherenceData(jsonString, threadName);
                    }
                    else if (jsonString.Contains("WebSocket Heartbeat") ||
                            (jsonString.Contains("\"topicName\": \"channel.metadata\"") &&
                             jsonString.Contains("\"message\": \"pong\"")))
                    {
                        _logger?.LogDebug("[UserAdherence][Thread:{ThreadName}] Received heartbeat", threadName);
                        // Reset error counters on successful heartbeat
                        TotalErrors = 0;
                    }

                    if (_writeUserAdherence && _adherenceData.Rows.Count > 0)
                    {
                        // Replace direct database call with thread-safe method, using our new wrapper
                        SafeWriteToDatabaseWithLogging(_adherenceData, "user adherence").Wait();
                        _writeUserAdherence = false;
                        _logger?.LogInformation("[UserAdherence][Thread:{ThreadName}][DB] Successfully wrote {Count} adherence records",
                            threadName, _adherenceData.Rows.Count);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][Thread:{ThreadName}] Error processing data", threadName);
            }
        }

        private async Task SafeWriteToDatabaseWithLogging(DataTable data, string dataDescription)
        {
            try
            {
                _logger?.LogInformation("[UserAdherence][DB] Writing {Count} {Description} records to database", 
                    data.Rows.Count, dataDescription);
                await base.SafeWriteToDatabase(data, dataDescription);
                _logger?.LogInformation("[UserAdherence][DB] Successfully wrote {Count} {Description} records", 
                    data.Rows.Count, dataDescription);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][DB] Error writing {Description} data to database", dataDescription);
                throw;
            }
        }

        private DataTable GetUsers()
        {
            return DBAdapter.GetSQLTableData("select id, name from userDetails where state = 'active'", "UserDetails");
        }

        private DataTable CreateAdherenceTable()
        {
            // Create table with only the columns needed for adherence data
            DataTable dtTemp = new DataTable("userRealTimeData");
            
            // Add columns based on userrealtimedata.sql schema
            dtTemp.Columns.Add("id", typeof(string));
            dtTemp.Columns.Add("name", typeof(string));
            dtTemp.Columns.Add("jabberId", typeof(string));
            dtTemp.Columns.Add("email", typeof(string));
            dtTemp.Columns.Add("state", typeof(string));
            dtTemp.Columns.Add("title", typeof(string));
            dtTemp.Columns.Add("username", typeof(string));
            dtTemp.Columns.Add("department", typeof(string));
            
            // Adherence service specific columns - only include what was in the legacy code
            dtTemp.Columns.Add("adherencestate", typeof(string));
            dtTemp.Columns.Add("adherencestarttime", typeof(DateTime)); // Renamed from adherenceChangeTime in the legacy code
            dtTemp.Columns.Add("impact", typeof(string));
            dtTemp.Columns.Add("scheduledactivitycategory", typeof(string));
            
            // These columns were not in the legacy code - comment them out
            // dtTemp.Columns.Add("activitycode", typeof(string));
            // dtTemp.Columns.Add("outofadherencetimestamp", typeof(DateTime));
            // dtTemp.Columns.Add("scheduledactivityname", typeof(string));
            // dtTemp.Columns.Add("scheduledactivitycode", typeof(string));
            // dtTemp.Columns.Add("actualactivityname", typeof(string));
            // dtTemp.Columns.Add("actualactivitycode", typeof(string));
            
            // Standard timestamp column
            dtTemp.Columns.Add("updated", typeof(DateTime));
            
            _logger?.LogDebug("[UserAdherence][Init] Creating adherence table with legacy columns: id, adherenceState, adherencestarttime, impact, scheduledactivitycategory");
            
            return dtTemp;
        }

        private void ProcessAdherenceData(string jsonString, string threadName)
        {
            try
            {
                // Parse JSON
                var adherenceData = JsonConvert.DeserializeObject<JObject>(jsonString);
                if (adherenceData == null || adherenceData["eventBody"] == null)
                {
                    _logger?.LogWarning("[UserAdherence][Thread:{ThreadName}] Received adherence data with null eventBody", threadName);
                    return;
                }

                // Extract user ID from topic
                string userId = adherenceData["topicName"]?.ToString()
                    .Replace("v2.users.", "")
                    .Replace(".workforcemanagement.adherence", "");

                if (string.IsNullOrEmpty(userId))
                {
                    _logger?.LogWarning("[UserAdherence][Thread:{ThreadName}] Could not extract user ID from topic", threadName);
                    return;
                }

                // Look up user name
                string userName = "Unknown User";
                if (_userDetails != null)
                {
                    var userRow = _userDetails.AsEnumerable()
                        .FirstOrDefault(r => r.Field<string>("id") == userId);
                    if (userRow != null)
                    {
                        userName = userRow.Field<string>("name") ?? "Unknown User";
                    }
                }

                // Extract adherence data
                var eventBody = adherenceData["eventBody"];
                string adherenceState = eventBody["adherenceState"]?.ToString() ?? "Unknown";
                string impact = eventBody["impact"]?.ToString() ?? "Unknown";
                
                // This field was not in the legacy code - comment it out
                // string activityCode = eventBody["scheduledActivityCode"]?["id"]?.ToString() ?? "";

                DateTime? adherenceChangeTime = null;
                if (eventBody["adherenceChangeTime"] != null)
                {
                    if (DateTime.TryParse(eventBody["adherenceChangeTime"].ToString(),
                        out DateTime parsedTime))
                    {
                        adherenceChangeTime = parsedTime;
                    }
                }

                // Get only the scheduled activity category (the only field used in legacy version)
                string scheduledActivityCategory = eventBody["scheduledActivityCategory"]?.ToString() ?? "";
                
                // These fields weren't in the legacy code - comment them out
                // string scheduledActivityCode = eventBody["scheduledActivityCode"]?["id"]?.ToString() ?? "";
                // string scheduledActivityName = eventBody["scheduledActivity"]?["name"]?.ToString() ?? "";
                // string actualActivityName = eventBody["actualActivity"]?["name"]?.ToString() ?? "";
                // string actualActivityCode = eventBody["actualActivity"]?["code"]?.ToString() ?? "";

                // Save to data table - only set the fields we had in the legacy schema
                lock (_adherenceLock)
                {
                    DataRow row = _adherenceData.NewRow();
                    row["id"] = userId;
                    row["updated"] = DateTime.UtcNow;
                    row["adherenceState"] = adherenceState;
                    row["impact"] = impact;
                    
                    // These fields weren't in the legacy code - comment them out
                    // row["activityCode"] = activityCode;
                    // row["scheduledactivitycode"] = scheduledActivityCode;
                    // row["scheduledactivityname"] = scheduledActivityName;
                    // row["actualactivityname"] = actualActivityName;
                    // row["actualactivitycode"] = actualActivityCode;
                    
                    row["scheduledactivitycategory"] = scheduledActivityCategory;

                    // Replace adherenceChangeTime with adherencestarttime to match legacy naming
                    if (adherenceChangeTime.HasValue)
                    {
                        row["adherencestarttime"] = adherenceChangeTime.Value;
                    }
                    else
                    {
                        row["adherencestarttime"] = DBNull.Value;
                    }

                    _adherenceData.Rows.Add(row);
                    _writeUserAdherence = true;
                    
                    _logger?.LogDebug("[UserAdherence][Thread:{ThreadName}] Added/updated adherence data for user {UserId} with state {State}", 
                        threadName, userId, adherenceState);
                }

                // Track entity activity
                TrackEntityActivity(userId, userName, "User");

                _logger?.LogDebug("[UserAdherence][Thread:{ThreadName}] Processed adherence data for user {UserId}: {State}",
                    threadName, userId, adherenceState);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][Thread:{ThreadName}] Error processing adherence data", threadName);
            }
        }

        private void CreateUserAdherenceSubscriptions(WebSocketDetail webSocket)
        {
            if (webSocket == null)
            {
                _logger?.LogError("[UserAdherence] Cannot create subscriptions - WebSocket is null");
                return;
            }

            // Implement directly instead of calling base method
            try
            {
                if (_userDetails == null || _userDetails.Rows.Count == 0)
                {
                    _logger?.LogWarning("[UserAdherence] User table is empty - no subscriptions to create");
                    return;
                }

                _logger?.LogInformation("[UserAdherence] Creating subscriptions for {Count} users on channel {ChannelId}",
                    _userDetails.Rows.Count, webSocket.id);

                // Use CombinedSubscriptionHelper directly
                GenesysCloudUtils.WebSocket.CombinedSubscriptionHelper
                    .CreateUserSubscriptionsAsync(
                        _logger,
                        _jsonUtils,
                        _userDetails,
                        webSocket,
                        "v2.users.",
                        ".workforcemanagement.adherence")
                    .GetAwaiter()
                    .GetResult();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence] Error creating subscriptions");
                throw;
            }
        }

        private void CreateUserAdherenceSubscriptionsForChunk(WebSocketDetail webSocket, DataTable userChunk)
        {
            try
            {
                _logger?.LogInformation("[UserAdherence][Channel:{ChannelId}] Creating adherence subscriptions for {Count} users",
                    webSocket.id, userChunk.Rows.Count);

                // Use the helper to create combined subscriptions
                GenesysCloudUtils.WebSocket.CombinedSubscriptionHelper
                    .CreateUserSubscriptionsAsync(
                        _logger,
                        _jsonUtils,
                        userChunk,
                        webSocket,
                        "v2.users.",
                        ".workforcemanagement.adherence")
                    .GetAwaiter().GetResult();

                _logger?.LogInformation("[UserAdherence][Channel:{ChannelId}] Successfully created adherence subscriptions", webSocket.id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][Channel:{ChannelId}] Failed to create adherence subscriptions", webSocket.id);
                throw;
            }
        }

        private void TrackEntityActivity(string entityId, string entityName, string entityType)
        {
            try
            {
                // Increment the processed events counter
                Interlocked.Increment(ref _processedEventsCount);

                // Update the last processed time
                _lastProcessedEventTime = DateTime.UtcNow;

                _logger?.LogDebug("[UserAdherence] Recorded activity for {EntityType} {EntityName} ({EntityId})",
                    entityType, entityName, entityId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence] Error tracking entity activity for {EntityType} {EntityName}", entityType, entityName);
            }
        }

        // Override the RecoverFailedConnectionsAsync method to implement service-specific recovery
        protected override async Task RecoverFailedConnectionsAsync()
        {
            try
            {
                _logger?.LogInformation("[UserAdherence][Recover] Attempting to recover failed connections");
                await base.RecoverFailedConnectionsAsync();

                // Clear the web socket list specific to this service
                _webSocketList.Clear();

                // Create new connections based on user chunks
                if (_userDetails != null && _userDetails.Rows.Count > 0)
                {
                    if (_userDetails.Rows.Count <= MAX_TOPICS_PER_SUBSCRIPTION)
                    {
                        WebSocketDetail socket = CreateChannel("userAdherence-recovery");
                        if (socket != null)
                        {
                            _webSocketList.Add(socket);

                            CreateUserAdherenceSubscriptions(socket);

                            Thread thread = new Thread(() => CreateWebSocket(socket.connectUri, socket.id, "userAdherence-recovery"));
                            thread.Name = "userAdherence-recovery";
                            thread.IsBackground = true;
                            thread.Start();
                            WebSocketThreads.Add(thread);

                            _logger?.LogInformation("[UserAdherence][Recover] Created recovery WebSocket connection");
                        }
                    }
                    else
                    {
                        // If we have multiple chunks, recover those too
                        var chunks = ChunkDataTable(_userDetails, MAX_TOPICS_PER_SUBSCRIPTION);
                        int chunkIndex = 1;

                        foreach (var chunk in chunks)
                        {
                            string channelName = $"userAdherence-recovery-{chunkIndex}";
                            WebSocketDetail socket = CreateChannel(channelName);

                            if (socket != null)
                            {
                                _webSocketList.Add(socket);

                                CreateUserAdherenceSubscriptionsForChunk(socket, chunk);

                                Thread thread = new Thread(() => CreateWebSocket(socket.connectUri, socket.id, channelName));
                                thread.Name = channelName;
                                thread.IsBackground = true;
                                thread.Start();
                                WebSocketThreads.Add(thread);
                            }

                            chunkIndex++;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][Recover] Error during connection recovery");
            }
        }

        // Override ProcessDatabaseWrites to use service-specific log prefix - enhancing existing override
        protected override void ProcessDatabaseWrites()
        {
            try
            {
                _logger?.LogDebug("[UserAdherence][DB] Processing database writes");
                base.ProcessDatabaseWrites();
                
                // Existing user adherence specific logic
                if (_writeUserAdherence && _adherenceData.Rows.Count > 0)
                {
                    SafeWriteToDatabaseWithLogging(_adherenceData, "user adherence data").Wait();
                    _writeUserAdherence = false;
                    _logger?.LogInformation("[UserAdherence][DB] Successfully wrote {Count} user adherence records to database",
                        _adherenceData.Rows.Count);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserAdherence][DB] Error processing database writes");
            }
        }

        // Add a method to expose the active connection count for diagnostics
        public int GetActiveConnectionCount()
        {
            return WebSocketManagers?.Count(m => m.IsConnected) ?? 0;
        }

        // Override to ensure metrics are reported consistently with other services
        public override Dictionary<string, object> GetServiceMetrics()
        {
            lock (_statsLock)
            {
                // Update the base class properties with our local counters
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }

            // Get base metrics (which will now include our updated values)
            var metrics = base.GetServiceMetrics();

            // Directly insert our values to be extra safe
            metrics["ProcessedEvents"] = _processedEventsCount;
            metrics["EventsProcessed"] = _processedEventsCount;
            metrics["LastEventTime"] = _lastProcessedEventTime;
            metrics["TotalWebSockets"] = WebSocketManagers?.Count ?? 0;
            metrics["ActiveWebSockets"] = GetActiveConnectionCount();
            metrics["ChannelsCreated"] = _webSocketList?.Count ?? 0;

            return metrics;
        }

        // Ensure FlushMetrics follows the same pattern as other services
        public override void FlushMetrics()
        {
            lock (_statsLock)
            {
                // Update the base class properties with our local counters
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }

            // Call base method second to avoid our values getting overwritten
            base.FlushMetrics();

            // Add explicit logging to show what metrics are being reported
            _logger?.LogInformation("[UserAdherence][Metrics] Current metrics snapshot: Events={EventCount}, LastActivity={LastActivity}",
                _processedEventsCount, _lastProcessedEventTime.ToString("yyyy-MM-dd HH:mm:ss"));
        }

        // Thread-safe method to increment event counters
        private void IncrementEventsCount()
        {
            lock (_statsLock)
            {
                // Increment our counter directly
                _processedEventsCount++;
                _lastProcessedEventTime = DateTime.UtcNow;

                // Update base class properties
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }

            // Call the base implementation
            IncrementEventsProcessed();
        }

        protected override void CreateWebSocket(string socketAddress, string socketChannel, string threadName)
        {
            var detail = new WebSocketDetail
            {
                connectUri = socketAddress,
                id = socketChannel,
                ReportName = threadName,
                Created = DateTime.UtcNow,
                Expires = DateTime.UtcNow.AddHours(1)
            };

            var wsManager = WebSocketManagerFactory.CreateWebSocketManager(
                _logger, detail, threadName, ReceiveData, _jsonUtils);

            WebSocketManagers.Add(wsManager);
            wsManager.StartWebSocketConnection();

            _logger?.LogInformation("[UserAdherence][WebSocket:{ChannelId}][Thread:{ThreadName}] Created WebSocket connection",
                socketChannel, threadName);
        }
    }
}