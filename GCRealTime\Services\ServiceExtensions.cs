using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;

namespace GCRealTime.Services
{
    public static class ServiceExtensions
    {
        /// <summary>
        /// Registers all the provided realtime service types with the service collection
        /// </summary>
        public static IServiceCollection RegisterRealtimeServices(this IServiceCollection services, IEnumerable<Type> serviceTypes)
        {
            foreach (var serviceType in serviceTypes)
            {
                // Register the service as both its concrete type and as IRealtimeService
                services.AddSingleton(serviceType);
                services.AddSingleton(typeof(IRealtimeService), 
                    provider => provider.GetRequiredService(serviceType));
            }
            
            return services;
        }
    }
}
