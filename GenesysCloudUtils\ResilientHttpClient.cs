using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.IO;
using System.Net.Sockets;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Retry;

namespace GenesysCloudUtils
{
    /// <summary>
    /// HTTP client that handles retries and resilience patterns
    /// </summary>
    public class ResilientHttpClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger _logger;
        private readonly AsyncRetryPolicy<HttpResponseMessage> _retryPolicy;
        private readonly int _maxRetries;
        private readonly int _retryDelayMilliseconds;

        /// <summary>
        /// Creates a new resilient HTTP client
        /// </summary>
        /// <param name="httpClient">The HttpClient to use for requests</param>
        /// <param name="logger">Logger for tracking retry attempts</param>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <param name="retryDelayMilliseconds">Base delay between retries in milliseconds</param>
        public ResilientHttpClient(HttpClient httpClient, ILogger logger, int maxRetries = 3, int retryDelayMilliseconds = 1000)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger;
            _maxRetries = maxRetries;
            _retryDelayMilliseconds = retryDelayMilliseconds;

            // Create a policy that will handle transient HTTP errors
            _retryPolicy = Policy
                .Handle<HttpRequestException>()
                .OrResult<HttpResponseMessage>(r => 
                    r.StatusCode == System.Net.HttpStatusCode.RequestTimeout ||
                    r.StatusCode == System.Net.HttpStatusCode.ServiceUnavailable ||
                    r.StatusCode == System.Net.HttpStatusCode.GatewayTimeout ||
                    r.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
                .WaitAndRetryAsync(
                    _maxRetries,
                    retryAttempt => TimeSpan.FromMilliseconds(_retryDelayMilliseconds * Math.Pow(2, retryAttempt - 1)),
                    (result, timeSpan, retryCount, context) =>
                    {
                        _logger?.LogWarning(
                            "Request failed with {StatusCode}. Waiting {RetryTimeSpan} before retry attempt {RetryCount}",
                            result.Result?.StatusCode.ToString() ?? "Unknown",
                            timeSpan,
                            retryCount);
                    }
                );
        }

        /// <summary>
        /// Executes an HTTP request with automatic retries for transient failures
        /// </summary>
        /// <param name="requestFunc">Function that performs the HTTP request</param>
        /// <returns>The HTTP response message</returns>
        public async Task<HttpResponseMessage> ExecuteWithRetriesAsync(Func<Task<HttpResponseMessage>> requestFunc)
        {
            return await _retryPolicy.ExecuteAsync(requestFunc);
        }

        public async Task<HttpResponseMessage> SendWithRetryAsync(HttpRequestMessage request, CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            Exception lastException = null;
            
            for (int retryAttempt = 0; retryAttempt <= _maxRetries; retryAttempt++)
            {
                try
                {
                    if (retryAttempt > 0)
                    {
                        // Clone the request for retry since HttpRequestMessage can only be sent once
                        request = await CloneHttpRequestMessageAsync(request);
                        
                        // Calculate delay with exponential backoff (2^retryAttempt * initialDelay)
                        int delayMs = _retryDelayMilliseconds * (int)Math.Pow(2, retryAttempt - 1);
                        _logger?.LogInformation($"Retry attempt {retryAttempt}/{_maxRetries} after {delayMs}ms delay");
                        await Task.Delay(delayMs, cancellationToken);
                    }

                    var response = await _httpClient.SendAsync(request, cancellationToken);
                    
                    // Consider also retrying on certain status codes like 429 (Too Many Requests), 502, 503, 504
                    if ((int)response.StatusCode >= 500 || response.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        lastException = new HttpRequestException($"Server returned status code {response.StatusCode}");
                        _logger?.LogWarning($"Request failed with status code {response.StatusCode}. Will retry ({retryAttempt}/{_maxRetries})");
                        continue;
                    }

                    return response;
                }
                catch (HttpRequestException ex) when (ShouldRetry(ex))
                {
                    lastException = ex;
                    _logger?.LogWarning(ex, $"HTTP request failed. Will retry ({retryAttempt}/{_maxRetries})");
                }
                catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
                {
                    lastException = ex;
                    _logger?.LogWarning(ex, $"Request timed out. Will retry ({retryAttempt}/{_maxRetries})");
                }
                catch (Exception ex)
                {
                    // For other exceptions, we'll just log and rethrow
                    _logger?.LogError(ex, "Unhandled exception during HTTP request");
                    throw;
                }
            }

            // If we get here, we've exhausted all retries
            _logger?.LogError(lastException, $"Request failed after {_maxRetries} retry attempts");
            throw lastException;
        }
        
        private bool ShouldRetry(HttpRequestException ex)
        {
            // Consider retrying on connection errors, which are likely to be temporary
            if (ex.InnerException is IOException || 
                ex.InnerException is WebException || 
                ex.InnerException is SocketException)
            {
                return true;
            }
            
            return false;
        }

        private async Task<HttpRequestMessage> CloneHttpRequestMessageAsync(HttpRequestMessage request)
        {
            var clone = new HttpRequestMessage(request.Method, request.RequestUri);

            // Copy the headers
            foreach (var header in request.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            // Copy the properties
            foreach (var prop in request.Properties)
            {
                clone.Properties.Add(prop);
            }

            // Copy the content if present
            if (request.Content != null)
            {
                // We need to copy the content
                var contentBytes = await request.Content.ReadAsByteArrayAsync();
                clone.Content = new ByteArrayContent(contentBytes);

                // Copy the content headers
                if (request.Content.Headers != null)
                {
                    foreach (var header in request.Content.Headers)
                    {
                        clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }
                }
            }

            return clone;
        }
    }
}
