using System;
using Microsoft.Extensions.Logging;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;

namespace GCRealTime.Core
{
    /// <summary>
    /// Service for handling errors and exceptions
    /// </summary>
    public class ErrorHandlingService
    {
        private readonly ILogger _logger;
        private readonly TelemetryClient _telemetry;
        private int _consecutiveErrorCount;
        private DateTime _lastErrorTime = DateTime.MinValue;
        private readonly TimeSpan _errorThrottleWindow = TimeSpan.FromMinutes(10);
        private readonly int _maxConsecutiveErrors = 10;

        public ErrorHandlingService(ILogger logger, TelemetryClient telemetry = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _telemetry = telemetry;
        }

        public bool HandleError(Exception ex, string component, string operation, bool isCritical = false)
        {
            var now = DateTime.UtcNow;
            
            // Reset error count if outside throttle window
            if (now - _lastErrorTime > _errorThrottleWindow)
            {
                _consecutiveErrorCount = 0;
            }
            
            _lastErrorTime = now;
            _consecutiveErrorCount++;
            
            // Log the error
            _logger?.LogError(ex, 
                "{component}: Error during {operation}. Error {count} of {max}.",
                component, operation, _consecutiveErrorCount, _maxConsecutiveErrors);
            
            // Track in AppInsights if available
            if (_telemetry != null)
            {
                var telemetryEx = new ExceptionTelemetry(ex)
                {
                    SeverityLevel = isCritical ? SeverityLevel.Critical : SeverityLevel.Error,
                };
                
                telemetryEx.Properties.Add("Component", component);
                telemetryEx.Properties.Add("Operation", operation);
                telemetryEx.Properties.Add("ConsecutiveCount", _consecutiveErrorCount.ToString());
                
                _telemetry.TrackException(telemetryEx);
            }
            
            // Return true if we should abort/retry due to too many errors
            return isCritical || _consecutiveErrorCount >= _maxConsecutiveErrors;
        }

        public void ResetErrorCount()
        {
            _consecutiveErrorCount = 0;
        }
    }
}
