using System.Threading;
using System.Threading.Tasks;

namespace GCRealTime.Services
{
    /// <summary>
    /// Service-layer interface for real-time services.
    /// Extends the Core IRealtimeService interface for compatibility.
    /// </summary>
    public interface IRealtimeService : GCRealTime.Core.IRealtimeService
    {
        // No additional members needed, just inherits from Core.IRealtimeService

        // Add this method to the interface
        void FlushMetrics();
    }
}
