﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RuntimeIdentifiers>win-x64;linux-x64;linux-musl-x64</RuntimeIdentifiers>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RestorePackagesWithLockFile>false</RestorePackagesWithLockFile>
    <RestoreLockedMode Condition="'$(ContinuousIntegrationBuild)' == 'true'">true</RestoreLockedMode>
    <AnalysisLevel>latest</AnalysisLevel>
    <NoWarn>CS8632</NoWarn>
    <!--
    CS8632  The annotation for nullable reference types should only be used in code within a '#nullable' annotations context
    -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.21.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="7.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.WebSockets.Client" Version="4.3.2" />
  </ItemGroup>

  <ItemGroup>
    <!-- Break circular dependencies by specifying the exact projects needed -->
    <ProjectReference Include="..\DBUtils\DBUtils.csproj" />
    <ProjectReference Include="..\StandardUtils\StandardUtils.csproj" />
    <ProjectReference Include="..\GenesysCloudUtils\GenesysCloudUtils.csproj" />
  </ItemGroup>

  <!-- Add this property to fix circular dependency issues -->
  <PropertyGroup>
    <DisableImplicitFrameworkReferences>false</DisableImplicitFrameworkReferences>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>

</Project>
