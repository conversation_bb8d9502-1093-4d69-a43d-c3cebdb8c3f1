using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using GCRealTime.Core;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RealCN = RealUserPushConversations;
using GenesysCloudUtils;
using GenesysCloudUtils.WebSocket;
using Newtonsoft.Json.Linq;

namespace GCRealTime.Services
{
    public class UserCallDetailsService : BaseRealTimeService, IRealtimeService
    {
        // Use static counters to persist across instances/recoveries
        private static long _processedEventsCount = 0;
        private static DateTime _lastProcessedEventTime = DateTime.MinValue;
        private readonly object _statsLock = new object(); // Thread safety

        private DataTable _userDetails = new DataTable();
        private DataTable _callDetailsData = new DataTable();
        private List<WebSocketDetail> _webSocketList = new List<WebSocketDetail>();
        private List<Task> _socketTasks = new List<Task>();
        private CancellationTokenSource _cts;
        private bool _writeCallData = false;
        private readonly JsonUtils _jsonUtils;
        private bool _isRunning = false;
        private readonly ILogger _logger;

        public UserCallDetailsService(ILogger logger, DBUtils.DBUtils dbAdapter) : base(logger, dbAdapter)
        {
            _logger = logger;
            _jsonUtils = new JsonUtils(logger);
        }

        public override void Initialize()
        {
            _logger?.LogInformation("[UserCallDetails][Init] Beginning UserCallDetailsService initialization");
            base.Initialize();

            // Log the current counter value on initialization
            _logger?.LogInformation("[UserCallDetails][Init] Starting with existing event count: {EventCount}",
                _processedEventsCount);

            // Check if required tables exist
            if (!TableExists("userDetails"))
            {
                _logger?.LogError("[UserCallDetails][Init] Required table 'userDetails' does not exist");
                throw new InvalidOperationException("Required table 'userDetails' does not exist");
            }

            if (!TableExists("userRealTimeData"))
            {
                _logger?.LogError("[UserCallDetails][Init] Required table 'userRealTimeData' does not exist. Please run the install process to set up required tables.");
                throw new InvalidOperationException("Required table 'userRealTimeData' does not exist. Please run the install process to set up required tables.");
            }

            // Get the active users
            _userDetails = GetUsers();

            if (_userDetails == null || _userDetails.Rows.Count == 0)
            {
                _logger?.LogWarning("[UserCallDetails][Init] No active users found in userDetails table");
            }

            // Create the call details data table that works with the shared schema
            _callDetailsData = CreateCallDetailsTable();

            // Try to load existing data from the shared table - without using a cutoff time
            try
            {
                // Fix: Check first if the conversationid column exists in the userRealTimeData table
                if (ColumnExistsInTable("userRealTimeData", "conversationid"))
                {
                    // Load all conversation data from userRealTimeData - tables are designed to be truncated in full
                    string query = "SELECT * FROM userRealTimeData WHERE conversationid IS NOT NULL";
                    
                    var existingData = DBAdapter.GetSQLTableData(query, "ExistingCallData");
                    if (existingData != null && existingData.Rows.Count > 0)
                    {
                        _logger?.LogInformation("[UserCallDetails][Init] Found {Count} existing call records", existingData.Rows.Count);
                        foreach (DataRow row in existingData.Rows)
                        {
                            _callDetailsData.ImportRow(row);
                        }
                    }
                }
                else
                {
                    _logger?.LogWarning("[UserCallDetails][Init] Column 'conversationid' does not exist in 'userRealTimeData' table. " +
                        "Table schema may need to be updated.");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "[UserCallDetails][Init] Could not retrieve existing call data");
            }

            _logger?.LogInformation("[UserCallDetails][Init] Call details service initialized successfully");
        }

        // Add a helper method to check if a column exists in a table
        private bool ColumnExistsInTable(string tableName, string columnName)
        {
            try
            {
                string query = DBAdapter.DBType switch
                {
                    CSG.Adapter.Configuration.DatabaseType.MSSQL =>
                        $"SELECT COUNT(1) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{tableName}' AND COLUMN_NAME = '{columnName}'",
                    CSG.Adapter.Configuration.DatabaseType.MySQL =>
                        $"SELECT COUNT(1) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{tableName}' AND COLUMN_NAME = '{columnName}'",
                    CSG.Adapter.Configuration.DatabaseType.PostgreSQL =>
                        $"SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = '{tableName.ToLower()}' AND column_name = '{columnName.ToLower()}') as exists",
                    _ => throw new NotSupportedException($"Unsupported database type: {DBAdapter.DBType}")
                };

                object result = DBAdapter.ExecuteScalar(query);
                return Convert.ToBoolean(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallDetails][DB] Error checking if column {ColumnName} exists in table {TableName}", columnName, tableName);
                return false;
            }
        }

        public override void Start()
        {
            _logger?.LogInformation("[UserCallDetails][Start] Starting UserCallDetailsService at {Time}", DateTime.UtcNow);
            _cts = new CancellationTokenSource();
            _socketTasks = new List<Task>();
            _webSocketList.Clear();
            WebSocketManagers.Clear();

            // Add null checks before using _userDetails
            if (_userDetails == null)
            {
                _logger?.LogError("[UserCallDetails][Start] UserDetails is null - cannot start service");
                throw new InvalidOperationException("UserDetails is null - cannot start service");
            }

            if (_userDetails.Rows.Count > MAX_TOPICS_PER_SUBSCRIPTION)
            {
                var chunks = ChunkDataTable(_userDetails, MAX_TOPICS_PER_SUBSCRIPTION);
                _logger?.LogInformation("[UserCallDetails][Start] Splitting user call details into {ChannelCount} channels due to {UserCount} total users", chunks.Count, _userDetails.Rows.Count);
                int chunkIndex = 1;
                foreach (var chunk in chunks)
                {
                    string channelName = $"userCallDetails-{chunkIndex}";
                    // Add null check for the socket return
                    WebSocketDetail socket = CreateChannel(channelName);
                    if (socket == null)
                    {
                        _logger?.LogError("[UserCallDetails][Start] Failed to create channel for chunk {ChunkIndex}", chunkIndex);
                        Interlocked.Increment(ref _failedChannelAttempts);
                        CheckFailureThreshold();
                        continue; // Skip to next chunk
                    }
                    
                    _webSocketList.Add(socket);
                    var task = Task.Factory.StartNew(async () =>
                    {
                        try
                        {
                            _logger?.LogInformation("[UserCallDetails][Channel:{Channel}] Creating subscriptions for {ChunkSize} users", socket.id, chunk.Rows.Count);
                            await CreateUserCallDetailsSubscriptionsForChunk(socket, chunk);
                            _logger?.LogInformation("[UserCallDetails][Channel:{Channel}] Starting WebSocket", socket.id);
                            CreateWebSocket(socket.connectUri, socket.id, channelName);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "[UserCallDetails][Start] Error in task for channel {ChannelId}", socket.id);
                            // Don't throw from background task
                        }
                    }, _cts.Token, TaskCreationOptions.LongRunning, TaskScheduler.Default);
                    _socketTasks.Add(task);
                    chunkIndex++;
                }
            }
            else
            {
                string channelName = "userCallDetails";
                // Add null check for the socket return
                WebSocketDetail socket = CreateChannel(channelName);
                if (socket == null)
                {
                    _logger?.LogError("[UserCallDetails][Start] Failed to create main channel");
                    Interlocked.Increment(ref _failedChannelAttempts);
                    CheckFailureThreshold();
                    return; // Exit if we can't create the main channel
                }
                
                _webSocketList.Add(socket);
                var task = Task.Factory.StartNew(async () =>
                {
                    try
                    {
                        _logger?.LogInformation("[UserCallDetails][Channel:{Channel}] Creating subscriptions", socket.id);
                        await CreateUserCallDetailsSubscriptions(socket);
                        _logger?.LogInformation("[UserCallDetails][Channel:{Channel}] Starting WebSocket", socket.id);
                        CreateWebSocket(socket.connectUri, socket.id, channelName);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[UserCallDetails][Start] Error in task for main channel");
                        // Don't throw from background task
                    }
                }, _cts.Token, TaskCreationOptions.LongRunning, TaskScheduler.Default);
                _socketTasks.Add(task);
            }
            _isRunning = true;
            _logger?.LogInformation("[UserCallDetails][Start] Started successfully at {Time}", DateTime.UtcNow);
        }

        public async Task StopAsync()
        {
            try
            {
                _logger?.LogInformation("[UserCallDetails][Stop] Stopping UserCallDetailsService asynchronously at {Time}", DateTime.UtcNow);
                _cts?.Cancel();
                await Task.WhenAll(_socketTasks);
                _logger?.LogInformation("[UserCallDetails][Stop] Stopped successfully at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallDetails][Stop] Failed to stop service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }

        public async Task<bool> CheckHealthAsync()
        {
            try 
            {
                _logger?.LogInformation("[UserCallDetails][Health] Performing health check");
                
                if (!_isRunning)
                {
                    _logger?.LogWarning("[UserCallDetails][Health] Service is not running");
                    return false;
                }
                
                bool hasActiveConnections = WebSocketManagers != null && 
                                           WebSocketManagers.Count > 0 &&
                                           WebSocketManagers.Any(m => m.IsConnected);
                                           
                if (!hasActiveConnections)
                {
                    _logger?.LogWarning("[UserCallDetails][Health] No active WebSocket connections");
                    
                    // Create new connections
                    string channelName = "userCallDetails-recovery";
                    WebSocketDetail socket = CreateChannel(channelName);
                    if (socket != null)
                    {
                        _webSocketList.Add(socket);
                        
                        // Create subscriptions
                        await CreateUserCallDetailsSubscriptions(socket);
                        
                        // Start WebSocket
                        CreateWebSocket(socket.connectUri, socket.id, channelName);
                        
                        // Wait for connection to establish
                        await Task.Delay(2000);
                        
                        // Check if recovery was successful
                        hasActiveConnections = WebSocketManagers != null && 
                                              WebSocketManagers.Count > 0 &&
                                              WebSocketManagers.Any(m => m.IsConnected);
                                              
                        if (!hasActiveConnections)
                        {
                            _logger?.LogError("[UserCallDetails][Health] Recovery failed - service remains unhealthy");
                            return false;
                        }
                        
                        _logger?.LogInformation("[UserCallDetails][Health] Recovery successful - service is now healthy");
                    }
                    else
                    {
                        _logger?.LogError("[UserCallDetails][Health] Could not create recovery channel");
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallDetails][Health] Health check failed");
                return false;
            }
        }

        public async Task CreateUserCallDetailsSubscriptions(WebSocketDetail webSocket)
        {
            if (webSocket == null)
            {
                _logger?.LogError("[UserCallDetails] Cannot create call details subscriptions - WebSocket is null");
                throw new ArgumentNullException(nameof(webSocket), "WebSocket cannot be null");
            }

            if (_userDetails == null)
            {
                _logger?.LogError("[UserCallDetails] Cannot create call details subscriptions - UserDetails is null");
                throw new InvalidOperationException("UserDetails is null");
            }

            await CreateUserCallDetailsSubscriptionsForChunk(webSocket, _userDetails);
        }

        private async Task CreateUserCallDetailsSubscriptionsForChunk(WebSocketDetail webSocket, DataTable userChunk)
        {
            if (webSocket == null)
            {
                _logger?.LogError("[UserCallDetails] Cannot create call details subscriptions for chunk - WebSocket is null");
                throw new ArgumentNullException(nameof(webSocket), "WebSocket cannot be null");
            }

            if (userChunk == null)
            {
                _logger?.LogError("[UserCallDetails] Cannot create call details subscriptions - UserChunk is null");
                throw new ArgumentNullException(nameof(userChunk), "User chunk cannot be null");
            }

            if (_jsonUtils == null)
            {
                _logger?.LogError("[UserCallDetails] Cannot create call details subscriptions - JsonUtils is null");
                throw new InvalidOperationException("JsonUtils is null");
            }

            _logger?.LogInformation("[UserCallDetails][Channel:{ChannelId}] Creating call details subscriptions for {Count} users", 
                webSocket.id, userChunk.Rows.Count);
            
            try
            {
                // Add more detailed logging
                StringBuilder topicList = new StringBuilder();
                int maxTopicsToLog = Math.Min(5, userChunk.Rows.Count); // Log first 5 topics at most
                
                for (int i = 0; maxTopicsToLog > i; i++)
                {
                    string userId = userChunk.Rows[i]["id"]?.ToString();
                    if (!string.IsNullOrEmpty(userId))
                    {
                        topicList.AppendLine($"v2.users.{userId}.conversations");
                    }
                }
                
                if (userChunk.Rows.Count > maxTopicsToLog)
                {
                    topicList.AppendLine($"... and {userChunk.Rows.Count - maxTopicsToLog} more");
                }
                
                _logger?.LogDebug("[UserCallDetails] Subscribing to topics: {TopicList}", topicList.ToString());
                
                // Call the helper method with proper exception handling
                await GenesysCloudUtils.WebSocket.CombinedSubscriptionHelper
                    .CreateUserSubscriptionsAsync(
                        _logger,
                        _jsonUtils,
                        userChunk,
                        webSocket,
                        "v2.users.",
                        ".conversations",
                        "id");
                        
                _logger?.LogInformation("[UserCallDetails][Channel:{ChannelId}] Successfully created call details subscriptions for {Count} users", 
                    webSocket.id, userChunk.Rows.Count);
                    
                // Check if the subscriptions are active - this is a diagnostic step
                try 
                {
                    string subscriptionsUrl = $"{_jsonUtils.ApiEndpoint}/api/v2/notifications/channels/{webSocket.id}/subscriptions";
                    string response = await _jsonUtils.JsonRestAsync(subscriptionsUrl, null, "GET");
                    _logger?.LogDebug("[UserCallDetails] Current subscriptions: {Subscriptions}", 
                        response?.Length > 500 ? response.Substring(0, 500) + "..." : response);
                }
                catch (Exception ex) 
                {
                    _logger?.LogWarning(ex, "[UserCallDetails] Could not verify subscriptions");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallDetails][Channel:{ChannelId}] Error creating call details subscriptions for {Count} users", 
                    webSocket?.id ?? "null", userChunk.Rows.Count);
                throw; // Rethrow to allow upstream error handling
            }
        }

        protected override void CreateWebSocket(string socketAddress, string socketChannel, string threadName)
        {
            var detail = new WebSocketDetail
            {
                connectUri = socketAddress,
                id = socketChannel,
                ReportName = threadName,
                Created = DateTime.UtcNow,
                Expires = DateTime.UtcNow.AddHours(1)
            };

            var wsManager = WebSocketManagerFactory.CreateWebSocketManager(
                _logger, detail, threadName, ReceiveData, _jsonUtils);

            WebSocketManagers.Add(wsManager);
            wsManager.StartWebSocketConnection();
            
            _logger?.LogInformation("[UserCallDetails][WebSocket:{ChannelId}][Thread:{ThreadName}] Created WebSocket connection", 
                socketChannel, threadName);
        }

        public int GetActiveConnectionCount()
        {
            return WebSocketManagers?.Count(m => m.IsConnected) ?? 0;
        }

        public override Dictionary<string, object> GetServiceMetrics()
        {
            lock (_statsLock)
            {
                // Update the base class properties with our local counters
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }

            // Get base metrics (which will now include our updated values)
            var metrics = base.GetServiceMetrics();

            // Directly insert our values to be extra safe
            metrics["ProcessedEvents"] = _processedEventsCount;
            metrics["EventsProcessed"] = _processedEventsCount;
            metrics["LastEventTime"] = _lastProcessedEventTime;
            metrics["TotalWebSockets"] = WebSocketManagers?.Count ?? 0;
            metrics["ActiveWebSockets"] = GetActiveConnectionCount();
            metrics["ChannelsCreated"] = _webSocketList?.Count ?? 0;
            metrics["ActiveCalls"] = _callDetailsData?.Select("callActive = 1").Length ?? 0;

            return metrics;
        }

        private DataTable GetUsers()
        {
            return DBAdapter.GetSQLTableData("SELECT id, name FROM userDetails WHERE state = 'active'", "UserDetails");
        }

        private DataTable CreateCallDetailsTable()
        {
            // Create table with only the columns needed for call details
            DataTable dtTemp = new DataTable("userRealTimeData");
            
            // Add columns based on userrealtimedata.sql and userrealtimeconvdata.sql schema
            dtTemp.Columns.Add("keyid", typeof(string));
            dtTemp.Columns.Add("id", typeof(string));
            dtTemp.Columns.Add("name", typeof(string));
            dtTemp.Columns.Add("state", typeof(string));
            
            // Call specific columns
            dtTemp.Columns.Add("conversationid", typeof(string));
            dtTemp.Columns.Add("media", typeof(string));
            dtTemp.Columns.Add("direction", typeof(string));
            dtTemp.Columns.Add("conversationstate", typeof(string));
            dtTemp.Columns.Add("actingas", typeof(string));
            dtTemp.Columns.Add("talktime", typeof(DateTime));
            dtTemp.Columns.Add("talktimeltc", typeof(DateTime));
            dtTemp.Columns.Add("heldstate", typeof(bool));
            dtTemp.Columns.Add("heldtime", typeof(DateTime));
            dtTemp.Columns.Add("acwstate", typeof(bool));
            dtTemp.Columns.Add("acwstring", typeof(string));
            dtTemp.Columns.Add("acwtime", typeof(DateTime));
            dtTemp.Columns.Add("startdate", typeof(DateTime));
            dtTemp.Columns.Add("startdateltc", typeof(DateTime));
            dtTemp.Columns.Add("queueid", typeof(string));
            dtTemp.Columns.Add("skill1", typeof(string));
            dtTemp.Columns.Add("skill2", typeof(string));
            dtTemp.Columns.Add("skill3", typeof(string));
            dtTemp.Columns.Add("initialpriority", typeof(int));
            dtTemp.Columns.Add("usedrout", typeof(string));
            dtTemp.Columns.Add("requestedrout1", typeof(string));
            dtTemp.Columns.Add("requestedrout2", typeof(string));
            dtTemp.Columns.Add("ani", typeof(string));
            dtTemp.Columns.Add("dnis", typeof(string));
            dtTemp.Columns.Add("participantname", typeof(string));
            dtTemp.Columns.Add("segmenttime", typeof(DateTime));
            dtTemp.Columns.Add("callactive", typeof(int));
            
            // Add the manuallychecked column for conversation status tracking
            dtTemp.Columns.Add("manuallychecked", typeof(bool));
            
            // Standard timestamp column
            dtTemp.Columns.Add("updated", typeof(DateTime));
            
            _logger?.LogDebug("[UserCallDetails][Init] Creating call details table with primary columns: id, keyid, updated, conversationid, media, direction, etc.");
            
            return dtTemp;
        }

        protected override async Task ProcessEventAsync(object eventData)
        {
            // Process the event data
            
            // Update both local and base class counters
            Interlocked.Increment(ref _processedEventsCount);
            EventsProcessed = _processedEventsCount;
            _lastProcessedEventTime = DateTime.UtcNow;
            LastActivity = _lastProcessedEventTime;
            
            // Log the event processing
            _logger?.LogDebug("[UserCallDetails] Processed event, total count: {EventCount}", _processedEventsCount);
        }

        private void RecoverConnections()
        {
            try
            {
                _logger?.LogInformation("[UserCallDetails][Recover] Attempting to recover WebSocket connections");
                
                // Close any existing connections
                if (WebSocketManagers != null)
                {
                    foreach (var manager in WebSocketManagers)
                    {
                        manager.StopWebSocketConnection();
                    }
                    WebSocketManagers.Clear();
                }

                // Create new channel
                var channel = CreateChannel("userCallDetails-recovery");
                if (channel != null)
                {
                    var manager = WebSocketManagerFactory.CreateWebSocketManager(
                        _logger, 
                        channel, 
                        "userCallDetails-thread",
                        ReceiveData,
                        _jsonUtils
                    );
                    
                    if (WebSocketManagers == null)
                    {
                        _logger?.LogWarning("[UserCallDetails][Recover] WebSocketManagers is null, creating new list");
                    }
                    
                    WebSocketManagers.Add(manager);
                    manager.StartWebSocketConnection();
                    
                    _logger?.LogInformation("[UserCallDetails][Recover] Successfully created new WebSocket connection");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallDetails][Recover] Failed to recover connections");
                throw;
            }
        }

        private async Task SafeWriteToDatabaseWithLogging(DataTable data, string dataDescription)
        {
            try
            {
                _logger?.LogInformation("[UserCallDetails][DB] Writing {Count} {Description} records to database", 
                    data.Rows.Count, dataDescription);
                await base.SafeWriteToDatabase(data, dataDescription);
                _logger?.LogInformation("[UserCallDetails][DB] Successfully wrote {Count} {Description} records", 
                    data.Rows.Count, dataDescription);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallDetails][DB] Error writing {Description} data to database", dataDescription);
                throw;
            }
        }

        public async Task GetConversationStatus()
        {
            List<string> conversationIds = new List<string>();
            List<int> rowsToUpdateIndexes = new List<int>();
            
            try
            {
                _logger?.LogDebug("[UserCallDetails][ConversationStatus] Checking for stale conversations");
                lock (_callDetailsData)
                {
                    foreach (DataRow row in _callDetailsData.Rows.Cast<DataRow>().ToList())
                    {
                        // Null handling for startDate and updatedDate
                        if (row["startdate"] == DBNull.Value || row["updated"] == DBNull.Value)
                        {
                            continue;
                        }

                        DateTime startDate;
                        DateTime updatedDate;

                        if (!DateTime.TryParse(row["startdate"].ToString(), out startDate) ||
                            !DateTime.TryParse(row["updated"].ToString(), out updatedDate))
                        {
                            continue;
                        }

                        TimeSpan difference = updatedDate - startDate;
                        double differenceInMinutes = difference.TotalMinutes;

                        // Handle manually checked flag
                        if (row["manuallychecked"] != DBNull.Value && Convert.ToBoolean(row["manuallychecked"]))
                        {
                            // Reset manually checked flag after 60 minutes for non-email conversations
                            if (differenceInMinutes > 60 && row["media"].ToString() != "email")
                            {
                                row["manuallychecked"] = false;
                            }
                            else
                            {
                                continue;
                            }
                        }

                        // Check conversations older than 5 minutes
                        if (differenceInMinutes > 5)
                        {
                            conversationIds.Add(row["conversationid"].ToString());
                            rowsToUpdateIndexes.Add(_callDetailsData.Rows.IndexOf(row));
                        }
                    }
                }

                if (conversationIds.Count > 0)
                {
                    _logger?.LogInformation("[UserCallDetails][ConversationStatus] Checking {Count} stale conversations",
                        conversationIds.Count);
                    
                    int batchSize = 100;
                    int numBatches = (int)Math.Ceiling((double)conversationIds.Count / batchSize);

                    for (int batchIndex = 0; batchIndex < numBatches; batchIndex++)
                    {
                        List<string> batchConversationIds = conversationIds
                            .Skip(batchIndex * batchSize)
                            .Take(batchSize)
                            .ToList();

                        string conversationIdsString = string.Join(",", batchConversationIds);
                        
                        try
                        {
                            // Fix: Properly convert JToken to string
                            var jsonResponseToken = await _jsonUtils.JsonReturnAsync(
                                $"{_jsonUtils.ApiEndpoint}/api/v2/analytics/conversations/details?id={conversationIdsString}", 
                                null, 
                                "GET");
                            
                            // Convert JToken to string
                            string jsonResponse = jsonResponseToken?.ToString(Newtonsoft.Json.Formatting.None) ?? "{}";
                            
                            if (!string.IsNullOrEmpty(jsonResponse) && jsonResponse != "{}")
                            {
                                JObject jsonObject = JObject.Parse(jsonResponse);
                                JArray conversations = jsonObject["conversations"] as JArray;
                                
                                if (conversations != null)
                                {
                                    foreach (JToken conversation in conversations)
                                    {
                                        string conversationId = conversation["conversationId"]?.Value<string>() ?? "";
                                        
                                        lock (_callDetailsData)
                                        {
                                            for (int i = 0; i < rowsToUpdateIndexes.Count; i++)
                                            {
                                                int rowIndex = rowsToUpdateIndexes[i];
                                                if (rowIndex >= 0 && rowIndex < _callDetailsData.Rows.Count)
                                                {
                                                    DataRow row = _callDetailsData.Rows[rowIndex];
                                                    if (row["conversationid"].ToString() == conversationId)
                                                    {
                                                        row["manuallychecked"] = true;
                                                        _logger?.LogDebug("[UserCallDetails][ConversationStatus] Conversation ID: {ConversationId} manually checked", conversationId);

                                                        // Check if conversation has ended
                                                        if (conversation["conversationEnd"] != null)
                                                        {
                                                            row["conversationstate"] = "disconnected";
                                                            _logger?.LogInformation("[UserCallDetails][ConversationStatus] Marking conversation ID: {ConversationId} as disconnected", conversationId);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "[UserCallDetails][ConversationStatus] Error during API call for conversations: {ConversationIds}", conversationIdsString);
                        }
                    }

                    // Mark database as needing an update
                    _writeCallData = true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallDetails][ConversationStatus] Error checking conversation status");
            }
        }

        protected override async Task PollDataAsync()
        {
            try
            {
                var retryCount = 0;
                const int maxRetries = 5;
                const int baseDelay = 1000; // Start with 1 second delay
                
                // Create a local copy of _cts to avoid race conditions
                var localCts = _cts;

                while (localCts != null && !localCts.Token.IsCancellationRequested && retryCount < maxRetries)
                {
                    try
                    {
                        // Check WebSocket connections
                        if (WebSocketManagers == null || !WebSocketManagers.Any(m => m.IsConnected))
                        {
                            var delay = (int)(baseDelay * Math.Pow(2, retryCount));
                            _logger?.LogWarning("[UserCallDetails][Poll] No active WebSocket connections. Attempting reconnect in {Delay}ms (Attempt {Attempt}/{MaxAttempts})",
                                delay, retryCount + 1, maxRetries);
                            
                            await Task.Delay(delay, localCts.Token);
                            await Task.Run(() => RecoverConnections(), localCts.Token);
                            retryCount++;
                        }
                        else
                        {
                            retryCount = 0;
                            
                            // Run conversation status check periodically
                            await GetConversationStatus();
                            
                            // Wait before next cycle
                            await Task.Delay(30000, localCts.Token); // Check every 30 seconds
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[UserCallDetails][Poll] Error during polling iteration");
                        retryCount++;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallDetails][Poll] Error during polling");
            }
        }

        private async Task ProcessAdherenceData(JToken adherenceData)
        {
            try
            {
                string userId = adherenceData["topicName"]?.ToString()
                    .Replace("v2.users.", "")
                    .Replace(".workforcemanagement.adherence", "");

                _logger?.LogInformation("[UserCallDetails][Adherence] Processing adherence data for user: {UserId}", userId);

                // Additional processing logic here
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallDetails][Adherence] Error processing adherence data");
            }
        }

        public async Task<string> GetCallStatsAsync(string url, string jsonBody)
        {
            var responseToken = await _jsonUtils.JsonReturnAsync(_jsonUtils.ApiEndpoint + url, null, "GET", jsonBody);
            if (responseToken == null)
                return string.Empty;
            
            // Explicitly convert JToken to string
            return responseToken.ToString(Newtonsoft.Json.Formatting.None);
        }
    }
}
