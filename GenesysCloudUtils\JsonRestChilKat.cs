﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using Newtonsoft.Json;
using RealCN = RealUserPushConversations;
using RealQCN = RealQueuePushConversations;
using RealQObs = RealQueuePushObservations;
using RealUA = RealUserPushActivityDef;
using RealUC = RealUserPushCallStatsDef;
using StandardUtils;
using GenesysCloudUtils.WebSocket; // Add this namespace

namespace GenesysCloudUtils
{
    public class JsonRestChilkat
    {
        public string APIKey { get; set; }
        public string APIBaseURL { get; set; }
        public string APILoginBaseURL { get; set; }
        public string APICLID { get; set; }
        public string APICLSC { get; set; }
        public string APISockURL { get; set; }

        public DateTime APILastAPICall { get; set; }
        public WebSocketDetail GCWebSocket { get; set; }
        public String SyncType { get; set; }

        public string APIProxyAddress { get; set; }
        public int APIProxyPort { get; set; }
        public string APIProxyUserName { get; set; }
        public string APIProxyPassword { get; set; }

        public DataTable DtRealTimeData { get; set; }
        public DataTable DTQueues { get; set; }
        public DataTable DTUsers { get; set; }

        public DataTable DTUserActivity { get; set; }
        public DataTable DTUserAdherence { get; set; }
        public DataTable DTUserCallStats { get; set; }
        public DataTable DTUserConversations { get; set; }
        public DataTable DTQueueConversations { get; set; }

        Chilkat.Global ChilGlob = new Chilkat.Global();

        public string CustomerKeyID { get; set; }

        private DBUtils.DBUtils DBUtils = new DBUtils.DBUtils();

        private DateTime LastChannelUpd;
        private DateTime LastTermUpd;
        private Boolean WriteUserDataAct = false;
        private Boolean WriteUserDataAdh = false;
        private Boolean WriteUserDataCalls = false;

        private System.Timers.Timer Process;

        public JsonRestChilkat()
        {
        }

        public bool Initialize()
        {
            Utils UCAUtils = new Utils();

            Console.WriteLine("ChilKat: Getting Customer Config File & Customer Key ID");

            Console.WriteLine("ChilKat: Unlocking ChilkKat Code");
            if (UnlockChilKat() != true)
                throw new ApplicationException("ChilKat: Cannot Unlock Chilkat - Do Not Proceed");

            APICLID = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_USERID");
            APICLSC = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_SECRET");
            APIBaseURL = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_URL");
            APILoginBaseURL = "https://login.mypurecloud.com.au/oauth/token";
            APISockURL = "streaming.mypurecloud.com.au";

            Console.WriteLine("ChilKat: Getting API Key");
            if (GetAuthAPIKey() != true)
            {
                Console.WriteLine("ChilKat: Cannot Obtain API Key - Do Not Proceed");
                return false;
            }

            DBUtils.Initialize();

            LastChannelUpd = DateTime.Now;
            LastTermUpd = DateTime.Now;

            return true;
        }

        public string ReturnJson(string APIURI)
        {
            Chilkat.Rest ChilRest = new Chilkat.Rest();
            Chilkat.Socket ChilSocket = new Chilkat.Socket();

            if (APIProxyAddress != null)
            {

                //Console.WriteLine("ChilKat GET : Using Proxy For Connection Address: {0}", APIProxyAddress);
                ChilSocket.HttpProxyHostname = APIProxyAddress;
                ChilSocket.HttpProxyPort = APIProxyPort;

                if (APIProxyUserName != null)
                {
                    ChilSocket.HttpProxyUsername = APIProxyUserName;
                    ChilSocket.HttpProxyPassword = APIProxyPassword;
                }
            }


            bool AutoReconnect = true;
            bool TLS = true;
            int Port = 443;
            int MaxWaitMs = 5000;

            bool success = ChilSocket.Connect(APIBaseURL, Port, TLS, MaxWaitMs);

            if (success != true)
            {
                Console.WriteLine("ChilKat GET : Connect Error {0}", ChilSocket.LastErrorText);
                return null;
            }

            success = ChilRest.UseConnection(ChilSocket, AutoReconnect);

            if (success != true)
            {
                Console.WriteLine("ChilKat GET : Connect Error {0}", ChilRest.LastErrorText);
                return null;
            }

            StringBuilder JSONResponse = new StringBuilder();

            int Attempts = 1;

            while (Attempts < 6)
            {

                Console.Write("ConGet:");

                if ((APILastAPICall.AddSeconds(86400) - DateTime.Now).TotalSeconds < 43200)
                {
                    Console.WriteLine("ChilKat GET : Reset API Needed");
                    if (GetAuthAPIKey() == false)
                    {
                        throw new Exception("ChilKat GET : Cannot Get New API Key");
                    }
                }
                Chilkat.StringBuilder AuthHeaderVal = new Chilkat.StringBuilder();
                AuthHeaderVal.Append("Bearer ");
                AuthHeaderVal.Append(APIKey);
                ChilRest.Authorization = AuthHeaderVal.GetAsString();

                ChilRest.AddHeader("Content-Type", "application/json; charset=UTF-8");
                JSONResponse.Append(ChilRest.FullRequestNoBody("GET", APIURI));

                if (ChilRest.ResponseStatusCode == 200 && JSONResponse.ToString().Contains("gateway.timeout") == false && JSONResponse.ToString().Contains("service.unavailable") == false)
                {
                    break;
                }
                else
                {
                    Attempts++;

                    switch (ChilRest.ResponseStatusCode)
                    {
                        case 429:
                            Console.WriteLine("ChilKat GET : Too Many Requests: Shifting API Key");
                            GetAuthAPIKey();
                            break;
                        case 503:
                        case 504:
                            JSONResponse.Append("{}");
                            Attempts = 7;
                            break;
                    }
                }
            }

            ChilRest.Dispose();
            ChilSocket.Dispose();

            return JSONResponse.ToString();
        }

        public string ReturnJson(string APIURI, string Body)
        {
            Chilkat.Rest ChilRest = new Chilkat.Rest();
            Chilkat.Socket ChilSocket = new Chilkat.Socket();

            if (APIProxyAddress != null)
            {
                //Console.WriteLine("ChilKat POST :  Using Proxy For Connection Address: {0}", APIProxyAddress);
                ChilSocket.HttpProxyHostname = APIProxyAddress;
                ChilSocket.HttpProxyPort = APIProxyPort;

                if (APIProxyUserName != null)
                {
                    ChilSocket.HttpProxyUsername = APIProxyUserName;
                    ChilSocket.HttpProxyPassword = APIProxyPassword;
                }
            }

            bool AutoReconnect = true;
            bool TLS = true;
            int Port = 443;
            int MaxWaitMs = 5000;

            bool success = ChilSocket.Connect(APIBaseURL, Port, TLS, MaxWaitMs);

            if (success != true)
            {
                Console.WriteLine("ChilKat POST : Connect Error {0}", ChilSocket.LastErrorText);
                return null;
            }

            success = ChilRest.UseConnection(ChilSocket, AutoReconnect);

            if (success != true)
            {
                Console.WriteLine("ChilKat POST : Connect Error {0}", ChilRest.LastErrorText);
                return null;
            }

            StringBuilder JSONResponse = new StringBuilder();
            if (success != true)
            {
                Console.WriteLine(ChilRest.LastErrorText);
                return "Connect Error";
            }

            int Attempts = 1;

            while (Attempts < 6)
            {

                Console.Write("ConPost:");
                if ((APILastAPICall.AddSeconds(86400) - DateTime.Now).TotalSeconds < 43200)
                {
                    Console.WriteLine("ChilKat POST : Reset API Needed");
                    if (GetAuthAPIKey() == false)
                    {
                        throw new Exception("ChilKat POST : Cannot Get New API Key");
                    }

                    APILastAPICall = DateTime.Now;

                }

                Chilkat.StringBuilder AuthHeaderVal = new Chilkat.StringBuilder();
                AuthHeaderVal.Append("Bearer ");
                AuthHeaderVal.Append(APIKey);
                ChilRest.Authorization = AuthHeaderVal.GetAsString();

                ChilRest.AddHeader("Content-Type", "application/json; charset=UTF-8");
                JSONResponse.Append(ChilRest.FullRequestString("POST", APIURI, Body));

                if (ChilRest.ResponseStatusCode == 200 && JSONResponse.ToString().Contains("gateway.timeout") == false && JSONResponse.ToString().Contains("service.unavailable") == false)
                {
                    break;
                }
                else
                {
                    Attempts++;

                    switch (ChilRest.ResponseStatusCode)
                    {
                        case 429:
                            Console.WriteLine("ChilKat POST : Too Many Requests: Pause & Shifting API Key");
                            Thread.Sleep(60000);
                            GetAuthAPIKey();
                            JSONResponse.Clear();
                            JSONResponse.Append("{}");
                            break;
                        case 503:
                        case 504:
                        default:
                            JSONResponse.Clear();
                            JSONResponse.Append("{}");
                            Attempts = 7;
                            break;

                    }

                }


            }

            ChilRest.Dispose();
            ChilSocket.Dispose();

            return JSONResponse.ToString();
        }

        private void TimerCallBack(object sender, ElapsedEventArgs elapsedEventArg)
        {
            Console.WriteLine("\nProcess:{0}", SyncType);

            switch (SyncType)
            {

                case "userRealTimeData":
                    UserUpdate();
                    break;
                default:
                    ConvUpdate();
                    break;
            }

        }

        private void UserUpdate()
        {
            lock (DtRealTimeData)
            {
                if (DtRealTimeData != null)
                {
                    if (WriteUserDataAct || WriteUserDataAdh || WriteUserDataCalls)
                    {


                        DataTable xDataTable = DtRealTimeData.GetChanges();
                        DtRealTimeData.AcceptChanges();
                        Console.Write("WrtUS:");

                        Boolean Successful = DBUtils.WriteSQLData(xDataTable, SyncType);

                        if (WriteUserDataAct)
                            WriteUserDataAct = false;
                        if (WriteUserDataAdh)
                            WriteUserDataAdh = false;
                        if (WriteUserDataCalls)
                            WriteUserDataCalls = false;
                    }
                }
            }
        }

        private void ConvUpdate()

        {
            lock (DtRealTimeData)
            {
                if (DtRealTimeData != null)
                {
                    if (DtRealTimeData.Rows.Count > 0)
                    {
                        Console.Write("WrtQC:");
                        Boolean Successful = DBUtils.WriteSQLData(DtRealTimeData, SyncType);
                        DtRealTimeData.Clear();
                    }
                }



                if ((DateTime.Now - LastTermUpd).TotalSeconds > 30)
                {
                    Boolean ClearTable = false;

                    switch (SyncType)
                    {
                        case "queuerealtimeconvData":
                            ClearTable = DBUtils.ExecuteSQLQuery("delete from queuerealtimeconvData where conversationid in (select * from ( select qr.conversationid	from queuerealtimeconvData qr      where  (qr.state in ('terminated','disconnected') and qr.acwstate = 0) or qr.conversationid in (select ur.conversationid from vwRealTimeUser ur where ur.systempresence = 'offline' and ur.OrgPresence = 'offline')) rawdata)");
                            DtRealTimeData = DBUtils.GetSQLTableData("select * from queuerealtimeconvData", "queuerealtimeconvData");
                            break;
                        //case "userRealTimeData":
                        //    //DtRealTimeData = DtInMemoryUsers.Copy();

                        //    break;
                        case "userRealTimeConvData":
                            ClearTable = DBUtils.ExecuteSQLQuery("delete  from userRealTimeConvData where (conversationstate in ('terminated','disconnected') and acwstate = 0) or  id in (select id from userRealTimeData where systempresence = 'offline')");
                            DtRealTimeData = DBUtils.GetSQLTableData("select * from userRealTimeConvData", "userRealTimeConvData");
                            break;
                    }

                    LastTermUpd = DateTime.Now;
                    Console.Write("CT:{0}\nLast Time Update Checked:{1}  Sync Type:{2}\n", ClearTable, LastTermUpd, SyncType);
                }


                if ((DateTime.Now - LastChannelUpd).TotalSeconds > 45)
                {
                    Console.Write("UC:");
                    Console.WriteLine("\nOld Auth:{0}", APIKey);
                    GetAuthAPIKey();
                    Console.WriteLine("\nNew Auth:{0}", APIKey);
                    LastChannelUpd = DateTime.Now;

                }
            }
        }

        public void CreateWebSocket(string SocketAddress, string SocketChannel, string ThreadName)
        {
            Chilkat.Rest ChilRest = new Chilkat.Rest();
            Chilkat.WebSocket ChilWebSocket = new Chilkat.WebSocket();

            Process = new System.Timers.Timer(3000);
            Process.Elapsed += new System.Timers.ElapsedEventHandler(this.TimerCallBack);
            Process.AutoReset = true;
            Process.Enabled = true;

            bool success = ChilRest.Connect(SocketAddress, 443, true, false);
            if (success != true)
            {
                Console.WriteLine(ChilRest.LastErrorText);
                return;
            }


            success = ChilWebSocket.UseConnection(ChilRest);
            if (success != true)
            {
                Console.WriteLine(ChilWebSocket.LastErrorText);
                return;
            }

            ChilWebSocket.AddClientHeaders();

            string ResponseBody = ChilRest.FullRequestNoBody("GET", SocketChannel);
            success = ChilWebSocket.ValidateServerHandshake();
            if (success != true)
            {
                Console.WriteLine(ChilWebSocket.LastErrorText);
                Console.WriteLine(ResponseBody);
                Console.WriteLine(ChilRest.ResponseHeader);
                return;
            }

            //Console.WriteLine("Starting Receive");
            while (true)
            {
                Boolean SuccessFul = ChilWebSocket.ReadFrame();
                //Console.WriteLine("Frame Received. Type {0}", ChilWebSocket.FrameOpcodeInt);
                if (SuccessFul == true)
                {
                    if (ChilWebSocket.FrameOpcodeInt == 1)
                    {
                        // Console.WriteLine("*/Start ReadFrame :{0}", SuccessFul);

                        string ReceivedJson = ChilWebSocket.GetFrameData();
                        //Console.WriteLine("\nReceived:{0}",ReceivedJson);
                        ReceiveData(ReceivedJson, ThreadName);
                        //Console.WriteLine("\nReceived:{0} SyncType {1} Records in Buffer {2}", DateTime.Now,SyncType,DtRealTimeData.Rows.Count);
                    }
                }
                else
                {
                    Console.WriteLine("No Data");
                }
            }
        }

        private bool ReceiveData(string JsonString, string ThreadName)
        {
            bool Successful = false;

            AlertObject Notification = new AlertObject();

            if (JsonString.IndexOf("WebSocket Heartbeat") > 0)
            {
                Console.Write("{0} Hrt Beat:{1}\n", ThreadName, DateTime.Now);

                if ((DateTime.Now - LastChannelUpd).TotalSeconds > 21600)
                {
                    LastChannelUpd = DateTime.Now;
                    Console.Write("{0} Redo WebSockets:{1}\n", ThreadName, DateTime.Now);

                    if (GetAuthAPIKey() != true)
                    {
                        throw new ApplicationException("ChilKat: Cannot Obtain API Key - Do Not Proceed");
                    }
                    else
                    {
                        Console.WriteLine("Regenerated The API For Thread :{0} Now {1}", ThreadName, APIKey);
                        Console.WriteLine("Redoing the Subs");

                        switch (ThreadName)
                        {
                            case "userRealTimeData":
                                Console.WriteLine("Renewing User Activity Job");
                                CreateUserActivitySubs(GCWebSocket);
                                CreateUserAdherenceSubs(GCWebSocket);
                                CreateUserCallSubs(GCWebSocket);
                                break;
                            case "userRealTimeConvData":
                                Console.WriteLine("Renewing User Conversation Job");
                                CreateUserConvSubs(GCWebSocket);
                                break;
                            case "queuerealtimeconvData":
                                Console.WriteLine("Renewing Queue Conversation Job");
                                CreateQueueConvSubs(GCWebSocket);
                                break;
                        }

                    }

                }
            }
            else
            {
                Notification = JsonConvert.DeserializeObject<AlertObject>(JsonString,
                                                            new JsonSerializerSettings
                                                            {
                                                                NullValueHandling = NullValueHandling.Ignore
                                                            });

                string NotificationClass = Notification.topicName.ToLower().Split('.')[1];
                string NotificationType = string.Empty;
                if (NotificationClass == "users")
                    NotificationType = Notification.topicName.ToLower().Split('.')[3];
                else
                    NotificationType = Notification.topicName.ToLower().Split('.')[2];

                Console.WriteLine("NotificationType:{0}", NotificationType);
                switch (NotificationType)
                {
                    case "workforcemanagement":
                        TransAdherence(JsonString);
                        break;
                    case "conversationsummary":
                        TransCalls(JsonString);
                        break;
                    case "conversations":
                        TransConvs(JsonString);
                        break;
                    case "activity":
                        TransActivity(JsonString);
                        break;
                    case "queues":
                        TransQConv(JsonString);
                        break;

                    default:
                        Console.Write("Unknown:{0}\nJSON:\n{1}", NotificationType, JsonString);
                        break;
                }
            }


            return Successful;
        }

        public bool GetAuthAPIKey()
        {

            Chilkat.Http ChilHTTP = new Chilkat.Http();
            Chilkat.HttpRequest ChilTokenReq = new Chilkat.HttpRequest();
            Chilkat.HttpResponse ChilResp = new Chilkat.HttpResponse();
            Chilkat.JsonObject ChilJSON = new Chilkat.JsonObject();

            ChilTokenReq.HttpVerb = "POST";
            ChilTokenReq.AddParam("grant_type", "client_credentials");
            ChilTokenReq.AddParam("client_id", APICLID);
            ChilTokenReq.AddParam("client_secret", APICLSC);


            if (APIProxyAddress != null)
            {
                Console.WriteLine("ChilKat: API Creation. Using Proxy For Connection Address: {0}", APIProxyAddress);
                ChilHTTP.ProxyDomain = APIProxyAddress;
                ChilHTTP.ProxyPort = APIProxyPort;

                if (APIProxyUserName != null)
                {
                    ChilHTTP.ProxyLogin = APIProxyUserName;
                    ChilHTTP.ProxyPassword = APIProxyPassword;
                }
            }

            ChilResp = ChilHTTP.PostUrlEncoded(APILoginBaseURL, ChilTokenReq);
            if (ChilHTTP.LastMethodSuccess == false)
            {
                Console.WriteLine(ChilHTTP.LastErrorText);
                return false;
            }


            // Make sure we got a 200 response status code, otherwise it's an error.
            if (ChilResp.StatusCode != 200)
            {
                Console.WriteLine("ChilKat: POST to token endpoint failed.");
                Console.WriteLine("ChilKat: Received response status code " + Convert.ToString(ChilResp.StatusCode));
                Console.WriteLine("ChilKat: Response body containing error text or JSON:");
                Console.WriteLine(ChilResp.BodyStr);

                return false;

            }

            Boolean Success = ChilJSON.Load(ChilResp.BodyStr);
            string AccessToken = ChilJSON.StringOf("access_token");


            ChilHTTP.Dispose();
            ChilTokenReq.Dispose();
            ChilResp.Dispose();
            ChilJSON.Dispose();

            APILastAPICall = DateTime.Now;

            APIKey = AccessToken;

            return true;

        }

        private bool UnlockChilKat()
        {

            bool unlockSuccess = ChilGlob.UnlockBundle("PLWLSN.CB1072023_tU7XBBn395nV");
            if ((unlockSuccess != true))
            {
                Console.WriteLine("ChilKat: Unlock Failed");
                Console.WriteLine(ChilGlob.LastErrorText);
                return false;
            }
            else
            {
                Console.WriteLine("ChilKat: Unlock Succeeded");
                return true;
            }

        }

        private Boolean TransActivity(string JsonString)
        {
            Boolean Successful = false;

            Console.Write("Act:");

            RealUA.Activity UserActivity = new RealUA.Activity();
            UserActivity = JsonConvert.DeserializeObject<RealUA.Activity>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
            RealUA.EventbodyUser RealUserInfo = UserActivity.eventBody;

            lock (DtRealTimeData)
            {
                DataRow DRUserAct = DtRealTimeData.Select("id = '" + RealUserInfo.id + "'").FirstOrDefault();

                if (DRUserAct != null)
                {
                    DRUserAct["routingStatus"] = RealUserInfo.routingStatus.status;
                    DRUserAct["routstarttime"] = RealUserInfo.routingStatus.startTime;
                    DRUserAct["systemPresence"] = RealUserInfo.presence.presenceDefinition.systemPresence;
                    DRUserAct["presenceId"] = RealUserInfo.presence.presenceDefinition.id;
                    DRUserAct["presstarttime"] = RealUserInfo.presence.modifiedDate;
                }
                WriteUserDataAct = true;
                Successful = true;
            }

            return Successful;
        }

        private Boolean TransAdherence(string JsonString)
        {
            Boolean Successful = false;

            Console.Write("Adh:");

            RealUA.Adherence UserAdherence = new RealUA.Adherence();
            UserAdherence = JsonConvert.DeserializeObject<RealUA.Adherence>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
            RealUA.EventbodyAdherence RealUserInfo = UserAdherence.eventBody;


            lock (DtRealTimeData)
            {
                DataRow DRUserAct = DtRealTimeData.Select("id = '" + RealUserInfo.user.id + "'").FirstOrDefault();

                if (DRUserAct != null)
                {
                    DRUserAct["id"] = RealUserInfo.user.id;
                    DRUserAct["adherenceState"] = RealUserInfo.adherenceState;
                    DRUserAct["adherencestarttime"] = RealUserInfo.adherenceChangeTime;
                    DRUserAct["impact"] = RealUserInfo.impact;
                    DRUserAct["scheduledActivityCategory"] = RealUserInfo.scheduledActivityCategory;
                }
                WriteUserDataAdh = true;
                Successful = true;
            }


            Successful = true;



            return Successful;
        }

        private Boolean TransCalls(string JsonString)
        {
            Boolean Successful = true;

            Console.Write("Cls:");

            RealUC.CallStats UserCalls = new RealUC.CallStats();
            UserCalls = JsonConvert.DeserializeObject<RealUC.CallStats>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
            RealUC.Eventbody RealUserInfo = UserCalls.eventBody;


            lock (DtRealTimeData)
            {
                DataRow DRUserAct = DtRealTimeData.Select("id = '" + UserCalls.topicName.Split('.')[2] + "'").FirstOrDefault();

                if (DRUserAct != null)
                {
                    DRUserAct["cccallactive"] = RealUserInfo.call.contactCenter.active;
                    DRUserAct["cccallacw"] = RealUserInfo.call.contactCenter.acw;
                    DRUserAct["othcallactive"] = RealUserInfo.call.enterprise.active;
                    DRUserAct["othcallactive"] = RealUserInfo.call.enterprise.acw;
                    DRUserAct["cbcallactive"] = RealUserInfo.callback.contactCenter.active;
                    DRUserAct["cbcallacw"] = RealUserInfo.callback.contactCenter.acw;
                    DRUserAct["cbothcallactive"] = RealUserInfo.callback.enterprise.active;
                    DRUserAct["cbothcallacw"] = RealUserInfo.callback.enterprise.acw;
                    DRUserAct["ccemailactive"] = RealUserInfo.email.contactCenter.active;
                    DRUserAct["ccemailacw"] = RealUserInfo.email.contactCenter.acw;
                    DRUserAct["othemailactive"] = RealUserInfo.email.enterprise.active;
                    DRUserAct["othemailacw"] = RealUserInfo.email.enterprise.acw;
                    DRUserAct["ccchatactive"] = RealUserInfo.chat.contactCenter.active;
                    DRUserAct["ccchatacw"] = RealUserInfo.chat.contactCenter.acw;
                    DRUserAct["othchatactive"] = RealUserInfo.chat.enterprise.active;
                    DRUserAct["othchatacw"] = RealUserInfo.chat.enterprise.acw;
                }
                WriteUserDataCalls = true;
                Successful = true;
            }


            DataRow DRCallStats = DTUserCallStats.NewRow();

            DRCallStats["id"] = UserCalls.topicName.Split('.')[2];
            DRCallStats["id"] = UserCalls.topicName.Split('.')[2];


            DTUserCallStats.Rows.Add(DRCallStats);

            Console.Write("\nCls{0}Row(s):", DTUserCallStats.Rows.Count);
            Successful = true;

            return Successful;
        }

        private Boolean TransConvs(string JsonString)
        {
            Boolean Successful = false;

            //Console.WriteLine("JSONSTRING : \n{0}",JsonString);
            Console.Write("User Que Conv:");

            RealCN.Conversations UserConvs = new RealCN.Conversations();
            UserConvs = JsonConvert.DeserializeObject<RealCN.Conversations>(JsonString,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                });



            RealCN.Eventbody RealConversation = UserConvs.eventBody;

            foreach (RealCN.Participant ConvPart in RealConversation.participants)
            {

                Boolean SuccessRecord = false;
                ConversationDetails ConvDetails = new ConversationDetails();

                #region "User Voice"

                if (ConvPart.calls != null)
                {
                    foreach (RealCN.Call ConvCall in ConvPart.calls)
                    {
                        if (!string.IsNullOrEmpty(ConvPart?.userId))
                        {
                            Console.WriteLine("Purpose:{0} Conv Id:{1} State:{2} User Id:{3} Media:Voice", ConvPart.purpose,
                                            RealConversation.id,
                                            ConvCall.state,
                                            ConvPart.userId);

                            ConvDetails = new ConversationDetails();

                            ConvDetails.Id = ConvPart.userId;
                            ConvDetails.ConversationId = RealConversation.id;
                            ConvDetails.Direction = ConvCall.direction;
                            ConvDetails.Purpose = ConvPart.purpose;
                            ConvDetails.QueueId = ConvPart.queueId;
                            ConvDetails.Mediatype = "voice";
                            ConvDetails.State = ConvCall.state;
                            ConvDetails.Held = ConvCall.held;
                            if (ConvCall.state == "connected")
                                ConvDetails.TalkTime = ConvPart.connectedTime.ToString();
                            else
                                ConvDetails.TalkTime = string.Empty;

                            if (ConvCall.afterCallWork != null)
                            {
                                ConvDetails.ACWState = ConvCall.afterCallWork.state;
                                if (ConvCall.afterCallWork.state == "pending")
                                {
                                    ConvDetails.IsACW = true;
                                    ConvDetails.ACWStartTime = ConvCall.afterCallWork.startTime.ToString();
                                }
                                else
                                {
                                    ConvDetails.IsACW = false;
                                    ConvDetails.ACWStartTime = null;
                                    ConvDetails.ACWState = null;
                                }
                            }

                            if (ConvCall.held == false)
                            {
                                ConvDetails.StartHoldTime = null;
                            }
                            else
                            {
                                ConvDetails.StartHoldTime = ConvCall.startHoldTime.ToString();
                            }

                        }

                        if (ConvDetails != null)
                            SuccessRecord = AddUConversationRealTimeRow(ConvDetails);
                    }


                }

                #endregion

                #region "User Callbacks"
                //Callbacks
                if (ConvPart.callbacks != null)
                {
                    foreach (RealCN.Callback ConvCall in ConvPart.callbacks)
                    {
                        if (string.IsNullOrEmpty(ConvPart?.userId))
                        {

                            Console.WriteLine("Purpose:{0} Conv Id:{1} State:{2} User Id:{3} Media:Callback", ConvPart.purpose,
                                                RealConversation.id,
                                                ConvCall.state,
                                                ConvPart.userId);


                            ConvDetails = new ConversationDetails();
                            ConvDetails.Id = ConvPart.userId;
                            ConvDetails.ConversationId = RealConversation.id;
                            ConvDetails.Direction = ConvCall.direction;
                            ConvDetails.Purpose = ConvPart.purpose;
                            ConvDetails.QueueId = ConvPart.queueId;
                            ConvDetails.Mediatype = "callback";
                            ConvDetails.State = ConvCall.state;
                            ConvDetails.Held = ConvCall.held;
                            if (ConvCall.state == "connected")
                                ConvDetails.TalkTime = ConvPart.connectedTime.ToString();

                            if (ConvCall.afterCallWork != null)
                            {
                                ConvDetails.ACWState = ConvCall.afterCallWork.state;
                                if (ConvCall.afterCallWork.state == "pending")
                                {
                                    ConvDetails.IsACW = true;
                                    ConvDetails.ACWStartTime = ConvCall.afterCallWork.startTime.ToString();
                                }
                                else
                                {
                                    ConvDetails.IsACW = false;
                                    ConvDetails.ACWStartTime = null;
                                    ConvDetails.ACWState = null;
                                }
                            }

                            if (ConvCall.held == false)
                            {
                                ConvDetails.StartHoldTime = null;
                            }
                            else
                            {
                                ConvDetails.StartHoldTime = ConvCall.startHoldTime.ToString();
                            }
                        }


                        if (ConvDetails != null)
                            SuccessRecord = AddUConversationRealTimeRow(ConvDetails);
                    }
                }

                #endregion

                #region "User Email"

                //Email Interactions
                try
                {
                    if (ConvPart.emails != null)
                    {
                        foreach (RealCN.Email ConvCall in ConvPart.emails)
                        {
                            if (string.IsNullOrEmpty(ConvPart?.userId))
                            {
                                ConvDetails = new ConversationDetails();
                                ConvDetails.Id = ConvPart.userId;
                                ConvDetails.ConversationId = RealConversation.id;
                                ConvDetails.Direction = ConvCall.direction;
                                ConvDetails.Purpose = ConvPart.purpose;
                                ConvDetails.QueueId = ConvPart.queueId;
                                ConvDetails.Mediatype = "email";
                                ConvDetails.State = ConvCall.state;
                                ConvDetails.Held = ConvCall.held;
                                if (ConvCall.state == "connected")
                                    ConvDetails.TalkTime = ConvPart.connectedTime.ToString();

                                if (ConvCall.afterCallWork != null)
                                {
                                    ConvDetails.ACWState = ConvCall.afterCallWork.state;
                                    if (ConvCall.afterCallWork.state == "pending")
                                    {
                                        ConvDetails.IsACW = true;
                                        ConvDetails.ACWStartTime = ConvCall.afterCallWork.startTime.ToString();
                                    }
                                    else
                                    {
                                        ConvDetails.IsACW = false;
                                        ConvDetails.ACWStartTime = null;
                                        ConvDetails.ACWState = null;
                                    }
                                }

                                if (ConvCall.held == false)
                                {
                                    ConvDetails.StartHoldTime = null;
                                }
                                else
                                {
                                    ConvDetails.StartHoldTime = ConvCall.startHoldTime.ToString();
                                }
                            }

                            if (ConvDetails != null)
                                SuccessRecord = AddUConversationRealTimeRow(ConvDetails);

                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(
                        "\n" + DateTime.Now + "\nUnHandled Error In Receiving Data Area:{2} \n:Error Code :{0}\n" +
                        "Inner Error:{1}\nEnding RealTime Application",
                                  ex.ToString(),
                                  ex.InnerException,
                                  "Email Interaction Transfer");
                }

                #endregion

                #region "User Chat"

                //Chat Interactions
                try
                {
                    if (ConvPart.chats != null)
                    {
                        foreach (RealCN.Chat ConvCall in ConvPart.chats)
                        {
                            if (string.IsNullOrEmpty(ConvPart?.userId))
                            {
                                ConvDetails = new ConversationDetails();
                                ConvDetails.Id = ConvPart.userId;
                                ConvDetails.ConversationId = RealConversation.id;
                                ConvDetails.Direction = "inbound";
                                ConvDetails.Purpose = ConvPart.purpose;
                                ConvDetails.QueueId = ConvPart.queueId;
                                ConvDetails.Mediatype = "chat";
                                ConvDetails.State = ConvCall.state;
                                ConvDetails.Held = ConvCall.held;
                                if (ConvCall.state == "connected")
                                    ConvDetails.TalkTime = ConvPart.connectedTime.ToString();

                                if (ConvCall.afterCallWork != null)
                                {
                                    ConvDetails.ACWState = ConvCall.afterCallWork.state;
                                    if (ConvCall.afterCallWork.state == "pending")
                                    {
                                        ConvDetails.IsACW = true;
                                        ConvDetails.ACWStartTime = ConvCall.afterCallWork.startTime.ToString();
                                    }
                                    else
                                    {
                                        ConvDetails.IsACW = false;
                                        ConvDetails.ACWStartTime = null;
                                        ConvDetails.ACWState = null;
                                    }
                                }

                                if (ConvCall.held == false)
                                {
                                    ConvDetails.StartHoldTime = null;
                                }
                                else
                                {
                                    ConvDetails.StartHoldTime = ConvCall.startHoldTime.ToString();
                                }

                            }

                            //Console.WriteLine("Purpose:{0} Conv Id:{1} State:{2} User Id:{3} Media:Chat", ConvPart.purpose,
                            //                    RealConversation.id,
                            //                    ConvCall.state,
                            //                    ConvPart.userId);



                            if (ConvDetails != null)
                                SuccessRecord = AddUConversationRealTimeRow(ConvDetails);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("\n" + DateTime.Now + "\n UnHandled Error In Receiving Data Area:{3} \n:Error Code :{0}\n" +
                    "QC: Error Source:{1}\nInner Error:{2}\nEnding RealTime Application",
                                  ex.ToString(),
                                  ex.Source,
                                  ex.InnerException,
                                  "Chat Interaction Transfer");
                }

                #endregion

            }

            return Successful;
        }

        private Boolean TransQConv(string JsonString)
        {

            Boolean Successful = false;


            RealQCN.Conversations QueueConvs = new RealQCN.Conversations();
            QueueConvs = JsonConvert.DeserializeObject<RealQCN.Conversations>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });


            RealQCN.Eventbody RealConversation = QueueConvs.eventBody;

            foreach (RealQCN.Participant ConvPart in RealConversation.participants)
            {

                if (ConvPart.purpose == "acd" || ConvPart.purpose == "agent" || ConvPart.purpose == "user")
                {

                    //Voice Calls
                    if (ConvPart.calls != null)
                    {

                        //Console.WriteLine(JsonString);

                        foreach (RealQCN.Call ConvCall in ConvPart.calls)
                        {
                            QConversationDetails QueueConversation = new QConversationDetails();

                            QueueConversation.ConversationId = RealConversation.id;
                            QueueConversation.Purpose = ConvPart.purpose;
                            QueueConversation.Mediatype = "voice";
                            QueueConversation.Direction = ConvCall.direction;
                            QueueConversation.ConversationState = ConvCall.state;
                            QueueConversation.UserId = ConvPart.userId;
                            QueueConversation.QueueId = ConvPart.queueId;
                            if (ConvCall.state == "connected")
                                QueueConversation.TalkTime = ConvPart.connectedTime.ToString();



                            Console.Write("QC:");

                            //Console.WriteLine("Purpose:{0} Conv Id:{1} State:{2} User Id:{3} Queue Id {4}", ConvPart.purpose,
                            //            RealConversation.id,
                            //            ConvCall.state,
                            //            ConvPart.userId,
                            //            ConvPart.queueId
                            //            );
                            try
                            {
                                if (ConvPart.conversationRoutingData.skills != null)
                                {
                                    QueueConversation.Skill1 = ConvPart.conversationRoutingData.skills[0].id;
                                    QueueConversation.Skill2 = ConvPart.conversationRoutingData.skills[1].id;
                                    QueueConversation.Skill3 = ConvPart.conversationRoutingData.skills[2].id;
                                }

                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine(ex.ToString());
                            }

                            if (ConvCall.afterCallWork != null)
                            {
                                QueueConversation.ACWState = ConvCall.afterCallWork.state;
                                if (ConvCall.afterCallWork.state == "pending")
                                {
                                    QueueConversation.IsACW = true;
                                    QueueConversation.ACWStartTime = ConvCall.afterCallWork.startTime.ToString();
                                }
                                else
                                {
                                    QueueConversation.IsACW = false;
                                    QueueConversation.ACWStartTime = null;
                                    QueueConversation.ACWState = null;
                                }
                            }

                            Boolean SuccessRecord = AddQConversationRealTimeRow(QueueConversation);

                        }


                    }

                    //Emails

                    if (ConvPart.emails != null)
                    {
                        foreach (RealQCN.Email ConvCall in ConvPart.emails)
                        {

                            if (ConvPart.userId != null)
                            {
                                //Console.WriteLine("Purpose:{0} Conv Id:{1} State:{2} User Id:{3} Media:Email", ConvPart.purpose,
                                //                    RealConversation.id,
                                //                    ConvCall.state,
                                //                    ConvPart.userId);
                                QConversationDetails QueueConversation = new QConversationDetails();
                                QueueConversation.UserId = ConvPart.userId;
                                QueueConversation.ConversationId = RealConversation.id;
                                QueueConversation.Direction = ConvCall.direction;
                                QueueConversation.Purpose = ConvPart.purpose;
                                QueueConversation.QueueId = ConvPart.queueId;
                                QueueConversation.Mediatype = "email";
                                QueueConversation.ConversationState = ConvCall.state;
                                //QueueConversation.Held = ConvCall.held;
                                if (ConvCall.state == "connected")
                                    QueueConversation.TalkTime = ConvPart.connectedTime.ToString();

                                Console.Write("QE:");

                                try
                                {
                                    if (ConvPart.conversationRoutingData.skills != null)
                                    {

                                        if (ConvPart.purpose == "agent")
                                            Console.WriteLine("AGENT SKILL:", ConvPart.conversationRoutingData.skills[0].id);

                                        QueueConversation.Skill1 = ConvPart.conversationRoutingData.skills[0].id;
                                        QueueConversation.Skill2 = ConvPart.conversationRoutingData.skills[1].id;
                                        QueueConversation.Skill3 = ConvPart.conversationRoutingData.skills[2].id;
                                    }

                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine(ex.ToString());
                                }

                                if (ConvCall.afterCallWork != null)
                                {
                                    QueueConversation.ACWState = ConvCall.afterCallWork.state;
                                    if (ConvCall.afterCallWork.state == "pending")
                                    {
                                        QueueConversation.IsACW = true;
                                        QueueConversation.ACWStartTime = ConvCall.afterCallWork.startTime.ToString();
                                    }
                                    else
                                    {
                                        QueueConversation.IsACW = false;
                                        QueueConversation.ACWStartTime = null;
                                        QueueConversation.ACWState = null;
                                    }
                                }

                                //if (ConvCall.held == false)
                                //{
                                //    QueueConversation.StartHoldTime = null;
                                //}
                                //else
                                //{
                                //    QueueConversation.StartHoldTime = ConvCall.startHoldTime.ToString();
                                //}
                                Boolean SuccessRecord = AddQConversationRealTimeRow(QueueConversation);
                            }

                        }
                    }
                }

            }

            return Successful;
        }

        public void ReInitializeGCKey()
        {
            Console.WriteLine("GC User Realtime Notification Data - ReInit API Key");
            Boolean Successful = GetAuthAPIKey();
        }

        private Boolean AddUConversationRealTimeRow(ConversationDetails ConvDetails)
        {
            Boolean Successful = false;

            lock (DtRealTimeData)
            {
                DataRow[] DRConversation = DtRealTimeData.Select("keyid='" + ConvDetails.Id + "|" + ConvDetails.ConversationId + "'");

                foreach (DataRow DrTemp in DRConversation)
                {
                    DtRealTimeData.Rows.Remove(DrTemp);
                }

                DtRealTimeData.AcceptChanges();

                DataRow DRConversationNew = DtRealTimeData.NewRow();


                DRConversationNew["keyid"] = ConvDetails.Id + "|" + ConvDetails.ConversationId;

                DRConversationNew["id"] = ConvDetails.Id;
                DRConversationNew["conversationid"] = ConvDetails.ConversationId;
                DRConversationNew["media"] = ConvDetails.Mediatype;

                if (ConvDetails.State != "connected")
                    DRConversationNew["talktime"] = System.DBNull.Value;
                else
                    DRConversationNew["talktime"] = ConvDetails.TalkTime;

                DRConversationNew["queueid"] = ConvDetails.QueueId;
                DRConversationNew["direction"] = ConvDetails.Direction;
                DRConversationNew["conversationstate"] = ConvDetails.State;
                DRConversationNew["actingAs"] = ConvDetails.Purpose;
                DRConversationNew["acwstate"] = ConvDetails.IsACW;

                if (ConvDetails.IsACW != false)
                    DRConversationNew["acwtime"] = ConvDetails.ACWStartTime;
                else
                    DRConversationNew["acwtime"] = System.DBNull.Value;

                DRConversationNew["heldstate"] = ConvDetails.Held;

                if (ConvDetails.Held != false)
                    DRConversationNew["heldtime"] = ConvDetails.StartHoldTime;
                else
                    DRConversationNew["heldtime"] = System.DBNull.Value;

                DtRealTimeData.Rows.Add(DRConversationNew);

                DtRealTimeData.AcceptChanges();

                Successful = true;
            }

            return Successful;
        }

        private Boolean AddQConversationRealTimeRow(QConversationDetails ConvDetails)
        {
            Boolean Successful = false;
            Boolean IsNewRecord = false;

            lock (DtRealTimeData)
            {
                DataRow DRConversation = DtRealTimeData.Select("conversationid = '" + ConvDetails.ConversationId + "'").FirstOrDefault();

                //lock(DRConversation)
                //{
                if (DRConversation == null && IsNewRecord == false)
                {

                    DRConversation = DtRealTimeData.NewRow();
                    IsNewRecord = true;
                }
                if (IsNewRecord)
                    DRConversation["keyid"] = ConvDetails.ConversationId;

                DRConversation["conversationid"] = ConvDetails.ConversationId;
                DRConversation["media"] = ConvDetails.Mediatype;

                if (ConvDetails.ConversationState != "connected")
                    DRConversation["talktime"] = System.DBNull.Value;
                else
                    DRConversation["talktime"] = ConvDetails.TalkTime;

                DRConversation["queueid"] = ConvDetails.QueueId;
                DRConversation["userid"] = ConvDetails.UserId;
                DRConversation["direction"] = ConvDetails.Direction;
                DRConversation["state"] = ConvDetails.ConversationState;
                DRConversation["actingAs"] = ConvDetails.Purpose;

                if (String.IsNullOrEmpty(ConvDetails.Skill1) == false)
                {
                    DRConversation["skill1"] = ConvDetails.Skill1;
                    DRConversation["skill2"] = ConvDetails.Skill2;
                    DRConversation["skill3"] = ConvDetails.Skill3;

                }

                DRConversation["acwstate"] = ConvDetails.IsACW;

                if (ConvDetails.IsACW != false)
                    DRConversation["acwtime"] = ConvDetails.ACWStartTime;
                else
                    DRConversation["acwtime"] = System.DBNull.Value;
                //}



                if (IsNewRecord == true)
                {
                    Console.Write("Q+:");
                    DtRealTimeData.Rows.Add(DRConversation);
                    IsNewRecord = false;
                }
                Console.Write("QU:");
                DtRealTimeData.AcceptChanges();
            }

            return Successful;
        }

        internal void CreateUserConvSubs(WebSocketDetail WebSock)
        {
            Console.WriteLine("Creating Conversation Channel For User Conversations");

            //Create Subscriptions for Notification - Conversations.

            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            foreach (DataRow DRRow in DTUsers.Rows)
            {
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".conversations\"},");
                ++Counter;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ReturnJson(URL, JSONBodyString);

            }
        }

        internal void CreateQueueConvSubs(WebSocketDetail WebSock)
        {
            Console.WriteLine("Creating Conversation Channel For Queue Conversations");

            //Create Subscriptions for Notification - Conversations.

            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            foreach (DataRow DRRow in DTQueues.Rows)
            {
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.routing.queues." + DRRow["id"].ToString() + ".conversations\"},");
                ++Counter;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ReturnJson(URL, JSONBodyString);

            }
        }

        internal void CreateUserActivitySubs(WebSocketDetail WebSock)
        {
            Console.WriteLine("Creating Activity Channel For Users");

            //Create Subscriptions for Notification - Activity and WFM.

            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            foreach (DataRow DRRow in DtRealTimeData.Rows)
            {
                //SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".workforcemanagement.adherence\"}," +
                //                        " \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".activity\"},");

                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".activity\"},");
                ++Counter;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ReturnJson(URL, JSONBodyString);

            }


        }

        internal void CreateUserAdherenceSubs(WebSocketDetail WebSock)
        {
            Console.WriteLine("Creating Adherence Channel For Users");

            //Create Subscriptions for Notification - Activity and WFM.

            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            foreach (DataRow DRRow in DtRealTimeData.Rows)
            {
                //SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".workforcemanagement.adherence\"}," +
                //                        " \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".activity\"},");

                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".activity\"},");
                ++Counter;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ReturnJson(URL, JSONBodyString);

            }


        }

        internal void CreateUserCallSubs(WebSocketDetail WebSock)
        {

            Console.WriteLine("Creating Call Summary Channel For Users");

            //Create Subscriptions for Notification - Calls.

            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            foreach (DataRow DRRow in DtRealTimeData.Rows)
            {
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".conversationsummary\"},");
                ++Counter;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ReturnJson(URL, JSONBodyString);

                //Console.WriteLine(JSONBodyString);

            }


        }

    }


    public class AlertObject
    {
        public string topicName { get; set; }
        public string version { get; set; }

    }

    public class ThreadDetails
    {
        public int ThreadId { get; set; }
        public string ThreadName { get; set; }
    }

    public class ConversationDetails
    {
        public string Id { get; set; }
        public string ConversationId { get; set; }
        public string ConversationState { get; set; }
        public string Mediatype { get; set; }
        public string Direction { get; set; }
        public Boolean IsACW { get; set; }
        public string ACWState { get; set; }
        public string ACWStartTime { get; set; }
        public string ActingAS { get; set; }
        public string QueueId { get; set; }
        public string ConvHeld { get; set; }
        public string ConvHeldTime { get; set; }
        public string State { get; set; }
        public Boolean Held { get; set; }
        public string Purpose { get; set; }
        public string StartHoldTime { get; set; }
        public string TalkTime { get; set; }

    }

    public class QConversationDetails
    {
        public string ConversationId { get; set; }
        public string ConversationState { get; set; }
        public string QueueId { get; set; }
        public string UserId { get; set; }
        public string Mediatype { get; set; }
        public string Direction { get; set; }
        public string Purpose { get; set; }
        public string Skill1 { get; set; }
        public string Skill2 { get; set; }
        public string Skill3 { get; set; }
        public string TalkTime { get; set; }

        public Boolean IsACW { get; set; }
        public string ACWState { get; set; }
        public string ACWStartTime { get; set; }
        public string ActingAS { get; set; }

        public string ConvHeld { get; set; }
        public string ConvHeldTime { get; set; }
        public string State { get; set; }
        public Boolean Held { get; set; }

        public string StartHoldTime { get; set; }


    }
}

namespace RealUserPushActivityDef
{

    public class Activity
    {
        public string topicName { get; set; }
        public string version { get; set; }
        public EventbodyUser eventBody { get; set; }
        public Metadata metadata { get; set; }
    }

    public class EventbodyUser
    {
        public string id { get; set; }
        public Routingstatus routingStatus { get; set; }
        public Presence presence { get; set; }
        public Outofoffice outOfOffice { get; set; }
        public string[] activeQueueIds { get; set; }
        public DateTime dateActiveQueuesChanged { get; set; }
    }

    public class Routingstatus
    {
        public string status { get; set; }
        public DateTime startTime { get; set; }
    }

    public class Presence
    {
        public Presencedefinition presenceDefinition { get; set; }
        public string presenceMessage { get; set; }
        public DateTime modifiedDate { get; set; }
    }

    public class Presencedefinition
    {
        public string id { get; set; }
        public string systemPresence { get; set; }
    }

    public class Outofoffice
    {
        public bool active { get; set; }
        public DateTime modifiedDate { get; set; }
    }

    public class Metadata
    {
        public string CorrelationId { get; set; }
    }

    public class Adherence
    {
        public string topicName { get; set; }
        public string version { get; set; }
        public EventbodyAdherence eventBody { get; set; }
        public Metadata metadata { get; set; }
    }

    public class EventbodyAdherence
    {
        public User user { get; set; }
        public string managementUnitId { get; set; }
        public string scheduledActivityCategory { get; set; }
        public string systemPresence { get; set; }
        public string organizationSecondaryPresenceId { get; set; }
        public string routingStatus { get; set; }
        public string actualActivityCategory { get; set; }
        public bool isOutOfOffice { get; set; }
        public string adherenceState { get; set; }
        public string impact { get; set; }
        public DateTime adherenceChangeTime { get; set; }
        public DateTime presenceUpdateTime { get; set; }
        public Activequeue[] activeQueues { get; set; }
        public DateTime activeQueuesModifiedTime { get; set; }
        public bool removedFromManagementUnit { get; set; }
    }

    public class User
    {
        public string id { get; set; }
    }

    public class Activequeue
    {
        public string id { get; set; }
    }

}

namespace RealUserPushCallStatsDef
{

    public class CallStats
    {
        public string topicName { get; set; }
        public string version { get; set; }
        public Eventbody eventBody { get; set; }
        public Metadata metadata { get; set; }
    }

    public class Eventbody
    {
        public Call call { get; set; }
        public Callback callback { get; set; }
        public Email email { get; set; }
        public Message message { get; set; }
        public Chat chat { get; set; }
        public Socialexpression socialExpression { get; set; }
        public Video video { get; set; }
    }

    public class Call
    {
        public Contactcenter contactCenter { get; set; }
        public Enterprise enterprise { get; set; }
    }

    public class Contactcenter
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Callback
    {
        public Contactcenter1 contactCenter { get; set; }
        public Enterprise1 enterprise { get; set; }
    }

    public class Contactcenter1
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise1
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Email
    {
        public Contactcenter2 contactCenter { get; set; }
        public Enterprise2 enterprise { get; set; }
    }

    public class Contactcenter2
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise2
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Message
    {
        public Contactcenter3 contactCenter { get; set; }
        public Enterprise3 enterprise { get; set; }
    }

    public class Contactcenter3
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise3
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Chat
    {
        public Contactcenter4 contactCenter { get; set; }
        public Enterprise4 enterprise { get; set; }
    }

    public class Contactcenter4
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise4
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Socialexpression
    {
        public Contactcenter5 contactCenter { get; set; }
        public Enterprise5 enterprise { get; set; }
    }

    public class Contactcenter5
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise5
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Video
    {
        public Contactcenter6 contactCenter { get; set; }
        public Enterprise6 enterprise { get; set; }
    }

    public class Contactcenter6
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise6
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Metadata
    {
        public string CorrelationId { get; set; }
    }


}

namespace RealUserPushConversations
{


    public class Conversations
    {
        public string topicName { get; set; }
        public string version { get; set; }
        public Eventbody eventBody { get; set; }
        public Metadata metadata { get; set; }
    }

    public class Eventbody
    {
        public string id { get; set; }
        public Participant[] participants { get; set; }
        public string recordingState { get; set; }
        public string address { get; set; }
    }

    public class Participant
    {
        public string id { get; set; }
        public DateTime connectedTime { get; set; }
        public string name { get; set; }
        public string queueId { get; set; }
        public string purpose { get; set; }
        public string address { get; set; }
        public bool wrapupRequired { get; set; }
        public bool wrapupExpected { get; set; }
        public Attributes attributes { get; set; }
        public Call[] calls { get; set; }
        public Email[] emails { get; set; }
        public Callback[] callbacks { get; set; }
        public Chat[] chats { get; set; }
        public Additionalproperties additionalProperties { get; set; }
        public DateTime endTime { get; set; }
        public Conversationroutingdata conversationRoutingData { get; set; }
        public string userId { get; set; }
        public string wrapupPrompt { get; set; }
        public Wrapup wrapup { get; set; }
        public DateTime startAcwTime { get; set; }
        public DateTime endAcwTime { get; set; }
        public int alertingTimeoutMs { get; set; }
    }

    public class Attributes
    {
        public string ivrSkills { get; set; }
        public string ivrLanguageSkill { get; set; }
        public string ivrPriority { get; set; }
    }

    public class Additionalproperties
    {
    }

    public class Conversationroutingdata
    {
        public Queue queue { get; set; }
        public Language language { get; set; }
        public int priority { get; set; }
        public Skill[] skills { get; set; }
    }

    public class Queue
    {
        public string id { get; set; }
    }

    public class Language
    {
        public string id { get; set; }
    }

    public class Skill
    {
        public string id { get; set; }
    }

    public class Wrapup
    {
        public string code { get; set; }
        public string notes { get; set; }
        public int durationSeconds { get; set; }
        public DateTime endTime { get; set; }
        public Additionalproperties1 additionalProperties { get; set; }
    }

    public class Additionalproperties1
    {
    }

    public class Callback
    {
        public string state { get; set; }
        public string id { get; set; }
        public string disconnectType { get; set; }
        public bool held { get; set; }
        public DateTime startHoldTime { get; set; }
        public DateTime endHoldTime { get; set; }
        public string[] callbackNumbers { get; set; }
        public string callbackUserName { get; set; }
        public string scriptId { get; set; }
        public string peerId { get; set; }
        public string direction { get; set; }
        public bool externalCampaign { get; set; }
        public bool skipEnabled { get; set; }
        public string provider { get; set; }
        public int timeoutSeconds { get; set; }
        public DateTime connectedTime { get; set; }
        public DateTime disconnectedTime { get; set; }
        public bool afterCallWorkRequired { get; set; }
        public Aftercallwork afterCallWork { get; set; }
    }

    public class Chat
    {
        public string state { get; set; }
        public string id { get; set; }
        public string disconnectType { get; set; }
        public bool held { get; set; }
        public DateTime startHoldTime { get; set; }
        public DateTime endHoldTime { get; set; }
        public string[] callbackNumbers { get; set; }
        public string callbackUserName { get; set; }
        public string scriptId { get; set; }
        public string peerId { get; set; }
        public string direction { get; set; }
        public bool externalCampaign { get; set; }
        public bool skipEnabled { get; set; }
        public string provider { get; set; }
        public int timeoutSeconds { get; set; }
        public DateTime connectedTime { get; set; }
        public DateTime disconnectedTime { get; set; }
        public bool afterCallWorkRequired { get; set; }
        public Aftercallwork afterCallWork { get; set; }
    }

    public class Email
    {
        public string id { get; set; }
        public string state { get; set; }
        public bool held { get; set; }
        public DateTime startHoldTime { get; set; }
        public DateTime endHoldTime { get; set; }
        public bool autoGenerated { get; set; }
        public string provider { get; set; }
        public string peerId { get; set; }
        public int messagesSent { get; set; }
        public DateTime connectedTime { get; set; }
        public string messageId { get; set; }
        public string direction { get; set; }
        public bool spam { get; set; }
        public bool afterCallWorkRequired { get; set; }
        public Additionalproperties1 additionalProperties { get; set; }
        public string disconnectType { get; set; }
        public DateTime disconnectedTime { get; set; }
        public Disconnectreason[] disconnectReasons { get; set; }
        public Errorinfo errorInfo { get; set; }
        public string scriptId { get; set; }
        public Aftercallwork afterCallWork { get; set; }
    }

    public class Call
    {
        public string id { get; set; }
        public string state { get; set; }
        public bool recording { get; set; }
        public string recordingState { get; set; }
        public bool muted { get; set; }
        public bool confined { get; set; }
        public bool held { get; set; }
        public DateTime startHoldTime { get; set; }
        public DateTime endHoldTime { get; set; }
        public string direction { get; set; }
        public Self self { get; set; }
        public Other other { get; set; }
        public string provider { get; set; }
        public DateTime connectedTime { get; set; }
        public bool afterCallWorkRequired { get; set; }
        public Additionalproperties4 additionalProperties { get; set; }
        public string disconnectType { get; set; }
        public string peerId { get; set; }
        public DateTime disconnectedTime { get; set; }
        public Disconnectreason[] disconnectReasons { get; set; }
        public Errorinfo errorInfo { get; set; }
        public string scriptId { get; set; }
        public Aftercallwork afterCallWork { get; set; }
    }

    public class Self
    {
        public string name { get; set; }
        public string nameRaw { get; set; }
        public string addressNormalized { get; set; }
        public string addressRaw { get; set; }
        public string addressDisplayable { get; set; }
        public Additionalproperties2 additionalProperties { get; set; }
    }

    public class Additionalproperties2
    {
    }

    public class Other
    {
        public string name { get; set; }
        public string nameRaw { get; set; }
        public string addressNormalized { get; set; }
        public string addressRaw { get; set; }
        public string addressDisplayable { get; set; }
        public Additionalproperties3 additionalProperties { get; set; }
    }

    public class Additionalproperties3
    {
    }

    public class Additionalproperties4
    {
    }

    public class Errorinfo
    {
        public string code { get; set; }
        public string message { get; set; }
        public string messageWithParams { get; set; }
        public Messageparams messageParams { get; set; }
        public Additionalproperties5 additionalProperties { get; set; }
    }

    public class Messageparams
    {
        public string type { get; set; }
        public string sessionId { get; set; }
    }

    public class Additionalproperties5
    {
    }

    public class Aftercallwork
    {
        public string state { get; set; }
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
    }

    public class Disconnectreason
    {
        public string type { get; set; }
        public int code { get; set; }
        public string phrase { get; set; }
    }

    public class Metadata
    {
        public string CorrelationId { get; set; }
    }


}

namespace RealQueuePushConversations
{

    public class Conversations
    {
        public string topicName { get; set; }
        public string version { get; set; }
        public Eventbody eventBody { get; set; }
        public Metadata metadata { get; set; }
    }

    public class Eventbody
    {
        public string id { get; set; }
        public Participant[] participants { get; set; }
        public string recordingState { get; set; }
        public string address { get; set; }
    }

    public class Participant
    {
        public string id { get; set; }
        public DateTime connectedTime { get; set; }
        public DateTime endTime { get; set; }
        public string name { get; set; }
        public string queueId { get; set; }
        public string purpose { get; set; }
        public string address { get; set; }
        public bool wrapupRequired { get; set; }
        public bool wrapupExpected { get; set; }
        public Attributes attributes { get; set; }
        public Call[] calls { get; set; }
        public Email[] emails { get; set; }
        public Additionalproperties additionalProperties { get; set; }
        public Conversationroutingdata conversationRoutingData { get; set; }
        public string userId { get; set; }
        public string wrapupPrompt { get; set; }
        public int wrapupTimeoutMs { get; set; }
        public Wrapup wrapup { get; set; }
        public DateTime startAcwTime { get; set; }
        public DateTime endAcwTime { get; set; }
        public int alertingTimeoutMs { get; set; }
    }

    public class Attributes
    {
        public string ivrSkills { get; set; }
        public string ivrLanguageSkill { get; set; }
        public string ivrPriority { get; set; }
    }

    public class Additionalproperties
    {
    }

    public class Conversationroutingdata
    {
        public Queue queue { get; set; }
        public Language language { get; set; }
        public int priority { get; set; }
        public Skill[] skills { get; set; }
    }

    public class Queue
    {
        public string id { get; set; }
    }

    public class Language
    {
    }

    public class Skill
    {
        public string id { get; set; }
    }

    public class Wrapup
    {
        public string code { get; set; }
        public int durationSeconds { get; set; }
        public DateTime endTime { get; set; }
        public Additionalproperties1 additionalProperties { get; set; }
    }

    public class Additionalproperties1
    {
    }


    public class Email
    {
        public string id { get; set; }
        public string state { get; set; }
        public bool held { get; set; }
        public DateTime startHoldTime { get; set; }
        public DateTime endHoldTime { get; set; }
        public bool autoGenerated { get; set; }
        public string provider { get; set; }
        public string peerId { get; set; }
        public int messagesSent { get; set; }
        public DateTime connectedTime { get; set; }
        public string messageId { get; set; }
        public string direction { get; set; }
        public bool spam { get; set; }
        public bool afterCallWorkRequired { get; set; }
        public Additionalproperties1 additionalProperties { get; set; }
        public string disconnectType { get; set; }
        public DateTime disconnectedTime { get; set; }
        public Disconnectreason[] disconnectReasons { get; set; }
        public Errorinfo errorInfo { get; set; }
        public string scriptId { get; set; }
        public Aftercallwork afterCallWork { get; set; }
    }

    public class Call
    {
        public string id { get; set; }
        public string state { get; set; }
        public bool recording { get; set; }
        public string recordingState { get; set; }
        public bool muted { get; set; }
        public bool confined { get; set; }
        public bool held { get; set; }
        public string disconnectType { get; set; }
        public string direction { get; set; }
        public Self self { get; set; }
        public Other other { get; set; }
        public string provider { get; set; }
        public DateTime connectedTime { get; set; }
        public DateTime disconnectedTime { get; set; }
        public Disconnectreason[] disconnectReasons { get; set; }
        public bool afterCallWorkRequired { get; set; }
        public Additionalproperties4 additionalProperties { get; set; }
        public string peerId { get; set; }
        public string scriptId { get; set; }
        public Aftercallwork afterCallWork { get; set; }
    }

    public class Self
    {
        public string name { get; set; }
        public string nameRaw { get; set; }
        public string addressNormalized { get; set; }
        public string addressRaw { get; set; }
        public string addressDisplayable { get; set; }
        public Additionalproperties2 additionalProperties { get; set; }
    }

    public class Additionalproperties2
    {
    }

    public class Other
    {
        public string name { get; set; }
        public string nameRaw { get; set; }
        public string addressNormalized { get; set; }
        public string addressRaw { get; set; }
        public string addressDisplayable { get; set; }
        public Additionalproperties3 additionalProperties { get; set; }
    }

    public class Additionalproperties3
    {
    }

    public class Additionalproperties4
    {
    }

    public class Aftercallwork
    {
        public string state { get; set; }
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
    }

    public class Disconnectreason
    {
        public string type { get; set; }
        public int code { get; set; }
        public string phrase { get; set; }
    }

    public class Metadata
    {
        public string CorrelationId { get; set; }
    }

    public class Errorinfo
    {
        public string code { get; set; }
        public string message { get; set; }
        public string messageWithParams { get; set; }
        public Messageparams messageParams { get; set; }
        public Additionalproperties5 additionalProperties { get; set; }
    }

    public class Messageparams
    {
        public string type { get; set; }
        public string sessionId { get; set; }
    }

    public class Additionalproperties5
    {
    }

}
namespace RealQueuePushObservations
{

    public class Observations
    {
        public string topicName { get; set; }
        public string version { get; set; }
        public Eventbody eventBody { get; set; }
        public Metadata metadata { get; set; }
    }
    public class Metadata
    {
        public string CorrelationId { get; set; }
    }
    public class Eventbody
    {
        public string id { get; set; }
        public Data[] data { get; set; }
        public Group group { get; set; }
        public string recordingState { get; set; }
        public string address { get; set; }
    }
    public class Stats
    {
        public double count { get; set; }
    }

    public class Observation
    {
        public DateTime observationDate { get; set; }
        public string conversationId { get; set; }
        public string sessionId { get; set; }
        public int routingPriority { get; set; }
        public string participantName { get; set; }
        public string userId { get; set; }
        public string direction { get; set; }
        public string ani { get; set; }
        public string dnis { get; set; }
    }

    public class Data
    {
        public string interval { get; set; } // Added interval property
        public Metrics[] metrics { get; set; } // Changed Observation[] to Metrics[] to match JSON structure
        public Observation[] observations { get; set; }
    }

    public class Metrics
    {
        public string metric { get; set; }
        public Stats stats { get; set; }
    }

    public class Group
    {
        public string queueId { get; set; }
        public string mediaType { get; set; }
    }

    public class Result
    {
        public Group group { get; set; }
        public Data[] data { get; set; }
    }
    
}
// spell-checker: ignore: outofoffice