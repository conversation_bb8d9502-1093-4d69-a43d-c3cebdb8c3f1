using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using GCRealTime.Core;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using GenesysCloudUtils;
using GenesysCloudUtils.WebSocket; // Add this for WebSocketDetail
using RealUA = RealUserPushActivityDef;
using Newtonsoft.Json.Linq;

namespace GCRealTime.Services
{
    public class UserActivityService : BaseRealTimeService, IRealtimeService
    {
        private DataTable _userDetails = new DataTable();
        private DataTable _userData = new DataTable();
        // Removed _userPresenceTable
        private List<WebSocketDetail> _webSocketList = new List<WebSocketDetail>();
        private List<Task> _socketTasks = new List<Task>();
        private CancellationTokenSource _cts;
        private bool _writeUserData = false;
        private readonly JsonUtils _jsonUtils;
        private bool _isRunning = false;
        // Make the counter fields static to persist across instances and recoveries
        private static long _processedEventsCount = 0;
        private static DateTime _lastProcessedEventTime = DateTime.MinValue;
        private readonly object _statsLock = new object(); // Add lock object for thread safety

        public UserActivityService(ILogger _logger, DBUtils.DBUtils dbAdapter) : base(_logger, dbAdapter)
        {
            _jsonUtils = new JsonUtils(_logger);
        }

        public override void Initialize()
        {
            _logger?.LogInformation("[UserActivity][Init] Beginning UserActivityService initialization");
            base.Initialize();
            
            if (!TableExists("userRealTimeData"))
            {
                _logger?.LogError("[UserActivity][Init] Required table 'userRealTimeData' does not exist");
                // Continue anyway - the service will try to work with what's available
            }
            
            // Truncate the userRealTimeData table
            try
            {
                _logger?.LogInformation("[UserActivity][Init] Truncating userRealTimeData table");
                string truncateQuery = "TRUNCATE TABLE userRealTimeData";
                DBAdapter.ExecuteSqlNonQuery(truncateQuery); // Use ExecuteSqlNonQuery to execute the truncate query
                _logger?.LogInformation("[UserActivity][Init] Successfully truncated userRealTimeData table");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Init] Failed to truncate userRealTimeData table");
                throw;
            }
            
            // Log the current counter value on initialization
            _logger?.LogInformation("[UserActivity][Init] Starting with existing event count: {EventCount}", 
                _processedEventsCount);
            
            // Check if userDetails table exists - this is required
            if (!TableExists("userDetails"))
            {
                _logger?.LogError("[UserActivity][Init] Required table 'userDetails' does not exist");
                throw new InvalidOperationException("Required table 'userDetails' does not exist");
            }
            
            // Get the active users
            _userDetails = GetUsers();
            
            if (_userDetails == null || _userDetails.Rows.Count == 0)
            {
                _logger?.LogWarning("[UserActivity][Init] No active users found in userDetails table");
            }
            
            // Create the activity data table
            _userData = CreateActivityTable();
            
            // Populate _userData with user details
            if (_userDetails != null && _userDetails.Rows.Count > 0)
            {
                foreach (DataRow userRow in _userDetails.Rows)
                {
                    DataRow newRow = _userData.NewRow();
                    newRow["id"] = userRow["id"];
                    newRow["name"] = userRow["name"];
                    newRow["jabberId"] = userRow["jabberId"];
                    newRow["email"] = userRow["email"];
                    newRow["state"] = userRow["state"];
                    newRow["title"] = userRow["title"];
                    newRow["username"] = userRow["username"];
                    newRow["department"] = userRow["department"];
                    newRow["updated"] = DateTime.UtcNow; // Set the updated timestamp
                    _userData.Rows.Add(newRow);
                }
                _logger?.LogInformation("[UserActivity][Init] Populated activity table with {Count} users", _userDetails.Rows.Count);
            }

            // Fetch presence details from API and update _userData
            try
            {
                int currentPage = 1;
                bool hasMorePages = true;

                while (hasMorePages)
                {
                    string apiUrl = $"/api/v2/users?state=active&pageSize=500&pageNumber={currentPage}&expand=presence%2CroutingStatus%2Cgeolocation%2CconversationSummary&sortOrder=asc";
                    _logger?.LogInformation("[UserActivity][Init] Fetching user presence details from API, page {Page}", currentPage);

                    string response = _jsonUtils.JsonReturnAsync(apiUrl).GetAwaiter().GetResult().ToString();
                    var jsonResponse = JsonConvert.DeserializeObject<dynamic>(response);

                    if (jsonResponse?.entities != null)
                    {
                        foreach (var user in jsonResponse.entities)
                        {
                            string userId = user.id;
                            DataRow userRow = _userData.Select($"id = '{userId}'").FirstOrDefault();

                            if (userRow != null && user.presence != null)
                            {
                                userRow["routingstatus"] = user.routingStatus?.status ?? DBNull.Value;
                                userRow["routstarttime"] = user.routingStatus?.startTime ?? DBNull.Value;
                                userRow["systemPresence"] = user.presence.presenceDefinition?.systemPresence ?? DBNull.Value;
                                userRow["presenceId"] = user.presence.presenceDefinition?.id ?? DBNull.Value;
                                userRow["presstarttime"] = user.presence.modifiedDate ?? DBNull.Value;
                                userRow["updated"] = DateTime.UtcNow;
                            }
                        }

                        hasMorePages = jsonResponse.pageNumber < jsonResponse.totalPages;
                        currentPage++;
                    }
                    else
                    {
                        hasMorePages = false;
                    }
                }

                _logger?.LogInformation("[UserActivity][Init] Successfully updated presence details for users");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Init] Failed to fetch or update user presence details");
            }
            
            _logger?.LogInformation("[UserActivity][Init] Activity service initialized successfully");
        }

        // Helper method to check if a table exists
        private bool CheckIfTableExists(string tableName)
        {
            try
            {
                string query = DBAdapter.DBType switch
                {
                    CSG.Adapter.Configuration.DatabaseType.MSSQL =>
                        $"SELECT CASE WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{tableName}') THEN 1 ELSE 0 END",
                    CSG.Adapter.Configuration.DatabaseType.MySQL =>
                        $"SELECT COUNT(1) FROM information_schema.tables WHERE table_name = '{tableName}'",
                    CSG.Adapter.Configuration.DatabaseType.PostgreSQL =>
                        $"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = '{tableName.ToLower()}') as exists",
                    _ => throw new NotSupportedException($"Unsupported database type: {DBAdapter.DBType}")
                };

                object result = DBAdapter.ExecuteScalar(query);
                return Convert.ToBoolean(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][DB] Error checking if table {TableName} exists", tableName);
                return false;
            }
        }

        public override void Start()
        {
            _logger?.LogInformation("[UserActivity][Start] Starting UserActivityService at {Time}", DateTime.UtcNow);
            _cts = new CancellationTokenSource();
            _socketTasks = new List<Task>();
            _webSocketList.Clear();
            WebSocketManagers.Clear();

            // Add null checks before using _userDetails
            if (_userDetails == null)
            {
                _logger?.LogError("[UserActivity][Start] UserDetails is null - cannot start service");
                throw new InvalidOperationException("UserDetails is null - cannot start service");
            }

            if (_userDetails.Rows.Count > MAX_TOPICS_PER_SUBSCRIPTION)
            {
                var chunks = ChunkDataTable(_userDetails, MAX_TOPICS_PER_SUBSCRIPTION);
                _logger?.LogInformation("[UserActivity][Start] Splitting user activity into {ChannelCount} channels due to {UserCount} total users", chunks.Count, _userDetails.Rows.Count);
                int chunkIndex = 1;
                foreach (var chunk in chunks)
                {
                    string channelName = $"userActivity-{chunkIndex}";
                    // Add null check for the socket return
                    WebSocketDetail socket = CreateChannel(channelName);
                    if (socket == null)
                    {
                        _logger?.LogError("[UserActivity][Start] Failed to create channel for chunk {ChunkIndex}", chunkIndex);
                        Interlocked.Increment(ref _failedChannelAttempts);
                        CheckFailureThreshold();
                        continue; // Skip to next chunk
                    }
                    
                    _webSocketList.Add(socket);
                    var task = Task.Factory.StartNew(async () =>
                    {
                        try
                        {
                            _logger?.LogInformation("[UserActivity][Channel:{Channel}] Creating subscriptions for {ChunkSize} users", socket.id, chunk.Rows.Count);
                            await CreateUserActivitySubscriptionsForChunk(socket, chunk);
                            _logger?.LogInformation("[UserActivity][Channel:{Channel}] Starting WebSocket", socket.id);
                            CreateWebSocket(socket.connectUri, socket.id, channelName);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "[UserActivity][Start] Error in task for channel {ChannelId}", socket.id);
                            // Don't throw from background task
                        }
                    }, _cts.Token, TaskCreationOptions.LongRunning, TaskScheduler.Default);
                    _socketTasks.Add(task);
                    chunkIndex++;
                }
            }
            else
            {
                string channelName = "userActivity";
                // Add null check for the socket return
                WebSocketDetail socket = CreateChannel(channelName);
                if (socket == null)
                {
                    _logger?.LogError("[UserActivity][Start] Failed to create main channel");
                    Interlocked.Increment(ref _failedChannelAttempts);
                    CheckFailureThreshold();
                    return; // Exit if we can't create the main channel
                }
                
                _webSocketList.Add(socket);
                var task = Task.Factory.StartNew(async () =>
                {
                    try
                    {
                        _logger?.LogInformation("[UserActivity][Channel:{Channel}] Creating subscriptions", socket.id);
                        await CreateUserActivitySubscriptions(socket);
                        _logger?.LogInformation("[UserActivity][Channel:{Channel}] Starting WebSocket", socket.id);
                        CreateWebSocket(socket.connectUri, socket.id, channelName);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[UserActivity][Start] Error in task for main channel");
                        // Don't throw from background task
                    }
                }, _cts.Token, TaskCreationOptions.LongRunning, TaskScheduler.Default);
                _socketTasks.Add(task);
            }
            _isRunning = true;
            _logger?.LogInformation("[UserActivity][Start] Started successfully at {Time}", DateTime.UtcNow);
        }

        // Fix: Add method declaration for StopAsync
        public async Task StopAsync()
        {
            try
            {
                _logger?.LogInformation("[UserActivity][Stop] Stopping UserActivityService asynchronously at {Time}", DateTime.UtcNow);
                _cts?.Cancel();
                await Task.WhenAll(_socketTasks);
                _logger?.LogInformation("[UserActivity][Stop] Stopped successfully at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Stop] Failed to stop service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }

        public async Task InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("[UserActivity][Init] Initializing UserActivityService asynchronously at {Time}", DateTime.UtcNow);
                await Task.Run(() => Initialize());
                _logger?.LogInformation("[UserActivity][Init] Initialization complete at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Init] Failed to initialize service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }

        public async Task StartAsync(CancellationToken token)
        {
            try
            {
                _logger?.LogInformation("[UserActivity][Start] Starting UserActivityService asynchronously at {Time}", DateTime.UtcNow);
                await Task.Run(() => Start(), token);
                _isRunning = true;
                _logger?.LogInformation("[UserActivity][Start] Started successfully at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Start] Failed to start service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }

        // Health check implementation
        public async Task<bool> CheckHealthAsync()
        {
            // Implement directly instead of calling base method
            try 
            {
                _logger?.LogInformation("[UserActivity][Health] Performing health check");
                
                if (!_isRunning)
                {
                    _logger?.LogWarning("[UserActivity][Health] Service is not running");
                    return false;
                }
                
                bool hasActiveConnections = WebSocketManagers != null && 
                                           WebSocketManagers.Count > 0 &&
                                           WebSocketManagers.Any(m => m.IsConnected);
                                           
                if (!hasActiveConnections)
                {
                    _logger?.LogWarning("[UserActivity][Health] No active WebSocket connections");
                    
                    // Create new connections
                    string channelName = "userActivity-recovery";
                    WebSocketDetail socket = CreateChannel(channelName);
                    if (socket != null)
                    {
                        _webSocketList.Add(socket);
                        
                        // Create subscriptions
                        await CreateUserActivitySubscriptions(socket);
                        
                        // Start WebSocket
                        CreateWebSocket(socket.connectUri, socket.id, channelName);
                        
                        // Wait for connection to establish
                        await Task.Delay(2000);
                        
                        // Check if recovery was successful
                        hasActiveConnections = WebSocketManagers != null && 
                                              WebSocketManagers.Count > 0 &&
                                              WebSocketManagers.Any(m => m.IsConnected);
                                              
                        if (!hasActiveConnections)
                        {
                            _logger?.LogError("[UserActivity][Health] Recovery failed - service remains unhealthy");
                            return false;
                        }
                        
                        _logger?.LogInformation("[UserActivity][Health] Recovery successful - service is now healthy");
                    }
                    else
                    {
                        _logger?.LogError("[UserActivity][Health] Could not create recovery channel");
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Health] Health check failed");
                return false;
            }
        }

        // Add a method to expose the active connection count for better diagnostics
        public int GetActiveConnectionCount()
        {
            return WebSocketManagers?.Count(m => m.IsConnected) ?? 0;
        }

        // Enhance the metrics to include more detailed connection status
        public override Dictionary<string, object> GetServiceMetrics()
        {
            lock (_statsLock)
            {
                // Update the base class properties with our local counters
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }
            
            // Get base metrics (which will now include our updated values)
            var metrics = base.GetServiceMetrics();
            
            // Directly insert our values to be extra safe
            metrics["ProcessedEvents"] = _processedEventsCount;
            metrics["EventsProcessed"] = _processedEventsCount;
            metrics["LastEventTime"] = _lastProcessedEventTime;
            metrics["TotalWebSockets"] = WebSocketManagers?.Count ?? 0;
            metrics["ActiveWebSockets"] = GetActiveConnectionCount();
            metrics["ChannelsCreated"] = _webSocketList?.Count ?? 0;
            
            return metrics;
        }

        public override void FlushMetrics()
        {
            lock (_statsLock)
            {
                // Update the base class properties with our local counters
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }
            
            // Call base method second to avoid our values getting overwritten
            base.FlushMetrics();
            
            // Add explicit logging to show what metrics are being reported
            _logger?.LogInformation("[UserActivity][Metrics] Current metrics snapshot: Events={EventCount}, LastActivity={LastActivity}", 
                _processedEventsCount, _lastProcessedEventTime.ToString("yyyy-MM-dd HH:mm:ss"));
        }

        // Fix the IncrementEventsCount to be thread-safe and ensure it never resets counters
        private void IncrementEventsCount()
        {
            lock (_statsLock)
            {
                // Increment our counter directly
                _processedEventsCount++;
                _lastProcessedEventTime = DateTime.UtcNow;
                
                // Update base class properties
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }
            
            // Call the base implementation
            IncrementEventsProcessed();
        }

        protected override void RefreshChannels()
        {
            foreach (var channel in _webSocketList)
            {
                if (channel.IsExpired)
                {
                    _logger?.LogWarning($"Channel has expired: {channel}");
                }
                else if (channel.NeedsRefresh)
                {
                    if (channel.ReportName.Contains("-")) // Fixed: changed contains to Contains
                    {
                        string[] parts = channel.ReportName.Split('-');
                        if (parts.Length > 1 && int.TryParse(parts[1], out int chunkIndex) && chunkIndex > 0)
                        {
                            var chunks = ChunkDataTable(_userDetails, MAX_TOPICS_PER_SUBSCRIPTION);
                            if (chunkIndex <= chunks.Count)
                            {
                                DataTable userChunk = chunks[chunkIndex - 1];
                                RefreshSubscriptionsForChannel(channel, userChunk);
                            }
                        }
                    }
                    else
                    {
                        RefreshSubscriptionsForChannel(channel, _userDetails);
                    }
                }
            }
        }

        private void RefreshSubscriptionsForChannel(WebSocketDetail webSocket, DataTable userChunk)
        {
            try
            {
                _logger?.LogInformation("[UserActivity][Channel:{ChannelId}] Refreshing subscriptions", webSocket.id);

                string deleteUrl = $"/api/v2/notifications/channels/{webSocket.id}/subscriptions";
                var response = _jsonUtils.JsonReturn(_jsonUtils.ApiEndpoint + deleteUrl, null, "DELETE");

                CreateUserActivitySubscriptionsForChunk(webSocket, userChunk).Wait();

                _logger?.LogInformation("[UserActivity][Channel:{ChannelId}] Successfully refreshed subscriptions", webSocket.id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Channel:{ChannelId}] Failed to refresh subscriptions", webSocket.id);
            }
        }

        public async Task CreateUserActivitySubscriptions(WebSocketDetail webSocket)
        {
            if (webSocket == null)
            {
                _logger?.LogError("[UserActivity] Cannot create activity subscriptions - WebSocket is null");
                throw new ArgumentNullException(nameof(webSocket), "WebSocket cannot be null");
            }

            if (_userDetails == null)
            {
                _logger?.LogError("[UserActivity] Cannot create activity subscriptions - UserDetails is null");
                throw new InvalidOperationException("UserDetails is null");
            }

            await CreateUserActivitySubscriptionsForChunk(webSocket, _userDetails);
        }

        private async Task CreateUserActivitySubscriptionsForChunk(WebSocketDetail webSocket, DataTable userChunk)
        {
            if (webSocket == null)
            {
                _logger?.LogError("[UserActivity] Cannot create activity subscriptions for chunk - WebSocket is null");
                throw new ArgumentNullException(nameof(webSocket), "WebSocket cannot be null");
            }

            if (userChunk == null)
            {
                _logger?.LogError("[UserActivity] Cannot create activity subscriptions - UserChunk is null");
                throw new ArgumentNullException(nameof(userChunk), "User chunk cannot be null");
            }

            if (_jsonUtils == null)
            {
                _logger?.LogError("[UserActivity] Cannot create activity subscriptions - JsonUtils is null");
                throw new InvalidOperationException("JsonUtils is null");
            }

            _logger?.LogInformation("[UserActivity][Channel:{ChannelId}] Creating activity subscriptions for {Count} users", 
                webSocket.id, userChunk.Rows.Count);
            
            try
            {
                // Add more detailed logging
                StringBuilder topicList = new StringBuilder();
                int maxTopicsToLog = Math.Min(5, userChunk.Rows.Count); // Log first 5 topics at most
                
                for (int i = 0; maxTopicsToLog > i; i++)
                {
                    string userId = userChunk.Rows[i]["id"]?.ToString();
                    if (!string.IsNullOrEmpty(userId))
                    {
                        topicList.AppendLine($"v2.users.{userId}.activity");
                    }
                }
                
                if (userChunk.Rows.Count > maxTopicsToLog)
                {
                    topicList.AppendLine($"... and {userChunk.Rows.Count - maxTopicsToLog} more");
                }
                
                _logger?.LogDebug("[UserActivity] Subscribing to topics: {TopicList}", topicList.ToString());
                
                // Call the helper method with proper exception handling
                await GenesysCloudUtils.WebSocket.CombinedSubscriptionHelper
                    .CreateUserSubscriptionsAsync(
                        _logger,
                        _jsonUtils,
                        userChunk,
                        webSocket,
                        "v2.users.",
                        ".activity",
                        "id");
                        
                _logger?.LogInformation("[UserActivity][Channel:{ChannelId}] Successfully created activity subscriptions for {Count} users", 
                    webSocket.id, userChunk.Rows.Count);
                    
                // Check if the subscriptions are active - this is a diagnostic step
                try {
                    string subscriptionsUrl = $"{_jsonUtils.ApiEndpoint}/api/v2/notifications/channels/{webSocket.id}/subscriptions";
                    string response = await _jsonUtils.JsonRestAsync(subscriptionsUrl, null, "GET");
                    _logger?.LogDebug("[UserActivity] Current subscriptions: {Subscriptions}", 
                        response?.Length > 500 ? response.Substring(0, 500) + "..." : response);
                }
                catch (Exception ex) {
                    _logger?.LogWarning(ex, "[UserActivity] Could not verify subscriptions");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Channel:{ChannelId}] Error creating activity subscriptions for {Count} users", 
                    webSocket?.id ?? "null", userChunk.Rows.Count);
                throw; // Rethrow to allow upstream error handling
            }
        }

        protected override void CreateWebSocket(string socketAddress, string socketChannel, string threadName)
        {
            var detail = new WebSocketDetail
            {
                connectUri = socketAddress,
                id = socketChannel,
                ReportName = threadName,
                Created = DateTime.UtcNow,
                Expires = DateTime.UtcNow.AddHours(1)
            };

            var wsManager = WebSocketManagerFactory.CreateWebSocketManager(
                _logger, detail, threadName, ReceiveData, _jsonUtils);

            WebSocketManagers.Add(wsManager);
            wsManager.StartWebSocketConnection();
            
            _logger?.LogInformation("[UserActivity][WebSocket:{ChannelId}][Thread:{ThreadName}] Created WebSocket connection", 
                socketChannel, threadName);
        }

        public void ReceiveData(string jsonString, string threadName)
        {
            // Add a direct log entry to show what's being received
            _logger?.LogDebug("[UserActivity][Thread:{ThreadName}] Received data: {JsonPreview}", 
                threadName, jsonString.Length > 100 ? jsonString.Substring(0, 100) + "..." : jsonString);
            
            // Always increment the event counter for any message received - with thread safety
            lock (_statsLock)
            {
                _processedEventsCount++;
                _lastProcessedEventTime = DateTime.UtcNow;
                
                // Update base class properties immediately
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }
            
            // Log an explicit event count message periodically (every 10 events)
            if (_processedEventsCount % 10 == 0)
            {
                _logger?.LogInformation("[UserActivity] Total events processed: {Count}, last activity: {LastActivity}", 
                    _processedEventsCount, _lastProcessedEventTime);
            }
            
            // When processing data from a specific thread, include the thread name in the log prefix
            if (jsonString.Contains("topicName") && jsonString.Contains("v2.users") &&
                jsonString.IndexOf("activity") > 0 && jsonString.IndexOf("systemPresence") > 0)
            {
                _logger?.LogDebug("[UserActivity][Thread:{ThreadName}] Processing user activity data", threadName);
                TranslateActivity(jsonString, threadName);
            }
            // Add a more general check that will catch any type of v2.users message
            else if (jsonString.Contains("topicName") && jsonString.Contains("v2.users"))
            {
                _logger?.LogDebug("[UserActivity][Thread:{ThreadName}] Received user data but not matching activity pattern", threadName);
            }

            // Process any pending data writes
            if (_writeUserData && _userData.Rows.Count > 0)
            {
                try
                {
                    // Check if table exists first and create if needed
                    try {
                        // Log the database type for debugging
                        _logger?.LogDebug("[UserActivity][Thread:{ThreadName}][DB] Database type: {DbType}", 
                            threadName, DBAdapter.DBType);
                        
                        // Check if table exists
                        bool tableExists = TableExists("userRealTimeData");
                        if (!tableExists)
                        {
                            _logger?.LogError("[UserActivity][Thread:{ThreadName}][DB] Required table 'userRealTimeData' does not exist. Please run the install process to set up required tables.", threadName);
                            throw new InvalidOperationException("Required table 'userRealTimeData' does not exist. Please run the install process to set up required tables.");
                        }
                    }
                    catch (Exception ex) {
                        _logger?.LogError(ex, "[UserActivity][Thread:{ThreadName}][DB] Error checking table existence", threadName);
                        throw;
                    }
                    
                    // Log the exact data being written for debugging
                    _logger?.LogInformation("[UserActivity][Thread:{ThreadName}][DB] Writing {Count} user activity records to database", 
                        threadName, _userData.Rows.Count);
                    
                    // Directly write to the database
                    DBAdapter.WriteSQLDataBulk(_userData);
                    
                    _writeUserData = false;
                    _logger?.LogInformation("[UserActivity][Thread:{ThreadName}][DB] Successfully wrote {Count} user activity records to database", 
                        threadName, _userData.Rows.Count);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[UserActivity][Thread:{ThreadName}][DB] Error writing user activity to database", threadName);
                }
            }

            if (jsonString.IndexOf("WebSocket Heartbeat") > 0 ||
                    (jsonString.Contains("\"topicName\": \"channel.metadata\"") &&
                     jsonString.Contains("\"message\": \"pong\"")))
            {
                _logger?.LogDebug("[UserActivity][Thread:{ThreadName}] Received heartbeat", threadName);
                // Reset error counters on successful heartbeat
                TotalErrors = 0;
            }
            else
            {
                // Add a catch-all to see what other types of messages we might be receiving
                _logger?.LogDebug("[UserActivity][Thread:{ThreadName}] Received unhandled data type: {JsonPreview}", 
                    threadName, jsonString.Length > 100 ? jsonString.Substring(0, 100) + "..." : jsonString);
            }
        }

        private bool TranslateActivity(string jsonString, string threadName)
        {
            bool successful = false;

            try
            {
                RealUA.Activity userActivity = JsonConvert.DeserializeObject<RealUA.Activity>(
                    jsonString,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }
                );

                if (userActivity?.eventBody == null)
                {
                    _logger?.LogWarning("[UserActivity][Thread:{ThreadName}] Received activity with null eventBody", threadName);
                    return false;
                }

                RealUA.EventbodyUser realUserInfo = userActivity.eventBody;

                lock (_userData)
                {
                    DataRow userRow = _userData.Select($"id = '{realUserInfo.id}'").FirstOrDefault();

                    if (userRow == null)
                    {
                        userRow = _userData.NewRow();
                        userRow["id"] = realUserInfo.id;
                        _userData.Rows.Add(userRow);
                    }

                    userRow["routingStatus"] = realUserInfo.routingStatus?.status;
                    userRow["routstarttime"] = realUserInfo.routingStatus?.startTime;

                    if (realUserInfo.presence?.presenceDefinition != null)
                    {
                        userRow["systemPresence"] = realUserInfo.presence.presenceDefinition.systemPresence;
                        userRow["presenceId"] = realUserInfo.presence.presenceDefinition.id;
                    }

                    userRow["presstarttime"] = realUserInfo.presence?.modifiedDate;
                    
                    // Add updated timestamp
                    userRow["updated"] = DateTime.UtcNow;

                    _writeUserData = true;
                    successful = true;
                }

                // Add this line to increment event counter and update last activity time
                IncrementEventsCount();
                
                // Log that we've processed an event
                _logger?.LogDebug("[UserActivity][Thread:{ThreadName}] Processed activity event, total events: {EventCount}", 
                    threadName, _processedEventsCount);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Thread:{ThreadName}][DataProcessing] Error translating activity", threadName);
                successful = false;
            }

            return successful;
        }

        private DataTable GetUsers()
        {
            string query = "SELECT id, name, jabberId, email, state, title, username, department FROM userDetails WHERE state = 'active'";
            return DBAdapter.GetSQLTableData(query, "UserDetails");
        }

        private DataTable CreateActivityTable()
        {
            // Create table with only the columns needed for activity data
            DataTable dtTemp = new DataTable("userRealTimeData");
            
            // Add columns based on userrealtimedata.sql schema
            dtTemp.Columns.Add("id", typeof(string));
            dtTemp.Columns.Add("name", typeof(string));
            dtTemp.Columns.Add("jabberId", typeof(string));
            dtTemp.Columns.Add("email", typeof(string));
            dtTemp.Columns.Add("state", typeof(string));
            dtTemp.Columns.Add("title", typeof(string));
            dtTemp.Columns.Add("username", typeof(string));
            dtTemp.Columns.Add("department", typeof(string));
            
            // Activity service specific columns
            dtTemp.Columns.Add("routingstatus", typeof(string));
            dtTemp.Columns.Add("routstarttime", typeof(DateTime));
            dtTemp.Columns.Add("systempresence", typeof(string));
            dtTemp.Columns.Add("presenceid", typeof(string));
            dtTemp.Columns.Add("presstarttime", typeof(DateTime));
            
            // Standard timestamp column
            dtTemp.Columns.Add("updated", typeof(DateTime));
            
            _logger?.LogDebug("[UserActivity][Init] Creating activity table with primary columns: id, updated, routingStatus, routstarttime, systemPresence, presenceId, presstarttime");
            
            return dtTemp;
        }

        private bool ProcessUserPresenceData(string jsonString, string threadName)
        {
            try
            {
                // Parse JSON
                var presenceData = JsonConvert.DeserializeObject<JObject>(jsonString);

                if (presenceData == null || presenceData["eventBody"] == null)
                {
                    return false;
                }

                // Extract user ID from topic
                string topicName = presenceData["topicName"]?.ToString();
                if (string.IsNullOrEmpty(topicName))
                {
                    return false;
                }
                
                // Fix: Remove ".activity" from the extracted user ID
                string userId = topicName.Replace("v2.users.", "").Replace(".activity", "");

                // Look up the user name for better logging
                string userName = "Unknown User";
                if (_userDetails != null)
                {
                    var userRow = _userDetails.AsEnumerable()
                        .FirstOrDefault(r => r.Field<string>("id") == userId);
                    if (userRow != null)
                    {
                        userName = userRow.Field<string>("name") ?? "Unknown User";
                    }
                }

                // Extract presence info
                string presenceId = presenceData["eventBody"]["presenceDefinition"]?["id"]?.ToString() ?? "";
                string systemPresence = presenceData["eventBody"]["presenceDefinition"]?["systemPresence"]?.ToString() ?? "UNKNOWN";

                // Parse the modified date
                DateTime modifiedDate = DateTime.UtcNow;
                if (presenceData["eventBody"]["modifiedDate"] != null)
                {
                    if (DateTime.TryParse(presenceData["eventBody"]["modifiedDate"].ToString(), out DateTime parsedDate))
                    {
                        modifiedDate = parsedDate;
                    }
                }

                // Update the user's presence data in the main user data table
                lock (_userData)
                {
                    DataRow userRow = _userData.Select($"id = '{userId}'").FirstOrDefault();
                    if (userRow == null)
                    {
                        userRow = _userData.NewRow();
                        userRow["id"] = userId;
                        userRow["systemPresence"] = systemPresence;
                        userRow["presenceId"] = presenceId;
                        userRow["presstarttime"] = modifiedDate;
                        userRow["updated"] = DateTime.UtcNow; // Make sure updated timestamp is set
                        _userData.Rows.Add(userRow);
                    }
                    else
                    {
                        userRow["systemPresence"] = systemPresence;
                        userRow["presenceId"] = presenceId;
                        userRow["presstarttime"] = modifiedDate;
                        userRow["updated"] = DateTime.UtcNow; // Make sure updated timestamp is set
                    }
                    _logger?.LogDebug("[UserActivity][Thread:{ThreadName}][DataProcessing] Updated presence for user {UserId} to {SystemPresence}", 
                        threadName, userId, systemPresence);
                    _writeUserData = true;
                    
                    // Add forced immediate write for testing
                    try {
                        DBAdapter.WriteSQLDataBulk(_userData);
                        _writeUserData = false;
                        _logger?.LogInformation("[UserActivity][Thread:{ThreadName}][DB] Force-wrote {Count} user activity records to database", 
                            threadName, _userData.Rows.Count);
                    }   
                    catch (Exception dbEx) {
                        _logger?.LogError(dbEx, "[UserActivity][Thread:{ThreadName}][DB] Error during force-write", threadName);
                    }
                }
                // Replace this.RecordEntityActivity with local implementation
                TrackEntityActivity(userId, userName, "User");
                
                // Add this line to track events
                IncrementEventsCount();
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Thread:{ThreadName}][DataProcessing] Error processing user presence data", threadName);
                return false;
            }
        }

        private bool ProcessUserRoutingStatusData(string jsonString, string threadName)
        {
            bool successful = false;
            try
            {
                var routingData = JsonConvert.DeserializeObject<JObject>(jsonString);
                if (routingData == null)
                {
                    return false;
                }
                
                // Extract user ID from topic
                string topicName = routingData["topicName"]?.ToString();
                if (string.IsNullOrEmpty(topicName))
                {
                    return false;
                }
                
                // Fix: Remove ".routingStatus" from the extracted user ID
                string userId = topicName.Replace("v2.users.", "").Replace(".routingStatus", "");

                // Look up the user name for better logging
                string userName = "Unknown User";
                if (_userDetails != null)
                {
                    var userRow = _userDetails.AsEnumerable()
                        .FirstOrDefault(r => r.Field<string>("id") == userId);
                    if (userRow != null)
                    {
                        userName = userRow.Field<string>("name") ?? "Unknown User";
                    }
                }

                // Extract routing status info
                string status = routingData["eventBody"]?["status"]?.ToString() ?? "UNKNOWN";
                DateTime startTime = DateTime.UtcNow;
                if (routingData["eventBody"]?["startTime"] != null)
                {
                    if (DateTime.TryParse(routingData["eventBody"]["startTime"].ToString(), out DateTime parsedTime))
                    {
                        startTime = parsedTime;
                    }
                }

                // Update the user's routing status in the main user data table
                lock (_userData)
                {
                    DataRow userRow = _userData.Select($"id = '{userId}'").FirstOrDefault();
                    if (userRow == null)
                    {
                        userRow = _userData.NewRow();
                        userRow["id"] = userId;
                        userRow["routingStatus"] = status;
                        userRow["routstarttime"] = startTime;
                        userRow["updated"] = DateTime.UtcNow; // Make sure updated timestamp is set
                        _userData.Rows.Add(userRow);
                    }
                    else
                    {
                        userRow["routingStatus"] = status;
                        userRow["routstarttime"] = startTime;
                        userRow["updated"] = DateTime.UtcNow; // Make sure updated timestamp is set
                    }
                    _logger?.LogDebug("[UserActivity][Thread:{ThreadName}][DataProcessing] Updated routing status for user {UserId} to {Status}", 
                        threadName, userId, status);
                    _writeUserData = true;
                    
                    // Add forced immediate write for testing
                    try {
                        DBAdapter.WriteSQLDataBulk(_userData);
                        _writeUserData = false;
                        _logger?.LogInformation("[UserActivity][Thread:{ThreadName}][DB] Force-wrote {Count} user activity records to database", 
                            threadName, _userData.Rows.Count);
                    }   
                    catch (Exception dbEx) {
                        _logger?.LogError(dbEx, "[UserActivity][Thread:{ThreadName}][DB] Error during force-write", threadName);
                    }
                }
                // Replace this.RecordEntityActivity with local implementation
                TrackEntityActivity(userId, userName, "User");
                
                // Add this line to track events
                IncrementEventsCount();
                successful = true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Thread:{ThreadName}][ProcessRouting] Error processing user routing status data", threadName);
                successful = false;
            }
            return successful;
        }

        // Add a local implementation of the entity tracking method
        private void TrackEntityActivity(string entityId, string entityName, string entityType)
        {
            try
            {
                // Increment the processed events counter
                Interlocked.Increment(ref _processedEventsCount);
                
                // Update the last processed time
                _lastProcessedEventTime = DateTime.UtcNow;
                
                // You can also add custom tracking logic here if needed
                _logger?.LogDebug("[UserActivity] Recorded activity for {EntityType} {EntityName} ({EntityId})", 
                    entityType, entityName, entityId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error tracking entity activity for {EntityType} {EntityName}", entityType, entityName);
            }
        }

        public async Task<string> GetActivityAsync(string url, string jsonBody)
        {
            var response = await _jsonUtils.JsonReturnAsync(_jsonUtils.ApiEndpoint + url, null, "GET", jsonBody);
            return response.Count() > 0 ? response.ToString() : string.Empty;
        }

        // Add a new public method to retrieve event metrics for monitoring
        public long GetProcessedEventsCount()
        {
            return _processedEventsCount;
        }

        protected async Task ProcessEventAsync(object eventData)
        {
            // Process the event data
            
            // Update both local and base class counters
            Interlocked.Increment(ref _processedEventsCount);
            EventsProcessed = _processedEventsCount;
            _lastProcessedEventTime = DateTime.UtcNow;
            LastActivity = _lastProcessedEventTime;
            
            // Log the event processing
            _logger?.LogDebug("[UserActivity] Processed event, total count: {EventCount}", _processedEventsCount);
        }

        protected async Task PollDataAsync()
        {
            try
            {
                // Polling implementation
                
                // Make sure to update LastActivity even if no events were processed
                LastActivity = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error polling user activity data");
            }
        }

        // Remove the override keyword and rename the method to avoid conflicts
        private async Task SafeWriteToDatabaseWithLogging(DataTable data, string dataDescription)
        {
            try
            {
                _logger?.LogInformation("[UserActivity][DB] Writing {Count} {Description} records to database", 
                    data.Rows.Count, dataDescription);
                await base.SafeWriteToDatabase(data, dataDescription);
                _logger?.LogInformation("[UserActivity][DB] Successfully wrote {Count} {Description} records", 
                    data.Rows.Count, dataDescription);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][DB] Error writing {Description} data to database", dataDescription);
                throw;
            }
        }

        // Update all references to SafeWriteToDatabase to use the new method
        protected override void ProcessDatabaseWrites()
        {
            try
            {
                _logger?.LogDebug("[UserActivity][DB] Processing database writes");
                base.ProcessDatabaseWrites();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][DB] Error processing database writes");
            }
        }

        // Override RecoverFailedConnectionsAsync to use service-specific log prefix
        protected override async Task RecoverFailedConnectionsAsync()
        {
            try
            {
                _logger?.LogInformation("[UserActivity][Recover] Attempting to recover failed connections");
                await base.RecoverFailedConnectionsAsync();
                _logger?.LogInformation("[UserActivity][Recover] Recovery attempt completed");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserActivity][Recover] Error during connection recovery");
            }
        }
    }
}
