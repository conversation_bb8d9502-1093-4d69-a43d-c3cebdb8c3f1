using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using GCRealTime.Core;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using GenesysCloudUtils.WebSocket;
using GenesysCloudUtils;
using StandardUtils; // Reference Utils class for datetime conversion

namespace GCRealTime.Services
{
    /// <summary>
    /// Service for monitoring queue metrics and statistics in real-time
    /// </summary>
    public class QueueObservationService : BaseRealTimeService, IRealtimeService
    {
        private DataTable _queueDetails;
        private DataTable _queueObservationData;
        private List<WebSocketDetail> _webSocketList = new List<WebSocketDetail>();
        private bool _writeObservationData = false;
        private readonly object _observationDataLock = new object();
        private bool _isRunning = false;
        private readonly ILogger _logger;
        private JsonUtils _jsonUtils;
        private List<string> _queueIds = new List<string>();

        public QueueObservationService(ILogger logger, DBUtils.DBUtils dbAdapter) 
            : base(logger, dbAdapter)
        {
            _logger = logger;
            _jsonUtils = new JsonUtils(logger);
        }

        public override void Initialize()
        {
            _logger?.LogInformation("Initializing queue observation service");
            base.Initialize();
            
            // Check if queueDetails table exists - this is required
            if (!TableExists("queueDetails"))
            {
                string errorMessage = "Required table 'queueDetails' does not exist - cannot start queue observation service";
                _logger?.LogError(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
            
            _queueDetails = GetQueues();
            
            // If no queues are found, log warning but don't throw (might be a valid state)
            if (_queueDetails.Rows.Count == 0)
            {
                _logger?.LogWarning("No active queues found in queueDetails table - service will run but won't monitor any queues");
            }
            
            _queueObservationData = CreateQueueObservationTable();
            
            try
            {
                // Check if the queueRealTimeData table exists
                if (TableExists("queueRealTimeData"))
                {
                    // Load existing queue observation data
                    var existingData = GetPaginatedTableData("queueRealTimeData", "updated");
                    if (existingData?.Rows.Count > 0)
                    {
                        _logger?.LogInformation("Found {Count} existing queue observation records", existingData.Rows.Count);
                        foreach (DataRow row in existingData.Rows)
                        {
                            _queueObservationData.ImportRow(row);
                        }
                    }
                }
                else
                {
                    _logger?.LogInformation("queueRealTimeData table does not exist - will be created when data is received");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Could not retrieve existing queue observation data");
            }
        }

        public override void Start()
        {
            _logger?.LogInformation("Starting queue observation monitoring");
            
            _webSocketList.Clear();
            WebSocketThreads.Clear();
            
            // Split queues into chunks if needed
            if (_queueDetails.Rows.Count > MAX_TOPICS_PER_SUBSCRIPTION)
            {
                var chunks = ChunkDataTable(_queueDetails, MAX_TOPICS_PER_SUBSCRIPTION);
                _logger?.LogInformation("Splitting queue observations into {Count} channels", chunks.Count);
                
                int chunkIndex = 1;
                foreach (var chunk in chunks)
                {
                    WebSocketDetail socket = CreateChannel($"queueObserve-{chunkIndex}");
                    _webSocketList.Add(socket);
                    
                    CreateQueueObservationSubscriptionsForChunk(socket, chunk);
                    
                    Thread thread = new Thread(() => CreateWebSocket(socket.connectUri, socket.id, $"queueObserve-{chunkIndex}"));
                    thread.Name = $"queueObserve-{chunkIndex}";
                    thread.Start();
                    WebSocketThreads.Add(thread);
                    
                    chunkIndex++;
                }
            }
            else
            {
                WebSocketDetail socket = CreateChannel("queueObserve");
                _webSocketList.Add(socket);
                
                CreateQueueObservationSubscriptions(socket);
                
                Thread thread = new Thread(() => CreateWebSocket(socket.connectUri, socket.id, "queueObserve"));
                thread.Name = "queueObserve";
                thread.Start();
                WebSocketThreads.Add(thread);
            }
        }

        public async Task InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("Initializing QueueObservationService at {Time}", DateTime.UtcNow);
                await Task.Run(() => Initialize());
                _logger?.LogInformation("QueueObservationService initialization complete");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to initialize QueueObservationService");
                throw;
            }
        }
        
        public async Task StartAsync(CancellationToken token)
        {
            try
            {
                _logger?.LogInformation("Starting QueueObservationService");
                await Task.Run(() => Start(), token);
                _isRunning = true;
                _logger?.LogInformation("QueueObservationService started successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to start QueueObservationService");
                throw;
            }
        }
        
        public async Task StopAsync()
        {
            try
            {
                _logger?.LogInformation("Stopping QueueObservationService");
                await Task.Run(() => Stop());
                _isRunning = false;
                _logger?.LogInformation("QueueObservationService stopped successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to stop QueueObservationService");
                throw;
            }
        }

        public async Task<bool> CheckHealthAsync()
        {
            try
            {
                _logger?.LogInformation("Performing health check for QueueObservationService");
                
                // Check if the service is running
                if (!_isRunning)
                {
                    _logger?.LogWarning("Service is not running");
                    return false;
                }
                
                // Check if there are active WebSocket connections
                if (WebSocketManagers.Count == 0)
                {
                    _logger?.LogWarning("No active WebSocket managers");
                    return false;
                }
                
                // Check if at least one WebSocket connection is operational
                bool hasActiveConnection = WebSocketManagers.Any(m => m.IsConnected);
                if (!hasActiveConnection)
                {
                    _logger?.LogWarning("No active WebSocket connections");
                    return false;
                }
                
                // Check if too many errors have occurred
                if (TotalErrors > 10)
                {
                    _logger?.LogWarning("Too many errors: {ErrorCount}", TotalErrors);
                    return false;
                }
                
                // Service is healthy
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Health check failed for QueueObservationService");
                return false;
            }
        }

        protected override void ReceiveData(string jsonString, string threadName)
        {
            using (_logger.BeginScope("QueueObserve/{ThreadName}", threadName))
            {
                try
                {
                    if (jsonString.Contains("topicName") && jsonString.Contains("routing.queues") && 
                        jsonString.Contains("observations"))
                    {
                        ProcessQueueObservationData(jsonString);
                    }
                    else if (jsonString.Contains("error") || jsonString.Contains("Error"))
                    {
                        _logger?.LogError("Error in WebSocket response: {JsonString}", jsonString);
                        TotalErrors++;
                    }
                    // Check for WebSocket closing notification
                    else if (jsonString.Contains("Websocket closing soon") || jsonString.Contains("websocket closing soon"))
                    {
                        _logger?.LogWarning("Received WebSocket closing notification on thread {ThreadName}", threadName);
                        Task.Run(async () => await StartAsync(CancellationToken.None)).Wait();
                    }
                    
                    if (jsonString.Contains("WebSocket Heartbeat") || 
                        (jsonString.Contains("\"topicName\": \"channel.metadata\"") && 
                         jsonString.Contains("\"message\": \"pong\"")))
                    {
                        _logger?.LogDebug("Received heartbeat on thread {ThreadName}", threadName);
                        TotalErrors = 0;
                    }
                    
                    // Check if we need to refresh channels
                    bool needsRefresh = false;
                    foreach (var channel in _webSocketList)
                    {
                        if (channel.NeedsRefresh)
                        {
                            _logger?.LogInformation("Channel {Id} needs refresh", channel.id);
                            needsRefresh = true;
                            break;
                        }
                    }
                    
                    if (needsRefresh)
                    {
                        try
                        {
                            RefreshChannels();
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Error refreshing channels");
                        }
                    }
                    
                    if (_writeObservationData && _queueObservationData.Rows.Count > 0)
                    {
                        try
                        {
                            DBAdapter.WriteSQLDataBulk(_queueObservationData);
                            _writeObservationData = false;
                            _logger?.LogInformation("Wrote {Count} queue observation records", _queueObservationData.Rows.Count);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Error writing queue observation data");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error processing WebSocket data on thread {ThreadName}", threadName);
                    TotalErrors++;
                }
            }
        }

        private bool ProcessQueueObservationData(string jsonString)
        {
            try
            {
                lock (_observationDataLock)
                {
                    var observationData = JsonConvert.DeserializeObject<JObject>(jsonString);
                    if (observationData == null)
                    {
                        return false;
                    }

                    string queueId = observationData["topicName"]?.ToString()
                        .Replace("v2.routing.queues.", "")
                        .Replace(".observations", "");
                    
                    // Look up the queue name for better logging
                    string queueName = "Unknown Queue";
                    if (_queueDetails != null)
                    {
                        var queueRow = _queueDetails.AsEnumerable()
                            .FirstOrDefault(r => r.Field<string>("id") == queueId);
                        if (queueRow != null)
                        {
                            queueName = queueRow.Field<string>("name") ?? "Unknown Queue";
                        }
                    }

                    var metrics = observationData["eventBody"];
                    if (metrics == null)
                    {
                        return false;
                    }

                    var row = _queueObservationData.NewRow();
                    row["id"] = $"{queueId}_{DateTime.UtcNow.Ticks}";
                    row["queueId"] = queueId;
                    row["observationDate"] = DateTime.UtcNow;
                    // Use the base class's conversion method for consistent timezone handling
                    row["observationDateLocalTZ"] = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _timeZone);

                    // Capture metrics
                    row["interacting"] = metrics["oInteracting"]?.Value<int>() ?? 0;
                    row["waiting"] = metrics["oWaiting"]?.Value<int>() ?? 0;
                    row["onHold"] = metrics["oOnHold"]?.Value<int>() ?? 0;
                    row["longestWaitingMs"] = metrics["oLongestWaitingMs"]?.Value<int>() ?? 0;
                    
                    // Media-specific metrics
                    row["interactingCalls"] = metrics["oInteractingCalls"]?.Value<int>() ?? 0;
                    row["waitingCalls"] = metrics["oWaitingCalls"]?.Value<int>() ?? 0;
                    row["interactingCallbacks"] = metrics["oInteractingCallbacks"]?.Value<int>() ?? 0;
                    row["waitingCallbacks"] = metrics["oWaitingCallbacks"]?.Value<int>() ?? 0;
                    row["interactingChats"] = metrics["oInteractingChats"]?.Value<int>() ?? 0;
                    row["waitingChats"] = metrics["oWaitingChats"]?.Value<int>() ?? 0;
                    row["interactingEmails"] = metrics["oInteractingEmails"]?.Value<int>() ?? 0;
                    row["waitingEmails"] = metrics["oWaitingEmails"]?.Value<int>() ?? 0;
                    row["interactingMessages"] = metrics["oInteractingMessages"]?.Value<int>() ?? 0;
                    row["waitingMessages"] = metrics["oWaitingMessages"]?.Value<int>() ?? 0;

                    row["updated"] = DateTime.UtcNow;
                    
                    _queueObservationData.Rows.Add(row);
                    _writeObservationData = true;
                    
                    // Track entity activity - add custom implementation since BaseRealTimeService's method isn't accessible
                    TrackEntityActivity(queueId, queueName, "Queue");
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing queue observation data");
                return false;
            }
        }

        // Add a local implementation of the entity tracking method
        private void TrackEntityActivity(string entityId, string entityName, string entityType)
        {
            try
            {
                // Increment the processed events counter
                Interlocked.Increment(ref _processedEventsCount);
                
                // Update the last processed time
                _lastProcessedEventTime = DateTime.UtcNow;
                
                // You can also add custom tracking logic here if needed
                _logger?.LogDebug("[QueueObserve] Recorded activity for {EntityType} {EntityName} ({EntityId})", 
                    entityType, entityName, entityId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error tracking entity activity for {EntityType} {EntityName}", entityType, entityName);
            }
        }

        // Add these fields if they don't exist
        private long _processedEventsCount = 0;
        private DateTime _lastProcessedEventTime = DateTime.MinValue;

        private DataTable GetQueues()
        {
            try
            {
                // First check if the state column exists
                var columnsQuery = DBAdapter.DBType switch
                {
                    CSG.Adapter.Configuration.DatabaseType.MSSQL => 
                        "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'queueDetails' AND COLUMN_NAME = 'state'",
                    _ => "SELECT column_name FROM information_schema.columns WHERE table_name = 'queuedetails' AND column_name = 'state'"
                };
                
                var columnsTable = DBAdapter.GetSQLTableData(columnsQuery, "QueueColumns");
                bool stateColumnExists = columnsTable != null && columnsTable.Rows.Count > 0;
                
                // Choose appropriate query based on column existence
                string query = stateColumnExists ? 
                    "SELECT id, name FROM queueDetails WHERE state = 'active'" : 
                    "SELECT id, name FROM queueDetails";
                
                _logger?.LogInformation("Getting queue details with query: {Query}", query);
                var queueTable = DBAdapter.GetSQLTableData(query, "QueueDetails");
                
                if (queueTable == null || queueTable.Rows.Count == 0)
                {
                    _logger?.LogWarning("No queue records found in database");
                }
                
                return queueTable;
            }
            catch (Exception ex)
            {
                string errorMessage = "Failed to retrieve queue details from database";
                _logger?.LogError(ex, errorMessage);
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        private DataTable CreateQueueObservationTable()
        {
            // Use the legacy table name "queueRealTimeData" instead of "queueObservationRealTime"
            var table = new DataTable("queueRealTimeData");
            
            table.Columns.Add("id", typeof(string));
            table.Columns.Add("queueId", typeof(string));
            table.Columns.Add("observationDate", typeof(DateTime));
            table.Columns.Add("observationDateLocalTZ", typeof(DateTime));
            table.Columns.Add("interacting", typeof(int));
            table.Columns.Add("waiting", typeof(int));
            table.Columns.Add("onHold", typeof(int));
            table.Columns.Add("longestWaitingMs", typeof(int));
            table.Columns.Add("interactingCalls", typeof(int));
            table.Columns.Add("waitingCalls", typeof(int));
            table.Columns.Add("interactingCallbacks", typeof(int));
            table.Columns.Add("waitingCallbacks", typeof(int));
            table.Columns.Add("interactingChats", typeof(int));
            table.Columns.Add("waitingChats", typeof(int));
            table.Columns.Add("interactingEmails", typeof(int));
            table.Columns.Add("waitingEmails", typeof(int));
            table.Columns.Add("interactingMessages", typeof(int));
            table.Columns.Add("waitingMessages", typeof(int));
            table.Columns.Add("updated", typeof(DateTime));
            
            DataColumn[] keys = new DataColumn[1];
            keys[0] = table.Columns["id"];
            table.PrimaryKey = keys;
            
            return table;
        }

        private void CreateQueueObservationSubscriptions(WebSocketDetail webSocket)
        {
            CreateQueueObservationSubscriptionsForChunk(webSocket, _queueDetails);
        }

        private void CreateQueueObservationSubscriptionsForChunk(WebSocketDetail webSocket, DataTable queueChunk)
        {
            _logger?.LogInformation("Creating observation subscriptions for {Count} queues", queueChunk.Rows.Count);

            // If no queues, don't attempt to create subscriptions
            if (queueChunk.Rows.Count == 0)
            {
                _logger?.LogWarning("No queues found to subscribe to");
                return;
            }

            // Use the helper to create combined subscriptions following best practices
            GenesysCloudUtils.WebSocket.CombinedSubscriptionHelper
                .CreateQueueSubscriptionsAsync(
                    _logger,
                    _jsonUtils,
                    queueChunk,
                    webSocket,
                    ".observations")
                .GetAwaiter().GetResult();
        }

        protected override void RefreshChannels()
        {
            foreach (var channel in _webSocketList)
            {
                if (channel.IsExpired)
                {
                    _logger?.LogWarning("Channel has expired: {Channel}", channel);
                }
                else if (channel.NeedsRefresh)
                {
                    if (channel.ReportName.Contains("-"))
                    {
                        string[] parts = channel.ReportName.Split('-');
                        if (parts.Length > 1 && int.TryParse(parts[1], out int chunkIndex) && chunkIndex > 0)
                        {
                            var chunks = ChunkDataTable(_queueDetails, MAX_TOPICS_PER_SUBSCRIPTION);
                            if (chunkIndex <= chunks.Count)
                            {
                                DataTable queueChunk = chunks[chunkIndex - 1];
                                RefreshSubscriptionsForChannel(channel, queueChunk);
                            }
                        }
                    }
                    else
                    {
                        RefreshSubscriptionsForChannel(channel, _queueDetails);
                    }
                }
            }
        }

        private void RefreshSubscriptionsForChannel(WebSocketDetail webSocket, DataTable queueChunk)
        {
            try
            {
                _logger?.LogInformation("Refreshing subscriptions for channel {ChannelId}", webSocket.id);

                string deleteUrl = $"/api/v2/notifications/channels/{webSocket.id}/subscriptions";
                _jsonUtils.JsonRestAsync(deleteUrl, null, "DELETE").Wait();

                CreateQueueObservationSubscriptionsForChunk(webSocket, queueChunk);

                _logger?.LogInformation("Successfully refreshed subscriptions for channel {ChannelId}", webSocket.id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to refresh subscriptions for channel {ChannelId}", webSocket.id);
            }
        }

        private async Task RefreshToken()
        {
            try
            {
                _logger?.LogInformation("Refreshing authentication token");
                
                // Replace _jsonUtilsAdapter with _jsonUtils
                bool success = await _jsonUtils.RefreshTokenAsync(true);
                
                if (success)
                {
                    _logger?.LogInformation("Successfully refreshed authentication token");
                }
                else
                {
                    _logger?.LogWarning("Failed to refresh authentication token");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error refreshing authentication token");
            }
        }

        private async Task<bool> FetchData()
        {
            try
            {
                // Define url and requestBody
                string url = $"{_jsonUtils.ApiEndpoint}/api/v2/analytics/queues/observations/query";
                string requestBody = JsonConvert.SerializeObject(new 
                {
                    filter = new {
                        type = "or",
                        predicates = _queueIds.Select(id => new {
                            dimension = "queueId",
                            value = id
                        }).ToArray()
                    },
                    metrics = new[] { "oWaiting", "oInteracting" }
                });

                // Add more detailed error handling for the API call
                string response = "";
                try
                {
                    response = await _jsonUtils.JsonRestAsync(
                        url,
                        null,
                        "POST",
                        requestBody);
                }
                catch (Exception apiEx)
                {
                    _logger?.LogError(apiEx, "API request failed when fetching queue observation data");
                    return false;
                }
                
                // Check for empty or invalid response
                if (string.IsNullOrEmpty(response))
                {
                    _logger?.LogWarning("Received empty response from queue observations API");
                    return false;
                }
                
                try
                {
                    var responseObject = JsonConvert.DeserializeObject<JObject>(response);
                    if (responseObject == null)
                    {
                        _logger?.LogWarning("Failed to parse queue observations response");
                        return false;
                    }
                    
                    // Process the results...
                    // ...existing code...
                    
                    return true;
                }
                catch (JsonException jsonEx)
                {
                    _logger?.LogError(jsonEx, "Error parsing queue observations JSON response");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error fetching queue observation data");
                return false;
            }
        }

        // Add method to get the current queue status - similar to legacy implementation
        public DataTable GetQueueStatus()
        {
            lock (_observationDataLock)
            {
                // Return a copy of the current observation data
                DataTable result = _queueObservationData.Copy();
                
                // Join with queue details to include queue names
                if (_queueDetails != null && _queueDetails.Rows.Count > 0)
                {
                    // Create a lookup of queue IDs to names
                    Dictionary<string, string> queueNames = new Dictionary<string, string>();
                    foreach (DataRow row in _queueDetails.Rows)
                    {
                        string id = row["id"].ToString();
                        string name = row["name"].ToString();
                        queueNames[id] = name;
                    }
                    
                    // Add queue names to the result table if it doesn't already have them
                    if (!result.Columns.Contains("queueName"))
                    {
                        result.Columns.Add("queueName", typeof(string));
                    }
                    
                    // Populate queue names
                    foreach (DataRow row in result.Rows)
                    {
                        string queueId = row["queueId"].ToString();
                        if (queueNames.TryGetValue(queueId, out string queueName))
                        {
                            row["queueName"] = queueName;
                        }
                    }
                }
                
                _logger?.LogInformation("Returning status for {Count} queues", result.Rows.Count);
                return result;
            }
        }

        // Ensure we have a method to get the latest observation for each queue
        public DataTable GetLatestQueueObservations()
        {
            lock (_observationDataLock)
            {
                // Create a result table with the same schema
                DataTable result = _queueObservationData.Clone();
                
                // Group by queue ID and get the most recent observation for each queue
                var queueIds = new HashSet<string>();
                var latestRows = new List<DataRow>();
                
                foreach (DataRow row in _queueObservationData.Rows)
                {
                    string queueId = row["queueId"].ToString();
                    DateTime observationDate = Convert.ToDateTime(row["observationDate"]);
                    
                    // Find the existing row for this queue, if any
                    DataRow existingRow = latestRows.Find(r => r["queueId"].ToString() == queueId);
                    
                    if (existingRow == null)
                    {
                        // No existing row for this queue, add this one
                        latestRows.Add(row);
                        queueIds.Add(queueId);
                    }
                    else
                    {
                        // Compare dates and keep the most recent one
                        DateTime existingDate = Convert.ToDateTime(existingRow["observationDate"]);
                        if (observationDate > existingDate)
                        {
                            // Remove the old row and add the new one
                            latestRows.Remove(existingRow);
                            latestRows.Add(row);
                        }
                    }
                }
                
                // Add all the latest rows to the result table
                foreach (var row in latestRows)
                {
                    result.ImportRow(row);
                }
                
                return result;
            }
        }

        // Add an additional method to get aggregated status
        public DataTable GetQueueAggregatedStatus()
        {
            lock (_observationDataLock)
            {
                // Create a result table with all the same columns
                DataTable result = _queueObservationData.Clone();
                
                // Group by queue ID and get the most recent observation for each queue
                var queueIds = new HashSet<string>();
                
                // For each unique queue ID, find the most recent observation
                foreach (string queueId in _queueObservationData.AsEnumerable()
                                         .Select(r => r.Field<string>("queueId"))
                                         .Distinct())
                {
                    // Get all rows for this queue
                    var queueRows = _queueObservationData.AsEnumerable()
                                    .Where(r => r.Field<string>("queueId") == queueId)
                                    .OrderByDescending(r => r.Field<DateTime>("observationDate"));
                    
                    // Add the most recent row to the result
                    if (queueRows.Any())
                    {
                        result.ImportRow(queueRows.First());
                    }
                }
                
                // Add queue names
                if (_queueDetails != null && _queueDetails.Rows.Count > 0)
                {
                    // Create a lookup of queue IDs to names
                    Dictionary<string, string> queueNames = new Dictionary<string, string>();
                    foreach (DataRow row in _queueDetails.Rows)
                    {
                        string id = row["id"].ToString();
                        string name = row["name"].ToString();
                        queueNames[id] = name;
                    }
                    
                    // Add queue names to the result table if it doesn't already have them
                    if (!result.Columns.Contains("queueName"))
                    {
                        result.Columns.Add("queueName", typeof(string));
                    }
                    
                    // Populate queue names
                    foreach (DataRow row in result.Rows)
                    {
                        string queueId = row["queueId"].ToString();
                        if (queueNames.TryGetValue(queueId, out string queueName))
                        {
                            row["queueName"] = queueName;
                        }
                    }
                }
                
                _logger?.LogInformation("Returning aggregated status for {Count} queues", result.Rows.Count);
                return result;
            }
        }

        protected override void CreateWebSocket(string socketAddress, string socketChannel, string threadName)
        {
            try
            {
                // Use the existing WebSocketManager implementation from GenesysCloudUtils.WebSocket
                var detail = new GenesysCloudUtils.WebSocket.WebSocketDetail
                {
                    connectUri = socketAddress,
                    id = socketChannel,
                    ReportName = threadName,
                    Created = DateTime.UtcNow,
                    Expires = DateTime.UtcNow.AddHours(1)
                };
                
                // Create the WebSocketManager using the factory
                var manager = GenesysCloudUtils.WebSocket.WebSocketManagerFactory.CreateWebSocketManager(
                    _logger,
                    detail,
                    threadName,
                    (jsonString, thread) => ReceiveData(jsonString, thread),
                    _jsonUtils);
                    
                WebSocketManagers.Add(manager);
                manager.StartWebSocketConnection();
                
                _logger?.LogInformation("Created WebSocket for channel {ChannelId} with thread name {ThreadName}", 
                    socketChannel, threadName);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error creating WebSocket for channel {ChannelId}", socketChannel);
                throw;
            }
        }
    }
}