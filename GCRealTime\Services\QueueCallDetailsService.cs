using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using GCRealTime.Core;
using Microsoft.Extensions.Logging;
using GenesysCloudUtils.WebSocket; // Add this for WebSocketDetail
using GenesysCloudUtils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils; // Add DateTimeExtensions reference

namespace GCRealTime.Services
{
    public class QueueCallDetailsService : BaseRealTimeService, IRealtimeService
    {
        private readonly ILogger _logger;
        private List<string> _queueIds;
        private DataTable _queueDetails;
        private DataTable _queueStatusData;
        private readonly object _queueStatusLock = new object();
        private bool _isRunning = false;
        private int _failedAttempts = 0;
        
        public QueueCallDetailsService(ILogger logger, DBUtils.DBUtils dbAdapter) 
            : base(logger, dbAdapter)
        {
            _logger = logger;
        }

        public override void Initialize()
        {
            base.Initialize();
            _logger?.LogInformation("Initializing QueueCallDetailsService");
            
            // Check if queueDetails table exists
            if (!TableExists("queueDetails"))
            {
                _logger?.LogWarning("queueDetails table does not exist - using empty queue list");
                _queueDetails = new DataTable();
                _queueDetails.Columns.Add("id", typeof(string));
                _queueDetails.Columns.Add("name", typeof(string));
            }
            else
            {
                // Load queues from database
                _queueDetails = DBAdapter.GetSQLTableData("SELECT id, name FROM queueDetails WHERE state = 'active'", "queueDetails");
            }
            
            _queueIds = _queueDetails.AsEnumerable().Select(row => row.Field<string>("id")).ToList();
            
            // Create queue status table
            _queueStatusData = CreateQueueStatusTable();
            
            // Load existing data if table exists
            if (TableExists("queueconvrealData"))
            {
                try
                {
                    var existingData = GetPaginatedTableData("queueconvrealData", "updated");
                    if (existingData?.Rows.Count > 0)
                    {
                        _logger?.LogInformation("Found {Count} existing queue call details records", existingData.Rows.Count);
                        foreach (DataRow row in existingData.Rows)
                        {
                            _queueStatusData.ImportRow(row);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Error loading existing queue call details data");
                }
            }
            
            if (_queueIds.Count == 0)
            {
                _logger?.LogWarning("No active queues found in database");
            }
            else
            {
                _logger?.LogInformation("Loaded {QueueCount} active queues", _queueIds.Count);
            }
        }

        public override void Start()
        {
            _logger?.LogInformation("Starting QueueCallDetailsService");
            
            try
            {
                // Create initial channel and subscriptions
                RefreshChannels();
                
                // Reset error count
                _failedAttempts = 0;
                _isRunning = true;
            }
            catch (Exception ex)
            {
                _failedAttempts++;
                _logger?.LogError(ex, "Error starting QueueCallDetailsService. Attempt {Attempt}", _failedAttempts);
                throw;
            }
        }

        protected override void RefreshChannels()
        {
            base.RefreshChannels();
            
            try
            {
                // Check if we need to refresh - if no websockets exist or if they are about to expire
                bool needsRefresh = WebSocketManagers.Count == 0 || 
                                   WebSocketManagers.Any(m => ((WebSocketDetail)m).NeedsRefresh);
                
                if (needsRefresh)
                {
                    _logger?.LogInformation("Refreshing queue conversation channels");
                    
                    // Stop existing websockets
                    foreach (var manager in WebSocketManagers)
                    {
                        try 
                        {
                            manager.StopWebSocketConnection();
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Error stopping WebSocket manager");
                        }
                    }
                    
                    // Clear the list
                    WebSocketManagers.Clear();
                    
                    // Create a new channel for queue conversations
                    var channel = CreateChannel("QueueCallDetails");
                    
                    // Create the websocket connection
                    CreateWebSocket(channel.connectUri, channel.id, "QueueCallDetails");
                    
                    // Create queue subscriptions
                    var chunks = ChunkDataTable(GetQueueTable(), MAX_TOPICS_PER_SUBSCRIPTION);
                    foreach (var chunk in chunks)
                    {
                        CreateQueueConversationSubscriptions(chunk, channel);
                    }
                    
                    _failedAttempts = 0;
                }
            }
            catch (Exception ex)
            {
                _failedAttempts++;
                _logger?.LogError(ex, "Error refreshing queue conversation channels. Attempt {Attempt}", _failedAttempts);
                
                if (_failedAttempts >= 3)
                {
                    _logger?.LogCritical("Failed to refresh queue conversation channels after {Attempts} attempts", _failedAttempts);
                }
            }
        }
        
        private DataTable GetQueueTable()
        {
            var table = new DataTable();
            table.Columns.Add("id", typeof(string));
            
            foreach (var queueId in _queueIds)
            {
                var row = table.NewRow();
                row["id"] = queueId;
                table.Rows.Add(row);
            }
            
            return table;
        }
        
        private void CreateQueueConversationSubscriptions(DataTable queueTable, WebSocketDetail channel)
        {
            _logger?.LogInformation("Creating conversation channel for {QueueCount} queues", queueTable.Rows.Count);
            
            // Use the helper to create combined subscriptions following best practices
            GenesysCloudUtils.WebSocket.CombinedSubscriptionHelper
                .CreateQueueSubscriptionsAsync(
                    _logger,
                    _jsonUtils,
                    queueTable,
                    channel,
                    ".conversations")
                .GetAwaiter().GetResult();
        }
        
        protected override void ReceiveData(string jsonString, string threadName)
        {
            _logger?.LogDebug("Received queue conversation data: {Length} bytes", jsonString?.Length ?? 0);
            
            try {
                if (jsonString.Contains("topicName") && jsonString.Contains("routing.queues") && 
                    jsonString.Contains("conversations"))
                {
                    ProcessQueueConversationData(jsonString);
                }
                else if (jsonString.Contains("error") || jsonString.Contains("Error"))
                {
                    _logger?.LogError("Error in WebSocket response: {JsonString}", jsonString);
                    TotalErrors++;
                }
                // Check for WebSocket closing notification
                else if (jsonString.Contains("Websocket closing soon") || jsonString.Contains("websocket closing soon"))
                {
                    _logger?.LogWarning("Received WebSocket closing notification on thread {ThreadName}", threadName);
                    RefreshChannels();
                }
                
                if (jsonString.Contains("WebSocket Heartbeat") || 
                    (jsonString.Contains("\"topicName\": \"channel.metadata\"") && 
                     jsonString.Contains("\"message\": \"pong\"")))
                {
                    _logger?.LogDebug("Received heartbeat on thread {ThreadName}", threadName);
                    TotalErrors = 0;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing WebSocket data on thread {ThreadName}", threadName);
                TotalErrors++;
            }
        }
        
        private bool ProcessQueueConversationData(string jsonString)
        {
            try
            {
                lock (_queueStatusLock)
                {
                    var conversationData = JsonConvert.DeserializeObject<JObject>(jsonString);
                    if (conversationData == null)
                    {
                        _logger?.LogWarning("Received null conversation data");
                        return false;
                    }

                    string queueId = conversationData["topicName"]?.ToString()
                        .Replace("v2.routing.queues.", "")
                        .Replace(".conversations", "");

                    var eventBody = conversationData["eventBody"];
                    if (eventBody == null)
                    {
                        _logger?.LogWarning("Conversation data missing eventBody");
                        return false;
                    }

                    // Check if the observations are truncated
                    bool isTruncated = eventBody["truncated"] != null && eventBody["truncated"].Value<bool>();
                    
                    if (isTruncated)
                    {
                        _logger?.LogInformation("Truncated conversation data detected for queue {QueueId}, fetching complete data", queueId);
                        return VerifyConversationsInQueue(queueId);
                    }
                    else
                    {
                        // Process the non-truncated conversation data
                        var row = _queueStatusData.NewRow();
                        row["id"] = $"{queueId}_{DateTime.UtcNow.Ticks}";
                        row["queueId"] = queueId;
                        row["observationDate"] = DateTime.UtcNow;
                        var utils = new Utils();
                        row["observationDateLocalTZ"] = utils.ConvertToLocalTime(DateTime.UtcNow, _timeZone);
                        
                        // Process conversation metrics
                        int activeConversations = 0;
                        int waitingConversations = 0;
                        int callsActive = 0;
                        int callsWaiting = 0;
                        int chatsActive = 0;
                        int chatsWaiting = 0;
                        int emailsActive = 0;
                        int emailsWaiting = 0;
                        
                        if (eventBody["participants"] != null && eventBody["participants"] is JArray participants)
                        {
                            // Process participant data
                            foreach (var participant in participants)
                            {
                                string purpose = participant["purpose"]?.ToString();
                                string participantType = participant["participantType"]?.ToString() ?? "";
                                
                                if (purpose == "customer" || purpose == "external")
                                {
                                    // Count active conversations by media type
                                    if (participant["calls"] != null && participant["calls"].HasValues)
                                    {
                                        callsActive++;
                                        activeConversations++;
                                    }
                                    if (participant["chats"] != null && participant["chats"].HasValues)
                                    {
                                        chatsActive++;
                                        activeConversations++;
                                    }
                                    if (participant["emails"] != null && participant["emails"].HasValues)
                                    {
                                        emailsActive++;
                                        activeConversations++;
                                    }
                                    
                                    // Count waiting conversations
                                    if (participantType == "acd" || participantType == "routing")
                                    {
                                        waitingConversations++;
                                        
                                        // Determine media type of waiting conversations
                                        if (participant["calls"] != null && participant["calls"].HasValues)
                                            callsWaiting++;
                                        if (participant["chats"] != null && participant["chats"].HasValues)
                                            chatsWaiting++;
                                        if (participant["emails"] != null && participant["emails"].HasValues)
                                            emailsWaiting++;
                                    }
                                }
                            }
                        }
                        
                        // Update row with conversation counts
                        row["activeConversations"] = activeConversations;
                        row["waitingConversations"] = waitingConversations;
                        row["callsActive"] = callsActive;
                        row["callsWaiting"] = callsWaiting;
                        row["chatsActive"] = chatsActive;
                        row["chatsWaiting"] = chatsWaiting;
                        row["emailsActive"] = emailsActive;
                        row["emailsWaiting"] = emailsWaiting;
                        row["updated"] = DateTime.UtcNow;
                        
                        _queueStatusData.Rows.Add(row);
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing queue conversation data");
                return false;
            }
        }

        /// <summary>
        /// Verifies and processes all conversations in a queue when truncated=true is received
        /// This function makes direct API calls to get complete conversation data
        /// </summary>
        /// <param name="queueId">The ID of the queue to verify</param>
        /// <returns>True if verification succeeded, false otherwise</returns>
        private bool VerifyConversationsInQueue(string queueId)
        {
            try
            {
                _logger?.LogInformation("Verifying all conversations in queue {QueueId}", queueId);
                
                // Create the API request to get detailed queue information
                string url = $"{_jsonUtils.ApiEndpoint}/api/v2/analytics/conversations/details/query";
                
                // Set up request parameters
                var requestBody = new
                {
                    interval = $"{DateTime.UtcNow.AddMinutes(-10):yyyy-MM-ddTHH:mm:ss.fffZ}/{DateTime.UtcNow:yyyy-MM-ddTHH:mm:ss.fffZ}",
                    order = "asc",
                    orderBy = "conversationStart",
                    paging = new
                    {
                        pageSize = 100,
                        pageNumber = 1
                    },
                    segmentFilters = new[]
                    {
                        new
                        {
                            type = "and",
                            predicates = new object[] 
                            {
                                new
                                {
                                    type = "dimension",
                                    dimension = "queueId",
                                    @operator = "matches",
                                    value = (object)queueId 
                                },
                                new
                                {
                                    type = "dimension",
                                    dimension = "segmentEnd",
                                    @operator = "notExists",
                                    value = (object)null
                                }
                            }
                        }
                    }
                };
                
                // Make the API request with improved error handling
                string jsonResponse = "";
                try
                {
                    jsonResponse = _jsonUtils.JsonRestAsync(
                        url,
                        null,
                        "POST",
                        JsonConvert.SerializeObject(requestBody)).GetAwaiter().GetResult();
                }
                catch (Exception apiEx)
                {
                    _logger?.LogError(apiEx, "API request failed when verifying conversations for queue {QueueId}", queueId);
                    return false;
                }
                    
                if (string.IsNullOrEmpty(jsonResponse))
                {
                    _logger?.LogError("Empty response when verifying conversations for queue {QueueId}", queueId);
                    return false;
                }
                
                // Process the results
                var responseObject = JsonConvert.DeserializeObject<JObject>(jsonResponse);
                if (responseObject == null || responseObject["conversations"] == null)
                {
                    _logger?.LogError("Invalid response format when verifying conversations for queue {QueueId}", queueId);
                    return false;
                }
                
                // Count active conversations by type
                int activeConversations = 0;
                int waitingConversations = 0;
                int callsActive = 0;
                int callsWaiting = 0;
                int chatsActive = 0;
                int chatsWaiting = 0;
                int emailsActive = 0;
                int emailsWaiting = 0;
                
                var conversations = (JArray)responseObject["conversations"];
                foreach (var conversation in conversations)
                {
                    // Process each conversation
                    if (conversation["participants"] == null)
                        continue;
                        
                    var participants = (JArray)conversation["participants"];
                    string mediaType = conversation["mediaType"]?.ToString() ?? "unknown";
                    bool isWaiting = false;
                    bool isActive = false;
                    
                    // Check participants to determine if conversation is waiting or active
                    foreach (var participant in participants)
                    {
                        string purpose = participant["purpose"]?.ToString();
                        string participantType = participant["participantType"]?.ToString() ?? "";
                        
                        if (purpose == "customer" || purpose == "external")
                        {
                            // Check if this conversation is for the specified queue
                            var segments = participant["segments"] as JArray;
                            if (segments != null)
                            {
                                foreach (var segment in segments)
                                {
                                    if (segment["queueId"]?.ToString() == queueId)
                                    {
                                        // Check if the segment is active (no end time)
                                        if (segment["segmentEnd"] == null || segment["segmentEnd"].Type == JTokenType.Null)
                                        {
                                            isActive = true;
                                            
                                            // Check if it's waiting
                                            if (participantType == "acd" || participantType == "routing")
                                            {
                                                isWaiting = true;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // Count this conversation
                    if (isActive)
                    {
                        activeConversations++;
                        
                        // Count by media type
                        switch (mediaType.ToLower())
                        {
                            case "call":
                            case "voice":
                                callsActive++;
                                break;
                            case "chat":
                                chatsActive++;
                                break;
                            case "email":
                                emailsActive++;
                                break;
                        }
                        
                        // If waiting, count it as waiting too
                        if (isWaiting)
                        {
                            waitingConversations++;
                            
                            // Count by media type
                            switch (mediaType.ToLower())
                            {
                                case "call":
                                case "voice":
                                    callsWaiting++;
                                    break;
                                case "chat":
                                    chatsWaiting++;
                                    break;
                                case "email":
                                    emailsWaiting++;
                                    break;
                            }
                        }
                    }
                }
                
                // Create a new status row
                lock (_queueStatusLock)
                {
                    var row = _queueStatusData.NewRow();
                    row["id"] = $"{queueId}_{DateTime.UtcNow.Ticks}";
                    row["queueId"] = queueId;
                    row["observationDate"] = DateTime.UtcNow;
                    var utils = new Utils();
                    row["observationDateLocalTZ"] = utils.ConvertToLocalTime(DateTime.UtcNow, _timeZone);
                    
                    // Add conversation counts
                    row["activeConversations"] = activeConversations;
                    row["waitingConversations"] = waitingConversations;
                    row["callsActive"] = callsActive;
                    row["callsWaiting"] = callsWaiting;
                    row["chatsActive"] = chatsActive;
                    row["chatsWaiting"] = chatsWaiting;
                    row["emailsActive"] = emailsActive;
                    row["emailsWaiting"] = emailsWaiting;
                    row["updated"] = DateTime.UtcNow;
                    
                    _queueStatusData.Rows.Add(row);
                    
                    _logger?.LogInformation("Successfully verified conversations for queue {QueueId}: {Active} active, {Waiting} waiting", 
                        queueId, activeConversations, waitingConversations);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error verifying conversations for queue {QueueId}", queueId);
                return false;
            }
        }
        
        private DataTable CreateQueueStatusTable()
        {
            // Use the legacy table name "queueconvrealData" instead of "queueStatusRealTime"
            var table = new DataTable("queueconvrealData");
            
            // Basic queue information
            table.Columns.Add("id", typeof(string));
            table.Columns.Add("queueId", typeof(string));
            table.Columns.Add("observationDate", typeof(DateTime));
            table.Columns.Add("observationDateLocalTZ", typeof(DateTime));
            
            // Queue status information - keep all the same column names as in the legacy code
            table.Columns.Add("activeConversations", typeof(int));
            table.Columns.Add("waitingConversations", typeof(int));
            table.Columns.Add("callsActive", typeof(int));
            table.Columns.Add("callsWaiting", typeof(int));
            table.Columns.Add("chatsActive", typeof(int));
            table.Columns.Add("chatsWaiting", typeof(int));
            table.Columns.Add("emailsActive", typeof(int));
            table.Columns.Add("emailsWaiting", typeof(int));
            
            // Track when data was updated
            table.Columns.Add("updated", typeof(DateTime));
            
            // Set primary key
            DataColumn[] keys = new DataColumn[1];
            keys[0] = table.Columns["id"];
            table.PrimaryKey = keys;
            
            return table;
        }

        // Add method to get the current queue status for clients
        public DataTable GetQueueStatus()
        {
            lock (_queueStatusLock)
            {
                // Return a copy of the current queue status data
                DataTable result = _queueStatusData.Copy();
                
                // Join with queue details to include queue names
                if (_queueDetails != null && _queueDetails.Rows.Count > 0)
                {
                    // Create a lookup of queue IDs to names
                    Dictionary<string, string> queueNames = new Dictionary<string, string>();
                    foreach (DataRow row in _queueDetails.Rows)
                    {
                        string id = row["id"].ToString();
                        string name = row["name"].ToString();
                        queueNames[id] = name;
                    }
                    
                    // Add queue names to the result table if it doesn't already have them
                    if (!result.Columns.Contains("queueName"))
                    {
                        result.Columns.Add("queueName", typeof(string));
                    }
                    
                    // Populate queue names
                    foreach (DataRow row in result.Rows)
                    {
                        string queueId = row["queueId"].ToString();
                        if (queueNames.TryGetValue(queueId, out string queueName))
                        {
                            row["queueName"] = queueName;
                        }
                    }
                }
                
                _logger?.LogInformation("Returning status for {Count} queues", result.Rows.Count);
                return result;
            }
        }

        // Get the latest status for each queue (one row per queue)
        public DataTable GetLatestQueueStatus()
        {
            lock (_queueStatusLock)
            {
                // Create a result table with the same schema
                DataTable result = _queueStatusData.Clone();
                
                // Group by queue ID and get the most recent status for each queue
                var queueIds = new HashSet<string>();
                var latestRows = new List<DataRow>();
                
                foreach (DataRow row in _queueStatusData.Rows)
                {
                    string queueId = row["queueId"].ToString();
                    DateTime observationDate = Convert.ToDateTime(row["observationDate"]);
                    
                    // Find the existing row for this queue, if any
                    DataRow existingRow = latestRows.Find(r => r["queueId"].ToString() == queueId);
                    
                    if (existingRow == null)
                    {
                        // No existing row for this queue, add this one
                        latestRows.Add(row);
                        queueIds.Add(queueId);
                    }
                    else
                    {
                        // Compare dates and keep the most recent one
                        DateTime existingDate = Convert.ToDateTime(existingRow["observationDate"]);
                        if (observationDate > existingDate)
                        {
                            // Remove the old row and add the new one
                            latestRows.Remove(existingRow);
                            latestRows.Add(row);
                        }
                    }
                }
                
                // Add all the latest rows to the result table
                foreach (var row in latestRows)
                {
                    result.ImportRow(row);
                }
                
                // Add queue names
                if (_queueDetails != null && _queueDetails.Rows.Count > 0)
                {
                    Dictionary<string, string> queueNames = new Dictionary<string, string>();
                    foreach (DataRow row in _queueDetails.Rows)
                    {
                        string id = row["id"].ToString();
                        string name = row["name"].ToString();
                        queueNames[id] = name;
                    }
                    
                    if (!result.Columns.Contains("queueName"))
                    {
                        result.Columns.Add("queueName", typeof(string));
                    }
                    
                    foreach (DataRow row in result.Rows)
                    {
                        string queueId = row["queueId"].ToString();
                        if (queueNames.TryGetValue(queueId, out string queueName))
                        {
                            row["queueName"] = queueName;
                        }
                    }
                }
                
                return result;
            }
        }

        // Implement missing interface methods
        public async Task InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("Initializing QueueCallDetailsService at {Time}", DateTime.UtcNow);
                await Task.Run(() => Initialize());
                _logger?.LogInformation("QueueCallDetailsService initialization complete");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to initialize QueueCallDetailsService");
                throw;
            }
        }
        
        public async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger?.LogInformation("Starting QueueCallDetailsService");
                await Task.Run(() => Start(), cancellationToken);
                _isRunning = true;
                _logger?.LogInformation("QueueCallDetailsService started successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to start QueueCallDetailsService");
                throw;
            }
        }
        
        public async Task StopAsync()
        {
            try
            {
                _logger?.LogInformation("Stopping QueueCallDetailsService");
                await Task.Run(() => Stop());
                _isRunning = false;
                _logger?.LogInformation("QueueCallDetailsService stopped successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to stop QueueCallDetailsService");
                throw;
            }
        }
        
        public async Task<bool> CheckHealthAsync()
        {
            try
            {
                _logger?.LogInformation("Performing health check for QueueCallDetailsService");
                bool isHealthy = IsHealthy();
                
                // Additional async health checks could be performed here
                if (isHealthy && WebSocketManagers.Count > 0)
                {
                    // Verify at least one WebSocket connection is working
                    bool hasWorkingConnection = WebSocketManagers.Any(m => m.IsConnected);
                    if (!hasWorkingConnection)
                    {
                        _logger?.LogWarning("Health check failed: No working WebSocket connections");
                        isHealthy = false;
                    }
                }
                
                return await Task.FromResult(isHealthy);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Health check failed for QueueCallDetailsService");
                return false;
            }
        }

        // Improve health check to be more comprehensive
        public override bool IsHealthy()
        {
            bool isHealthy = base.IsHealthy();
            
            // Additional health criteria
            if (TotalErrors > 10)
            {
                _logger?.LogWarning("Service health check failed: Too many errors ({ErrorCount})", TotalErrors);
                isHealthy = false;
            }
            
            if (WebSocketManagers.Count == 0)
            {
                _logger?.LogWarning("Service health check failed: No active WebSocket connections");
                isHealthy = false;
            }
            
            if (WebSocketManagers.All(m => !m.IsConnected))
            {
                _logger?.LogWarning("Service health check failed: All WebSocket connections are disconnected");
                isHealthy = false;
            }
            
            return isHealthy && _isRunning;
        }
    }
}
