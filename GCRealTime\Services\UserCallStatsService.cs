using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using GCRealTime.Core;
// Add namespace alias for GCRealTime.Models
using GCRTModels = GCRealTime.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using GenesysCloudUtils;
using GenesysCloudUtils.WebSocket; // Add this for WebSocketDetail
using RealUC = RealUserPushCallStatsDef;

namespace GCRealTime.Services
{
    public class UserCallStatsService : BaseRealTimeService, IRealtimeService
    {
        // Add static counters to persist across instances/recoveries
        private static long _processedEventsCount = 0;
        private static DateTime _lastProcessedEventTime = DateTime.MinValue;
        private readonly object _statsLock = new object(); // Thread safety

        private DataTable _userDetails = new DataTable();
        private DataTable _userData;
        private readonly List<WebSocketDetail> _webSocketList = new List<WebSocketDetail>();
        private readonly List<Task> _socketTasks = new List<Task>();
        private CancellationTokenSource _cts = new CancellationTokenSource(); // Initialize here
        private bool _writeUserData = false;
        private readonly JsonUtils _jsonUtils; // Replace JsonUtilsAdapter
        private int _failedChannelAttempts = 0;
        private const int MAX_CHANNEL_FAILURES = 3;
        private bool _isRunning = false;

        public UserCallStatsService(ILogger _logger, DBUtils.DBUtils dbAdapter) : base(_logger, dbAdapter)
        {
            _jsonUtils = new JsonUtils(_logger);
        }
        
        public override void Initialize()
        {
            base.Initialize();
            
            if (!IsUserActivityServiceRunning())
            {
                _logger?.LogInformation("[CallStats][Init] UserActivityService not running, initializing table");
                DBAdapter.TruncateTable("userRealTimeData", _logger);
            }
            
            _userDetails = GetUsers();
            _userData = CreateCallStatsTable();
            
            // Initialize with any existing call stats data
            try
            {
                string query = "SELECT * FROM userRealTimeData WHERE cccallactive IS NOT NULL OR ccemailactive IS NOT NULL OR ccchatactive IS NOT NULL";
                
                var existingData = DBAdapter.GetSQLTableData(query, "ExistingCallStatsData");
                if (existingData != null && existingData.Rows.Count > 0)
                {
                    _logger?.LogInformation("[CallStats][Init] Found {Count} existing call stats records", existingData.Rows.Count);
                    foreach (DataRow row in existingData.Rows)
                    {
                        _userData.ImportRow(row);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "[CallStats][Init] Could not retrieve existing call stats data");
            }
        }
        private bool IsUserActivityServiceRunning()
        {
            try
            {
                // A simple check - see if there are any records with systemPresence not null
                string query = "SELECT COUNT(*) FROM userRealTimeData WHERE systemPresence IS NOT NULL";
                object result = DBAdapter.ExecuteScalar(query);
                int count = result != null ? Convert.ToInt32(result) : 0;
                
                _logger?.LogDebug("[CallStats][Init] UserActivityService check: {Count} records with systemPresence", count);
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][Init] Error checking if UserActivityService is running");
                // If there's an error, assume it's not running
                return false;
            }
        }
        
        public override void Start()
        {
            try
            {
                _logger?.LogInformation("[CallStats][Start] Starting UserCallStatsService");

                // Reset the CancellationTokenSource rather than creating a new one
                if (_cts != null && !_cts.IsCancellationRequested)
                {
                    _cts.Dispose();
                }
                _cts = new CancellationTokenSource();

                // Reset failure counter on start
                _failedChannelAttempts = 0;

                // Clear any existing connections
                _webSocketList?.Clear();
                WebSocketManagers?.Clear();

                // Adding robust null checking for userDetails
                if (_userDetails == null)
                {
                    _logger?.LogError("[CallStats][Start] UserDetails is null - cannot start service");
                    throw new InvalidOperationException("UserDetails is null - cannot start service");
                }

                if (_userDetails.Rows.Count > MAX_TOPICS_PER_SUBSCRIPTION)
                {
                    var chunks = ChunkDataTable(_userDetails, MAX_TOPICS_PER_SUBSCRIPTION);
                    _logger?.LogInformation("[CallStats][Start] Splitting user call stats into {ChannelCount} channels due to {UserCount} users", 
                        chunks.Count, _userDetails.Rows.Count);

                    int chunkIndex = 1;
                    int failedChunks = 0;
                    foreach (var chunk in chunks)
                    {
                        WebSocketDetail socket = null;
                        try
                        {
                            socket = CreateChannel($"userCallStats-{chunkIndex}");
                            if (socket == null)
                            {
                                _logger?.LogError("[CallStats][Start] Failed to create channel for chunk {ChunkIndex}", chunkIndex);
                                failedChunks++;
                                continue; // Skip to the next chunk
                            }
                            
                            _webSocketList.Add(socket);
                            
                            _logger?.LogInformation("[CallStats][Channel:{ChannelId}] Creating subscriptions for chunk {ChunkIndex}", 
                                socket.id, chunkIndex);
                                
                            CreateUserCallStatsSubscriptionsForChunk(socket, chunk);
                            
                            Thread thread = new Thread(() => {
                                try 
                                {
                                    CreateWebSocket(socket.connectUri, socket.id, $"userCallStats-{chunkIndex}");
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogError(ex, "[CallStats][Start] Error creating WebSocket for channel {ChannelId}", socket.id);
                                    Interlocked.Increment(ref _failedChannelAttempts);
                                    CheckFailureThreshold();
                                }
                            });
                            thread.Name = $"userCallStats-{chunkIndex}";
                            thread.Start();
                            WebSocketThreads.Add(thread);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "[CallStats][Start] Error setting up channel for chunk {ChunkIndex}", chunkIndex);
                            failedChunks++;
                            Interlocked.Increment(ref _failedChannelAttempts);
                            // Continue to next chunk instead of failing the whole service
                        }
                        
                        chunkIndex++;
                    }
                    
                    // Check if too many chunks failed
                    if (failedChunks > 0)
                    {
                        _logger?.LogWarning("[CallStats][Start] {FailedCount} out of {TotalCount} channel chunks failed to initialize", 
                            failedChunks, chunks.Count);
                        
                        if (failedChunks == chunks.Count)
                        {
                            // All chunks failed, increment the global failure counter
                            Interlocked.Add(ref _failedChannelAttempts, MAX_CHANNEL_FAILURES);
                            CheckFailureThreshold();
                        }
                    }
                }
                else
                {
                    WebSocketDetail socket = null;
                    try
                    {
                        socket = CreateChannel("userCallStats");
                        if (socket == null)
                        {
                            _logger?.LogError("[CallStats][Start] Failed to create channel");
                            return; // Exit if we can't create the channel
                        }
                        
                        _webSocketList.Add(socket);
                        
                        _logger?.LogInformation("[CallStats][Channel:{ChannelId}] Creating subscriptions", socket.id);
                        
                        CreateUserCallStatsSubscriptions(socket);
                        
                        Thread thread = new Thread(() => {
                            try
                            {
                                CreateWebSocket(socket.connectUri, socket.id, "userCallStats");
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogError(ex, "[CallStats][Start] Error creating WebSocket for channel {ChannelId}", socket.id);
                                Interlocked.Increment(ref _failedChannelAttempts);
                                CheckFailureThreshold();
                            }
                        });
                        thread.Name = "userCallStats";
                        thread.Start();
                        WebSocketThreads.Add(thread);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[CallStats][Start] Error setting up channel");
                        Interlocked.Increment(ref _failedChannelAttempts);
                        CheckFailureThreshold();
                        throw; // We only have one channel, so if it fails, the service can't start
                    }
                }
                
                _isRunning = true; // Set _isRunning to true
                
                _logger?.LogInformation("[CallStats][Start] UserCallStatsService started successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][Start] Failed to start service. Exception: {ExceptionMessage}", ex.Message);
                Interlocked.Increment(ref _failedChannelAttempts);
                CheckFailureThreshold();
                throw; // Rethrow to signal that the service failed to start
            }
        }

        public async Task InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("[CallStats][Init] Initializing UserCallStatsService asynchronously at {Time}", DateTime.UtcNow);
                await Task.Run(() => Initialize());
                _logger?.LogInformation("[CallStats][Init] Initialization complete at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][Init] Failed to initialize service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }
        
        public async Task StartAsync(CancellationToken token)
        {
            try
            {
                _logger?.LogInformation("[CallStats][Start] Starting UserCallStatsService asynchronously at {Time}", DateTime.UtcNow);
                await Task.Run(() => Start(), token);
                _logger?.LogInformation("[CallStats][Start] Started successfully at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][Start] Failed to start service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }
        
        public async Task StopAsync()
        {
            try
            {
                _logger?.LogInformation("[UserCallStats][Stop] Stopping UserCallStatsService asynchronously at {Time}", DateTime.UtcNow);
                _isRunning = false;

                // Check if _cts is null before trying to cancel it
                if (_cts != null)
                {
                    _cts.Cancel();
                }

                // Wait for all tasks to complete
                if (_socketTasks != null && _socketTasks.Any())
                {
                    try
                    {
                        await Task.WhenAll(_socketTasks);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[CallStats][Stop] Error waiting for socket tasks to complete");
                    }
                }

                // Clean up WebSocket managers
                if (WebSocketManagers != null)
                {
                    foreach (var manager in WebSocketManagers)
                    {
                        try
                        {
                            manager.StopWebSocketConnection();
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "[CallStats][Stop] Error stopping WebSocket manager");
                        }
                    }
                    WebSocketManagers.Clear();
                }

                _webSocketList?.Clear();

                // Dispose of CancellationTokenSource
                _cts?.Dispose();
                _cts = null;

                _logger?.LogInformation("[UserCallStats][Stop] Stopped successfully at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallStats][Stop] Failed to stop service. Exception: {ExceptionMessage}", ex.Message);
                throw;
            }
        }

        public async Task<bool> CheckHealthAsync()
        {
            try 
            {
                _logger?.LogInformation("[UserCallStats][Health] Performing health check");
                
                if (!_isRunning)
                {
                    _logger?.LogWarning("[UserCallStats][Health] Service is not running");
                    return false;
                }
                
                bool hasActiveConnections = WebSocketManagers != null && 
                                           WebSocketManagers.Count > 0 &&
                                           WebSocketManagers.Any(m => m.IsConnected);
                                           
                if (!hasActiveConnections)
                {
                    _logger?.LogWarning("[UserCallStats][Health] No active WebSocket connections");
                    
                    // Attempt restart using the service's own restart method
                    await RestartMainChannel("userCallStats");
                    
                    // Check if recovery was successful
                    hasActiveConnections = WebSocketManagers != null && 
                                          WebSocketManagers.Count > 0 &&
                                          WebSocketManagers.Any(m => m.IsConnected);
                                          
                    if (!hasActiveConnections)
                    {
                        _logger?.LogError("[UserCallStats][Health] Recovery failed - service remains unhealthy");
                        return false;
                    }
                    
                    _logger?.LogInformation("[UserCallStats][Health] Recovery successful - service is now healthy");
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallStats][Health] Health check failed");
                return false;
            }
        }
        
        private async Task RestartMainChannel(string channelName)
        {
            try
            {
                _logger?.LogInformation("[CallStats][Restart] Attempting to restart main channel");
                
                // Clean up existing connections
                foreach (var manager in WebSocketManagers.ToList())
                {
                    try
                    {
                        manager.StopWebSocketConnection();
                        WebSocketManagers.Remove(manager);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[CallStats][Restart] Error stopping WebSocket manager");
                    }
                }
                
                // Clear WebSocket threads
                WebSocketThreads.Clear();
                
                // Create a new main channel
                WebSocketDetail socket = CreateChannel(channelName);
                if (socket != null)
                {
                    _webSocketList.Add(socket);
                    
                    // Create subscriptions
                    CreateUserCallStatsSubscriptions(socket);
                    
                    // Start WebSocket on a new thread
                    Thread thread = new Thread(() => CreateWebSocket(socket.connectUri, socket.id, channelName));
                    thread.Name = channelName;
                    thread.IsBackground = true;
                    thread.Start();
                    WebSocketThreads.Add(thread);
                    
                    // Give the connection time to establish
                    await Task.Delay(2000);
                    
                    _logger?.LogInformation("[CallStats][Restart] Successfully restarted main channel");
                }
                else
                {
                    _logger?.LogError("[CallStats][Restart] Failed to create new channel");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][Restart] Error restarting main channel");
            }
        }

        protected override void RefreshChannels()
        {
            try
            {
                foreach (var channel in _webSocketList)
                {
                    if (channel.IsExpired)
                    {
                        _logger?.LogWarning($"Channel has expired: {channel}");
                    }
                    else if (channel.NeedsRefresh)
                    {
                        if (channel.ReportName.Contains("-"))
                        {
                            string[] parts = channel.ReportName.Split('-');
                            if (parts.Length > 1 && int.TryParse(parts[1], out int chunkIndex) && chunkIndex > 0)
                            {
                                var chunks = ChunkDataTable(_userDetails, MAX_TOPICS_PER_SUBSCRIPTION);
                                if (chunkIndex <= chunks.Count)
                                {
                                    DataTable userChunk = chunks[chunkIndex - 1];
                                    RefreshSubscriptionsForChannel(channel, userChunk);
                                }
                            }
                        }
                        else
                        {
                            RefreshSubscriptionsForChannel(channel, _userDetails);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats] Error refreshing channels");
                Interlocked.Increment(ref _failedChannelAttempts);
                CheckFailureThreshold();
            }
        }
        
        private void RefreshSubscriptionsForChannel(WebSocketDetail webSocket, DataTable userChunk)
        {
            try
            {
                _logger?.LogInformation("[CallStats][Channel:{ChannelId}] Refreshing subscriptions", webSocket.id);
                
                string deleteUrl = $"/api/v2/notifications/channels/{webSocket.id}/subscriptions";
                _jsonUtils.JsonReturn(_jsonUtils.ApiEndpoint + deleteUrl, null, "DELETE");
                
                CreateUserCallStatsSubscriptionsForChunk(webSocket, userChunk);
                
                _logger?.LogInformation("[CallStats][Channel:{ChannelId}] Successfully refreshed subscriptions", webSocket.id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][Channel:{ChannelId}] Failed to refresh subscriptions", webSocket.id);
            }
        }
        
        private void CreateUserCallStatsSubscriptions(WebSocketDetail webSocket)
        {
            if (webSocket == null)
            {
                _logger?.LogError("[CallStats] Cannot create subscriptions - WebSocket is null");
                return;
            }
            
            try
            {
                if (_userDetails == null || _userDetails.Rows.Count == 0)
                {
                    _logger?.LogWarning("[CallStats] User table is empty - no subscriptions to create");
                    return;
                }

                _logger?.LogInformation("[CallStats] Creating subscriptions for {Count} users on channel {ChannelId}", 
                    _userDetails.Rows.Count, webSocket.id);

                // Use CombinedSubscriptionHelper directly
                GenesysCloudUtils.WebSocket.CombinedSubscriptionHelper
                    .CreateUserSubscriptionsAsync(
                        _logger,
                        _jsonUtils,
                        _userDetails,
                        webSocket,
                        "v2.users.",
                        ".conversationsummary")
                    .GetAwaiter()
                    .GetResult();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats] Error creating subscriptions");
                throw;
            }
        }
        
        // Fix the CreateUserCallStatsSubscriptionsForChunk method to use async Task properly
        private async Task CreateUserCallStatsSubscriptionsForChunk(WebSocketDetail webSocket, DataTable userChunk) // Changed parameter type
        {
            try
            {
                if (webSocket == null)
                {
                    _logger?.LogError("[CallStats] Cannot create subscriptions for chunk - WebSocket is null");
                    return;
                }

                if (userChunk == null || userChunk.Rows.Count == 0)
                {
                    _logger?.LogWarning("[CallStats] User chunk is empty - no subscriptions to create");
                    return;
                }
                
                _logger?.LogInformation("[CallStats][Channel:{ChannelId}] Creating call stats subscriptions for {Count} users", 
                    webSocket.id, userChunk.Rows.Count);

                // Ensure the JsonUtils instance is available
                if (_jsonUtils == null)
                {
                    _logger?.LogError("[CallStats] JsonUtils is null - cannot create subscriptions");
                    return;
                }

                // Use the helper to create combined subscriptions following best practices
                GenesysCloudUtils.WebSocket.CombinedSubscriptionHelper
                    .CreateUserSubscriptionsAsync(
                        _logger,
                        _jsonUtils,
                        userChunk,
                        webSocket,
                        "v2.users.",
                        ".conversationsummary")
                    .GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats] Error creating call stats subscriptions for chunk");
                throw;
            }
        }

        protected override void CreateWebSocket(string socketAddress, string socketChannel, string threadName)
        {
            var detail = new WebSocketDetail
            {
                connectUri = socketAddress,
                id = socketChannel,
                ReportName = threadName,
                Created = DateTime.UtcNow,
                Expires = DateTime.UtcNow.AddHours(1)
            };

            var wsManager = WebSocketManagerFactory.CreateWebSocketManager(
                _logger, detail, threadName, ReceiveData, _jsonUtils);

            WebSocketManagers.Add(wsManager);
            wsManager.StartWebSocketConnection();
            
            _logger?.LogInformation("[UserCallStats][WebSocket:{ChannelId}][Thread:{ThreadName}] Created WebSocket connection", 
                socketChannel, threadName);
        }
        
        public void ReceiveData(string jsonString, string threadName)
        {
            try 
            {
                // Add a direct log entry to show what's being received
                _logger?.LogDebug("[CallStats][Thread:{ThreadName}] Received data: {JsonPreview}",
                    threadName, jsonString.Length > 100 ? jsonString.Substring(0, 100) + "..." : jsonString);

                // Always increment the event counter for any message received - with thread safety
                lock (_statsLock)
                {
                    _processedEventsCount++;
                    _lastProcessedEventTime = DateTime.UtcNow;

                    // Update base class properties immediately
                    EventsProcessed = _processedEventsCount;
                    LastActivity = _lastProcessedEventTime;
                }

                // Log an explicit event count message periodically (every 10 events)
                if (_processedEventsCount % 10 == 0)
                {
                    _logger?.LogInformation("[CallStats] Total events processed: {Count}, last activity: {LastActivity}",
                        _processedEventsCount, _lastProcessedEventTime);
                }
                
                // Fix: Change 'contains' to 'Contains' (uppercase C) - C# is case sensitive
                if (jsonString.Contains("topicName") && jsonString.IndexOf("v2.users") > 0 && 
                    jsonString.IndexOf("conversationsummary") > 0)
                {
                    TranslateCallStats(jsonString, threadName);
                }
                
                if (jsonString.IndexOf("WebSocket Heartbeat") > 0 || 
                    (jsonString.Contains("\"topicName\": \"channel.metadata\"") && 
                     jsonString.Contains("\"message\": \"pong\"")))
                {
                    _logger?.LogDebug("[CallStats][Thread:{ThreadName}] Received heartbeat at {Time}", threadName, DateTime.Now);
                    // Reset error counters on successful heartbeat
                    TotalErrors = 0;
                }
                
                if (_writeUserData && _userData.Rows.Count > 0)
                {
                    try
                    {
                        // Replace direct database call with thread-safe method, using our new wrapper
                        SafeWriteToDatabaseWithLogging(_userData, "user call stats").Wait();
                        _writeUserData = false;
                        _logger?.LogInformation("[CallStats][Thread:{ThreadName}][DB] Successfully wrote {Count} user call stats records to database",
                            threadName, _userData.Rows.Count);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[CallStats][Thread:{ThreadName}][DB] Error writing user call stats to database", threadName);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][Thread:{ThreadName}] Error processing data", threadName);
            }
        }

        // Add method to track entity activity like the adherence service has
        private void TrackEntityActivity(string entityId, string entityName, string entityType)
        {
            try
            {
                // Increment the processed events counter
                Interlocked.Increment(ref _processedEventsCount);

                // Update the last processed time
                _lastProcessedEventTime = DateTime.UtcNow;

                _logger?.LogDebug("[CallStats] Recorded activity for {EntityType} {EntityName} ({EntityId})",
                    entityType, entityName, entityId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats] Error tracking entity activity for {EntityType} {EntityName}", entityType, entityName);
            }
        }

        // Update TranslateCallStats to track entity activity
        private bool TranslateCallStats(string jsonString, string threadName)
        {
            bool successful = false;

            try
            {
                RealUC.CallStats userCalls = JsonConvert.DeserializeObject<RealUC.CallStats>(
                    jsonString,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }
                );
                
                if (userCalls?.eventBody == null)
                {
                    _logger?.LogWarning("[CallStats][Thread:{ThreadName}] Received call stats with null eventBody", threadName);
                    return false;
                }

                // Extract user ID from topic name since it's not in the event body
                string userId = userCalls.topicName.Split('.')[2];
                RealUC.Eventbody callStatsBody = userCalls.eventBody;

                // Look up user name
                string userName = "Unknown User";
                if (_userDetails != null)
                {
                    var userRow = _userDetails.AsEnumerable()
                        .FirstOrDefault(r => r.Field<string>("id") == userId);
                    if (userRow != null)
                    {
                        userName = userRow.Field<string>("name") ?? "Unknown User";
                    }
                }

                lock (_userData)
                {
                    DataRow userRow = _userData.Select($"id = '{userId}'").FirstOrDefault();
                    
                    if (userRow == null)
                    {
                        userRow = _userData.NewRow();
                        userRow["id"] = userId;
                        _userData.Rows.Add(userRow);
                    }
                    
                    // Update call center voice calls
                    userRow["cccallactive"] = callStatsBody.call.contactCenter.active;
                    userRow["cccallacw"] = callStatsBody.call.contactCenter.acw;
                    
                    // Update enterprise voice calls
                    if (callStatsBody.call.enterprise != null)
                    {
                        userRow["othcallactive"] = callStatsBody.call.enterprise.active;
                        userRow["othcallacw"] = callStatsBody.call.enterprise.acw;
                    }
                    else
                    {
                        userRow["othcallactive"] = 0;
                        userRow["othcallacw"] = 0;
                    }
                    
                    // Update callbacks
                    userRow["cbcallactive"] = callStatsBody.callback.contactCenter.active;
                    userRow["cbcallacw"] = callStatsBody.callback.contactCenter.acw;
                    
                    if (callStatsBody.callback.enterprise != null)
                    {
                        userRow["cbothcallactive"] = callStatsBody.callback.enterprise.active;
                        // Fix: Enterprise1 doesn't have 'acw' property - use 'active' as fallback
                        // Or add a check if the property is missing
                        userRow["cbothcallacw"] = callStatsBody.callback.enterprise.active; // Using active instead of acw
                    }
                    else
                    {
                        userRow["cbothcallactive"] = 0;
                        userRow["cbothcallacw"] = 0;
                    }
                    
                    // Update emails
                    userRow["ccemailactive"] = callStatsBody.email.contactCenter.active;
                    userRow["ccemailacw"] = callStatsBody.email.contactCenter.acw;
                    
                    if (callStatsBody.email.enterprise != null)
                    {
                        userRow["othemailactive"] = callStatsBody.email.enterprise.active;
                        userRow["othemailacw"] = callStatsBody.email.enterprise.acw;
                    }
                    else
                    {
                        userRow["othemailactive"] = 0;
                        userRow["othemailacw"] = 0;
                    }
                    
                    // Update chats
                    userRow["ccchatactive"] = callStatsBody.chat.contactCenter.active;
                    userRow["ccchatacw"] = callStatsBody.chat.contactCenter.acw;
                    
                    if (callStatsBody.chat.enterprise != null)
                    {
                        userRow["othchatactive"] = callStatsBody.chat.enterprise.active;
                        userRow["othchatacw"] = callStatsBody.chat.enterprise.acw;
                    }
                    else
                    {
                        userRow["othchatactive"] = 0;
                        userRow["othchatacw"] = 0;
                    }
                    
                    // Update the "updated" field as well to ensure schema compatibility
                    userRow["updated"] = DateTime.UtcNow;
                    
                    _logger?.LogDebug("[CallStats][Thread:{ThreadName}][DataProcessing] Updated call stats for user {UserId}", 
                        threadName, userId);
                    _writeUserData = true;
                    successful = true;
                }
                
                // Track entity activity after updating data
                TrackEntityActivity(userId, userName, "User");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][Thread:{ThreadName}][DataProcessing] Error translating call stats", threadName);
                successful = false;
            }
            
            return successful;
        }

        private async Task SafeWriteToDatabaseWithLogging(DataTable data, string dataDescription)
        {
            try
            {
                _logger?.LogInformation("[CallStats][DB] Writing {Count} {Description} records to database", 
                    data.Rows.Count, dataDescription);
                await base.SafeWriteToDatabase(data, dataDescription);
                _logger?.LogInformation("[CallStats][DB] Successfully wrote {Count} {Description} records", 
                    data.Rows.Count, dataDescription);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][DB] Error writing {Description} data to database", dataDescription);
                throw;
            }
        }

        private DataTable GetUsers()
        {
            // This query doesn't need database-specific syntax as it's a simple SELECT
            return DBAdapter.GetSQLTableData("select id,name from userDetails where state = 'active'", "UserDetails");
        }
        
        private DataTable CreateCallStatsTable()
        {
            // Changed: Create table with structure of userRealTimeData
            DataTable dtTemp = new DataTable();
            dtTemp.TableName = "userRealTimeData";
            
            // Required ID field
            dtTemp.Columns.Add("id", typeof(string));
            
            // Remove timeStamp and only use updated
            dtTemp.Columns.Add("updated", typeof(DateTime));
            
            // Call stats fields
            dtTemp.Columns.Add("cccallactive", typeof(int));
            dtTemp.Columns.Add("cccallacw", typeof(int));
            dtTemp.Columns.Add("othcallactive", typeof(int));
            dtTemp.Columns.Add("othcallacw", typeof(int));
            dtTemp.Columns.Add("cbcallactive", typeof(int));
            dtTemp.Columns.Add("cbcallacw", typeof(int));
            dtTemp.Columns.Add("cbothcallactive", typeof(int));
            dtTemp.Columns.Add("cbothcallacw", typeof(int));
            dtTemp.Columns.Add("ccemailactive", typeof(int));
            dtTemp.Columns.Add("ccemailacw", typeof(int));
            dtTemp.Columns.Add("othemailactive", typeof(int));
            dtTemp.Columns.Add("othemailacw", typeof(int));
            dtTemp.Columns.Add("ccchatactive", typeof(int));
            dtTemp.Columns.Add("ccchatacw", typeof(int));
            dtTemp.Columns.Add("othchatactive", typeof(int));
            dtTemp.Columns.Add("othchatacw", typeof(int));
            
            // Other fields that might be in userRealTimeData
            dtTemp.Columns.Add("routingStatus", typeof(string));
            dtTemp.Columns.Add("routstarttime", typeof(DateTime));
            dtTemp.Columns.Add("systemPresence", typeof(string));
            dtTemp.Columns.Add("presenceId", typeof(string));
            dtTemp.Columns.Add("presstarttime", typeof(DateTime));
            
            // Primary key
            dtTemp.PrimaryKey = new DataColumn[] { dtTemp.Columns["id"] };
            
            return dtTemp;
        }

        public async Task<string> GetCallStatsAsync(string url, string jsonBody)
        {
            var response = await _jsonUtils.JsonReturnAsync(_jsonUtils.ApiEndpoint + url, null, "GET", jsonBody);
            return response.Count() > 0 ? response.ToString() : string.Empty;
        }

        private void CheckFailureThreshold()
        {
            if (_failedChannelAttempts >= MAX_CHANNEL_FAILURES)
            {
                _logger?.LogCritical("[CallStats] Service has failed {FailCount} times - exceeding the threshold of {MaxFailures}. Exiting application.", 
                    _failedChannelAttempts, MAX_CHANNEL_FAILURES);
                
                // Notify and terminate the entire application after graceful cleanup
                Task.Run(async () => {
                    try 
                    {
                        await StopAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[CallStats] Error during emergency shutdown");
                    }
                    finally
                    {
                        // Force application to terminate
                        Environment.Exit(1);
                    }
                });
            }
        }

        public int GetActiveConnectionCount()
        {
            return WebSocketManagers?.Count(m => m.IsConnected) ?? 0;
        }

        public override Dictionary<string, object> GetServiceMetrics()
        {
            lock (_statsLock)
            {
                // Update the base class properties with our local counters
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }

            // Get base metrics (which will now include our updated values)
            var metrics = base.GetServiceMetrics();

            // Directly insert our values to be extra safe
            metrics["ProcessedEvents"] = _processedEventsCount;
            metrics["EventsProcessed"] = _processedEventsCount;
            metrics["LastEventTime"] = _lastProcessedEventTime;
            metrics["TotalWebSockets"] = WebSocketManagers?.Count ?? 0;
            metrics["ActiveWebSockets"] = GetActiveConnectionCount();
            metrics["ChannelsCreated"] = _webSocketList?.Count ?? 0;

            return metrics;
        }

        private void RecoverConnections()
        {
            try
            {
                _logger?.LogInformation("[UserCallStats][Recover] Attempting to recover WebSocket connections");
                
                // Close any existing connections
                if (WebSocketManagers != null)
                {
                    foreach (var manager in WebSocketManagers)
                    {
                        manager.StopWebSocketConnection();
                    }
                    WebSocketManagers.Clear();
                }

                // Create new connection
                WebSocketDetail socket = CreateChannel("userCallStats");
                if (socket != null)
                {
                    var wsManager = WebSocketManagerFactory.CreateWebSocketManager(
                        _logger, socket, "userCallStats", ReceiveData, _jsonUtils);
                    WebSocketManagers.Add(wsManager);
                    wsManager.StartWebSocketConnection();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallStats][Recover] Failed to recover connections");
                throw;
            }
        }

        protected override async Task PollDataAsync()
        {
            try
            {
                var retryCount = 0;
                const int maxRetries = 5;
                const int baseDelay = 1000; // Start with 1 second delay
                
                // Store reference to _cts to avoid threading issues
                var cancellationToken = _cts?.Token ?? CancellationToken.None;

                while (!cancellationToken.IsCancellationRequested && retryCount < maxRetries)
                {
                    try
                    {
                        if (WebSocketManagers == null || !WebSocketManagers.Any(m => m.IsConnected))
                        {
                            var delay = (int)(baseDelay * Math.Pow(2, retryCount));
                            _logger?.LogWarning("[UserCallStats][Poll] No active WebSocket connections. Attempting reconnect in {Delay}ms (Attempt {Attempt}/{MaxAttempts})",
                                delay, retryCount + 1, maxRetries);

                            await Task.Delay(delay, cancellationToken);
                            await Task.Run(() => RecoverConnections(), cancellationToken);
                            retryCount++;
                        }
                        else
                        {
                            retryCount = 0;
                            await Task.Delay(1000, cancellationToken);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[UserCallStats][Poll] Error during polling iteration");
                        retryCount++;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UserCallStats][Poll] Error during polling");
            }
        }

        // Override ProcessDatabaseWrites to use service-specific log prefix
        protected override void ProcessDatabaseWrites()
        {
            try
            {
                _logger?.LogDebug("[CallStats][DB] Processing database writes");
                base.ProcessDatabaseWrites();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][DB] Error processing database writes");
            }
        }

        // Override RecoverFailedConnectionsAsync to use service-specific log prefix
        protected override async Task RecoverFailedConnectionsAsync()
        {
            try
            {
                _logger?.LogInformation("[CallStats][Recover] Attempting to recover failed connections");
                await base.RecoverFailedConnectionsAsync();
                _logger?.LogInformation("[CallStats][Recover] Recovery attempt completed");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CallStats][Recover] Error during connection recovery");
            }
        }

        // Override FlushMetrics to use service-specific log prefix
        public override void FlushMetrics()
        {
            lock (_statsLock)
            {
                // Update the base class properties with our local counters
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }

            // Call base method second to avoid our values getting overwritten
            base.FlushMetrics();

            // Add explicit logging to show what metrics are being reported
            _logger?.LogInformation("[CallStats][Metrics] Current metrics snapshot: Events={EventCount}, LastActivity={LastActivity}",
                _processedEventsCount, _lastProcessedEventTime.ToString("yyyy-MM-dd HH:mm:ss"));
        }

        // Thread-safe method to increment event counters
        private void IncrementEventsCount()
        {
            lock (_statsLock)
            {
                // Increment our counter directly
                _processedEventsCount++;
                _lastProcessedEventTime = DateTime.UtcNow;

                // Update base class properties
                EventsProcessed = _processedEventsCount;
                LastActivity = _lastProcessedEventTime;
            }

            // Call the base implementation
            IncrementEventsProcessed();
        }
    }
}
