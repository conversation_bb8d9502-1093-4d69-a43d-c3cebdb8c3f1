using System;

namespace GCRealTime
{
    public class ServiceMetrics
    {
        public string ServiceType { get; set; }
        public int EventsProcessed { get; set; }
        public int FailedEvents { get; set; }
        public double ErrorRate { get; set; }
        public double UptimeSeconds { get; set; }
        public DateTime LastEventTimestamp { get; set; }
        public int WebSocketCount { get; set; }
        public int ErrorCount { get; set; }
    }
}
