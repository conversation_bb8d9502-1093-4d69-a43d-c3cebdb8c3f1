# Adding New Realtime Services

This guide explains how to add new realtime services to the Genesys Cloud Adapter. The adapter uses a modular, service-based approach that allows for easy extension with new functionality.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Creating a New Service](#creating-a-new-service)
- [Implementing Required Methods](#implementing-required-methods)
- [Processing Realtime Data](#processing-realtime-data)
- [Integration with GCRealtimeManager](#integration-with-gcrealtimemanager)
- [Testing Your Service](#testing-your-service)
- [Best Practices](#best-practices)

## Architecture Overview

The realtime services architecture consists of:

1. **GCRealtimeManager**: Central coordinator for all realtime services
2. **BaseRealTimeService**: Abstract base class that handles common functionality
3. **Service Classes**: Individual services implementing specific functionality (like UserActivityService)
4. **WebSocket Management**: Classes for handling WebSocket connections and notifications
5. **Configuration**: System for managing service settings

## Creating a New Service

### Step 1: Create a new service class in the Services directory

```csharp
// filepath: g:\My Drive\Documents\repos\customerscience\genesys-adapter\GCRealTime\Services\MyNewService.cs

using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Threading;
using GCRealTime.Core;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace GCRealTime.Services
{
    public class MyNewService : BaseRealTimeService
    {
        private DataTable _mainData;
        private List<WebSocketDetail> _webSocketList = new List<WebSocketDetail>();
        private bool _writeData = false;
        
        // Constructor must take ILogger parameter
        public MyNewService(ILogger logger) : base(logger)
        {
        }
        
        // Override Initialize to set up your service's data
        public override void Initialize()
        {
            // Call base initialization first
            base.Initialize();
            
            // Service-specific initialization
            _mainData = CreateDataTable();
            
            // Load any existing data if needed
            LoadExistingData();
        }
        
        // Create your data table to store service data
        private DataTable CreateDataTable()
        {
            DataTable dt = new DataTable();
            dt.TableName = "myServiceData";
            dt.Columns.Add("id", typeof(string));
            dt.Columns.Add("name", typeof(string));
            dt.Columns.Add("value", typeof(string));
            dt.Columns.Add("timestamp", typeof(DateTime));
            return dt;
        }
        
        // Example of loading existing data from database
        private void LoadExistingData()
        {
            try
            {
                var existingData = DBAdapter.GetSQLTableData("SELECT * FROM myServiceData", "ExistingData");
                if (existingData != null && existingData.Rows.Count > 0)
                {
                    _logger?.LogInformation($"Found {existingData.Rows.Count} existing records");
                    foreach (DataRow row in existingData.Rows)
                    {
                        _mainData.ImportRow(row);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Could not retrieve existing data");
            }
        }
        
        // Implement the Start method to begin service operation
        public void Start()
        {
            // More implementation follows...
        }
    }
}
```

## Implementing Required Methods

### Step 2: Implement the Start method to create WebSocket connections

```csharp
public void Start()
{
    // Clear existing connections
    _webSocketList.Clear();
    WebSocketThreads.Clear();

    // Get the data source (e.g., users, queues) to subscribe to
    var sourceData = GetSourceData();

    // Check if we need multiple WebSocket connections due to topic limits
    if (sourceData.Rows.Count > MAX_TOPICS_PER_SUBSCRIPTION)
    {
        var chunks = ChunkDataTable(sourceData, MAX_TOPICS_PER_SUBSCRIPTION);
        _logger?.LogInformation($"Splitting service into {chunks.Count} channels due to {sourceData.Rows.Count} items");

        int chunkIndex = 1;
        foreach (var chunk in chunks)
        {
            WebSocketDetail socket = CreateChannel($"myService-{chunkIndex}");
            _webSocketList.Add(socket);
            
            CreateSubscriptionsForChunk(socket, chunk);
            
            Thread thread = new Thread(() => CreateWebSocket(socket.connectUri, socket.id, $"myService-{chunkIndex}"));
            thread.Name = $"myService-{chunkIndex}";
            thread.Start();
            WebSocketThreads.Add(thread);
            
            chunkIndex++;
        }
    }
    else
    {
        WebSocketDetail socket = CreateChannel("myService");
        _webSocketList.Add(socket);
        CreateSubscriptions(socket);
        
        Thread thread = new Thread(() => CreateWebSocket(socket.connectUri, socket.id, "myService"));
        thread.Name = "myService";
        thread.Start();
        WebSocketThreads.Add(thread);
    }
}

// Get source data (e.g., users or queues to monitor)
private DataTable GetSourceData()
{
    return DBAdapter.GetSQLTableData("[your query here]", "SourceData");
}

// Method to create subscriptions for all data
private void CreateSubscriptions(WebSocketDetail webSocket)
{
    CreateSubscriptionsForChunk(webSocket, GetSourceData());
}
```

### Step 3: Implement subscription creation

```csharp
private void CreateSubscriptionsForChunk(WebSocketDetail webSocket, DataTable dataChunk)
{
    _logger?.LogInformation($"Creating subscriptions for {dataChunk.Rows.Count} items");

    StringBuilder subscriptionJson = new StringBuilder();
    int counter = 0;
    
    foreach (DataRow row in dataChunk.Rows)
    {
        // Format will depend on the notification type you need
        subscriptionJson.Append($" \n{{ \"id\": \"v2.your.topic.{row["id"]}.subtopic\"}},");
        counter++;
    }

    if (counter > 0)
    {
        subscriptionJson.Length = subscriptionJson.Length - 1; // Remove last comma
        string jsonBody = $"[{subscriptionJson}]";
        string url = $"/api/v2/notifications/channels/{webSocket.id}/subscriptions";

        _logger?.LogDebug($"Sending subscription request for {counter} items to channel {webSocket.id}");
        string response = ChilKatJsonObj.ReturnJson(url, jsonBody);
        
        if (string.IsNullOrEmpty(response) || response.Contains("error"))
        {
            _logger?.LogError($"Failed to create subscription: {response}");
        }
    }
}
```

### Step 4: Implement channel refresh mechanism

```csharp
// Override RefreshChannels to keep subscriptions active
protected override void RefreshChannels()
{
    foreach (var channel in _webSocketList)
    {
        if (channel.IsExpired)
        {
            _logger?.LogWarning($"Channel has expired: {channel}");
        }
        else if (channel.NeedsRefresh)
        {
            _logger?.LogInformation($"Refreshing channel that expires soon: {channel}");
            
            // Find the appropriate chunk for this channel
            DataTable dataChunk;
            if (channel.ReportName.Contains("-"))
            {
                string[] parts = channel.ReportName.Split('-');
                if (parts.Length > 1 && int.TryParse(parts[1], out int chunkIndex) && chunkIndex > 0)
                {
                    var chunks = ChunkDataTable(GetSourceData(), MAX_TOPICS_PER_SUBSCRIPTION);
                    if (chunkIndex <= chunks.Count)
                    {
                        dataChunk = chunks[chunkIndex - 1];
                        RefreshSubscriptionsForChannel(channel, dataChunk);
                    }
                }
            }
            else
            {
                RefreshSubscriptionsForChannel(channel, GetSourceData());
            }
        }
    }
}

private void RefreshSubscriptionsForChannel(WebSocketDetail webSocket, DataTable dataChunk)
{
    try
    {
        _logger?.LogInformation($"Refreshing subscriptions for channel {webSocket.id}");
        
        string deleteUrl = $"/api/v2/notifications/channels/{webSocket.id}/subscriptions";
        ChilKatJsonObj.ReturnJson(deleteUrl, "", "DELETE");
        
        CreateSubscriptionsForChunk(webSocket, dataChunk);
        
        _logger?.LogInformation($"Successfully refreshed subscriptions for channel {webSocket.id}");
    }
    catch (Exception ex)
    {
        _logger?.LogError(ex, $"Failed to refresh subscriptions for channel {webSocket.id}");
    }
}
```

## Processing Realtime Data

### Step 5: Implement WebSocket creation and data reception

```csharp
// Create the WebSocket connection
public void CreateWebSocket(string socketAddress, string socketChannel, string threadName)
{
    var webSocketManager = new WebSocketManager(_logger, 
        new WebSocketDetail { connectUri = socketAddress, id = socketChannel, ReportName = threadName }, 
        threadName, ReceiveData);
        
    webSocketManager.StartWebSocketConnection();
}

// Process incoming data
public void ReceiveData(string jsonString, string threadName)
{
    // Check if this is the data type we're interested in
    if (jsonString.Contains("topicName") && jsonString.IndexOf("v2.your.topic") > 0)
    {
        ProcessNotification(jsonString);
    }
    
    // Handle heartbeats
    if (jsonString.IndexOf("WebSocket Heartbeat") > 0 || 
        (jsonString.Contains("\"topicName\": \"channel.metadata\"") && 
         jsonString.Contains("\"message\": \"pong\"")))
    {
        _logger?.LogDebug($"Received heartbeat on thread {threadName} at {DateTime.Now}");
        // Reset error counters on successful heartbeat
        TotalErrors = 0;
    }
    
    // Process any pending data writes
    if (_writeData && _mainData.Rows.Count > 0)
    {
        try
        {
            DBAdapter.WriteSQLDataBulk(_mainData);
            _writeData = false;
            _logger?.LogInformation($"Successfully wrote {_mainData.Rows.Count} records to database");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error writing data to database");
        }
    }
}

// Process notification data
private bool ProcessNotification(string jsonString)
{
    try
    {
        // Parse the JSON data and update your data table
        var notificationData = JsonConvert.DeserializeObject<YourNotificationClass>(
            jsonString,
            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }
        );
        
        if (notificationData?.eventBody == null)
        {
            _logger?.LogWarning("Received notification with null eventBody");
            return false;
        }
        
        lock (_mainData)
        {
            // Process the notification data
            // Example:
            string itemId = notificationData.eventBody.id;
            DataRow dataRow = _mainData.Select($"id = '{itemId}'").FirstOrDefault();
            
            if (dataRow == null)
            {
                dataRow = _mainData.NewRow();
                dataRow["id"] = itemId;
                _mainData.Rows.Add(dataRow);
            }
            
            // Update fields
            dataRow["name"] = notificationData.eventBody.name;
            dataRow["value"] = notificationData.eventBody.value;
            dataRow["timestamp"] = DateTime.UtcNow;
            
            _writeData = true;
            return true;
        }
    }
    catch (Exception ex)
    {
        _logger?.LogError(ex, "Error processing notification");
        return false;
    }
}

// Define your notification data classes
public class YourNotificationClass
{
    public string topicName { get; set; }
    public EventBody eventBody { get; set; }
    
    public class EventBody
    {
        public string id { get; set; }
        public string name { get; set; }
        public string value { get; set; }
        // Add more properties as needed
    }
}
```

## Integration with GCRealtimeManager

### Step 6: Register your service with the GCRealtimeManager

Update GCRealtimeManager.cs to include your new service:

```csharp
// In GCRealtimeManager.cs

// Add a field for your service
private MyNewService _myNewService;

// Add a method to start your service
private void StartMyNewService()
{
    _logger.LogInformation("Starting My New service");
    _myNewService = _serviceFactory.CreateMyNewService();
    _services.Add(_myNewService);
    
    Thread thread = new Thread(_myNewService.Start);
    thread.Name = "MyNewServiceThread";
    thread.Start();
    _serviceThreads.Add(thread);
}

// Update the Start method to include your service
public void Start()
{
    if (_isRunning)
        return;
        
    _logger.LogInformation($"Starting Genesys Cloud Real-Time services at {DateTime.Now}");
    
    try
    {
        // Create and start all services
        StartUserActivityService();
        StartUserAdherenceService();
        StartUserCallStatsService();
        StartUserCallDetailsService();
        StartQueueCallDetailsService();
        StartMyNewService(); // Add your new service here
        
        // Start monitoring thread
        Thread monitorThread = new Thread(MonitorServices);
        monitorThread.Name = "RealtimeMonitor";
        monitorThread.Start();
        _serviceThreads.Add(monitorThread);
        
        _isRunning = true;
        _logger.LogInformation("All real-time services started successfully");
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to start real-time services");
        throw;
    }
}
```

### Step 7: Add your service to the RealtimeServiceFactory

```csharp
// In RealtimeServiceFactory.cs

// Add method to create your service
public MyNewService CreateMyNewService()
{
    var service = new MyNewService(_logger);
    service.Initialize();
    return service;
}
```

## Testing Your Service

1. Test WebSocket connection:
   - Verify your service can establish WebSocket connections
   - Ensure subscriptions are created correctly

2. Test notification processing:
   - Use manual test data or capture real notifications
   - Verify data is parsed correctly

3. Test database integration:
   - Ensure data is written to database properly
   - Verify existing data is loaded correctly on startup

4. Test error handling:
   - Verify your service handles WebSocket disconnections
   - Ensure proper retry logic is in place

## Best Practices

1. **Error Handling**:
   - Always use try/catch blocks in critical sections
   - Log errors with appropriate context
   - Implement retry logic for transient failures

2. **Resource Management**:
   - Dispose of resources properly in the Dispose method
   - Use locks appropriately when accessing shared resources

3. **Performance**:
   - Batch database operations
   - Use chunking for large data sets
   - Implement efficient data structures

4. **Logging**:
   - Log important events at Information level
   - Use Debug level for detailed information
   - Include context (like thread name, operation name)

5. **Configuration**:
   - Use RealtimeConfigurationManager for service-specific settings
   - Document configuration options

6. **Code Structure**:
   - Follow existing patterns in other services
   - Create separate methods for distinct functionality
   - Keep methods focused and relatively small

7. **Testing**:
   - Write unit tests for critical components
   - Create integration tests for end-to-end functionality
   - Test with realistic data volumes
```

## Example: Complete Service Implementation

For a complete example, refer to the existing services such as `UserActivityService.cs` and `QueueCallDetailsService.cs`. These demonstrate best practices for realtime service implementation.

## Debugging Tips

1. Enable detailed logging:
   ```csharp
   // In appsettings.json
   {
     "RealTime": {
       "EnableDetailedLogging": true
     }
   }
   ```

2. Monitor WebSocket traffic:
   - Set breakpoints in the ReceiveData method
   - Log raw message content for debugging

3. Common issues:
   - WebSocket connection failures: Check network connectivity and API credentials
   - Topic subscription errors: Verify topic formats and channel limits
   - Data parsing errors: Validate JSON structure matches your model classes

## Reference

- Genesys Cloud Notification Service API: https://developer.genesys.cloud/notificationsapi/
- WebSocket Protocol: https://tools.ietf.org/html/rfc6455
