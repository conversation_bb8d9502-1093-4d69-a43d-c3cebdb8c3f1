using System;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils.WebSocket
{
    /// <summary>
    /// Factory for creating WebSocketManager instances
    /// </summary>
    public static class WebSocketManagerFactory
    {
        /// <summary>
        /// Maximum number of topics that should be included in a single subscription request to avoid API limits
        /// </summary>
        public const int MAX_TOPICS_PER_REQUEST = 100; // Reduced from 1000 to 100 to avoid API limits

        /// <summary>
        /// Creates a new WebSocketManager instance
        /// </summary>
        public static IWebSocketManager CreateWebSocketManager(
            Microsoft.Extensions.Logging.ILogger logger, 
            WebSocketDetail detail, 
            string name, 
            Action<string, string> dataCallback, 
            JsonUtils jsonUtils)
        {
            return new WebSocketManager(logger, detail, name, dataCallback, jsonUtils);
        }
    }
}
