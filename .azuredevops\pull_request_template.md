## Change description

> Description here

## Type of change
- [ ] Bug fix (fixes an issue)
- [ ] New feature (adds functionality)

## Related issues

> Fix [#1]() 

## Checklists

### Development

- [ ] Application changes have been tested thoroughly, against defined QA process
- [ ] Automated tests covering modified code pass in Azure Pipeline

### Security

- [ ] Security impact of change has been considered
- [ ] Code follows company security practices and guidelines

### Code review 

- [ ] Pull request has a descriptive title and context useful to a reviewer.
- [ ] Pull request linked to task tracker where applicable

### Release

- [ ] Release notes updated in knowledge base to reflect changes included in PR
- [ ] Create tag for release aligned to destination version number (https://learn.microsoft.com/en-us/azure/devops/repos/git/git-tags)

+semver: minor