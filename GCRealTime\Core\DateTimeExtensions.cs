using System;

namespace GCRealTime.Core
{
    public static class DateTimeExtensions
    {
        /// <summary>
        /// Converts a UTC DateTime to the local time for the specified TimeZone.
        /// </summary>
        /// <param name="utcTime">The UTC time to convert. Will be treated as UTC regardless of Kind.</param>
        /// <param name="timeZone">The timezone to convert to</param>
        /// <returns>A DateTime in the specified timezone</returns>
        public static DateTime ConvertToLocalTime(this DateTime utcTime, TimeZoneInfo timeZone)
        {
            try
            {
                if (utcTime.Kind != DateTimeKind.Utc)
                {
                    // Ensure we're working with UTC time
                    utcTime = DateTime.SpecifyKind(utcTime, DateTimeKind.Utc);
                }
                
                return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone);
            }
            catch
            {
                // Fallback to direct conversion
                return utcTime.ToLocalTime();
            }
        }
        
        /// <summary>
        /// Converts a local DateTime to UTC based on the specified TimeZone.
        /// </summary>
        /// <param name="localTime">The local time to convert</param>
        /// <param name="timeZone">The timezone of the local time</param>
        /// <returns>A DateTime in UTC</returns>
        public static DateTime ConvertToUtcTime(this DateTime localTime, TimeZoneInfo timeZone)
        {
            // If the time is already UTC, return it as is
            if (localTime.Kind == DateTimeKind.Utc)
                return localTime;
                
            try
            {
                // If the time is "Unspecified", assume it's in the specified timezone
                if (localTime.Kind == DateTimeKind.Unspecified)
                    localTime = DateTime.SpecifyKind(localTime, DateTimeKind.Local);
                    
                // Convert to UTC using the timezone
                return TimeZoneInfo.ConvertTimeToUtc(localTime, timeZone);
            }
            catch
            {
                // Fallback to direct conversion
                return localTime.ToUniversalTime();
            }
        }
    }
}
