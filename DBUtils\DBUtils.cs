using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using CSG.Adapter.Compatability;
using CSG.Adapter.Configuration;
using Microsoft.Extensions.Logging;
using MySql.Data.MySqlClient;
using Npgsql;
using Snowflake.Data.Client;
using StandardUtils;
using Z.BulkOperations;
using CSG.Adapter.Configuration;


namespace DBUtils
{
    public class DBUtils
    {
        public string DBConnectionString { get; set; }
        public string DBConnectionStringEncrypted { get; set; }
        public DbConnection DBConnect { get; set; }
        public DbDataAdapter DBDataAdapter { get; set; }
        public DbCommand DBSelectCommand { get; set; }
        public DbCommand DBUpdateCommand { get; set; }
        public DbCommand DBDeleteCommand { get; set; }
        public DbDataReader DBDataReader { get; set; }
        public DataTable ControlData { get; set; }
        private Utils UCAUtils = new Utils();
        public string CustomerKeyID { get; set; }
        public CSG.Adapter.Configuration.DatabaseType DBType { get; set; }
        public string DBName { get; set; }
        public string PostgresSchema { get; set; }
        public bool SurpressErrors { get; set; }
        private readonly ILogger? _logger;

        public DBUtils()
        {
        }

        public DBUtils(ILogger logger)
        {
            _logger = logger;
        }

        #nullable enable
        public void Initialize()
        {
            bool Successful = false;

            CustomerKeyID = UCAUtils.ReadSetting("CSG_CUSTOMERKEYID");
            DBType = Enum.Parse<CSG.Adapter.Configuration.DatabaseType>(
                UCAUtils.ReadSetting("CSG_SQLDATABASETYPE"), true);

            PostgresSchema = "public";
            if (DBType == DatabaseType.PostgreSQL)
            {
                PostgresSchema = UCAUtils.ReadSetting("CSG_SQLDATABASESCHEMA");

                if (PostgresSchema == "Not Found" || string.IsNullOrEmpty(PostgresSchema))
                {
                    Console.WriteLine("Postgres Schema Not Found in Config. Reverting to public");
                    PostgresSchema = "public";
                }
            }

            Successful = ReadDBConnectionString();

            string SelectString = "SELECT * from tabledefinitions";
            if (Successful == true)
            {
                try
                {
                    ControlData = GetSQLTableData(SelectString, "ControlData");
                }
                catch (Npgsql.PostgresException ex) when (ex.Message.StartsWith("42P01: relation ")) // does not exist
                {
                    // Try to show some useful information about the schema and search path which is a common cause of
                    // this exception.
                    SelectString = "SELECT setting, current_schema(), current_database() FROM pg_settings WHERE name = 'search_path'";
                    DataTable res = GetSQLTableData(SelectString, "SearchPath");
                    Console.WriteLine(
                        "Cannot access table definitions, check schema/search path or make sure an install has been performed.");
                    if (res != null && res.Rows.Count > 0)
                        Console.WriteLine(
                            "Postgres search path is {0}, schema is {1}, database is {2}",
                            res.Rows[0]["setting"],
                            res.Rows[0]["current_schema"],
                            res.Rows[0]["current_database"]);
                    throw;
                }
                catch
                {
                    Console.WriteLine("Cannot Access Table Definitions - Exiting");
                    throw;
                }
            }
            else
            {
                Console.WriteLine("DB UTILS: Warning:Definition Table not yet Initialized - Might Be Install - If seen in production please contact UCA");
            }

            Z.BulkOperations.LicenseManager.AddLicense("4121;300-ucarchitects.com.au", "b23237d2-d300-881d-098f-020f9e7bdd1c");

            if (!LicenseManager.ValidateLicense(out string licenseErrorMessage))
            {
                throw new ApplicationException(licenseErrorMessage);
            }
        }

        public DateTime GetSyncLastUpdate(string SyncType)
        {
            var TableName = "tabledefinitions";
            var LastSyncDate = DateTime.MinValue;
            var utcNow = DateTime.UtcNow;
            SyncType = SyncType.ToLower();

            try
            {
                // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
                switch (DBType)
                {
                    case DatabaseType.PostgreSQL:
                        TableName = PostgresSchema + "." + TableName.ToLower();
                        break;
                    default:
                        break;
                }
                VersionNumberCompare(TableName, SyncType);
                var sql = string.Format(
                    "SELECT datekeyfield FROM {0} WHERE tablename = '{1}'",
                    TableName,
                    SyncType
                );              
                DataTable DTTemp = GetSQLTableData(sql, SyncType);
                if (DTTemp != null && DTTemp.Rows.Count > 0 && DTTemp.Rows[0]["datekeyfield"] != DBNull.Value)
                {
                    LastSyncDate = Convert.ToDateTime(DTTemp.Rows[0]["datekeyfield"]);
                    _logger?.LogInformation(
                        "Sync job {SyncType} last update is {LastUpdate}Z",
                        SyncType,
                        LastSyncDate.ToString("s"));
                }
            }
            catch (Exception ex)
            {
                if (_logger != null)
                    _logger?.LogWarning(ex, "Suppressed error");
                else
                    Console.WriteLine(ex.ToString());
            }

            if (LastSyncDate == DateTime.MinValue)
            {
                LastSyncDate = DateTime.UtcNow.AddDays(-365);
                Console.WriteLine(
                    "{0} {1}: Sync job {2} was not set. Syncing from {3}Z",
                    DateTime.Now.ToString("s"),
                    nameof(GetSyncLastUpdate),
                    SyncType,
                    LastSyncDate.ToString("s"));
            }
            if (LastSyncDate > utcNow)
            {
                Console.WriteLine(
                    "{0} {1}: Sync job {2} last update {3}Z is in the future. Setting to {4}Z",
                    DateTime.Now.ToString("s"),
                    nameof(GetSyncLastUpdate),
                    SyncType,
                    LastSyncDate.ToString("s"),
                    utcNow.ToString("s"));
                LastSyncDate = utcNow;
            }

            return LastSyncDate;
        }
        public void VersionNumberCompare(string TableName, string SyncType)
        {
            bool successful = true;
            try
            {
                var LastVersion = "";
                var version = string.Format(
                        "SELECT version FROM {0} WHERE tablename = '{1}'",
                        TableName,
                        SyncType
                    );

                DataTable DTTempVersion = GetSQLTableData(version, SyncType);

                if (DTTempVersion != null && DTTempVersion.Rows.Count > 0 && DTTempVersion.Rows[0]["version"] != DBNull.Value)
                {
                    LastVersion = Convert.ToString(DTTempVersion.Rows[0]["version"]);
                    var CurrentVersion = ApplicationVersion.MajorMinorPatch.ToString();
                    Version? lastVersion = Version.TryParse(LastVersion, out Version parsedLastVersion) ? parsedLastVersion : null;

                    Version? currentVersion = Version.TryParse(CurrentVersion, out Version parsedCurrentVersion) ? parsedCurrentVersion : null;

                    if (lastVersion != null && currentVersion != null)
                    {
                        if (lastVersion < currentVersion)
                        {
                            // throw new InvalidOperationException("Table version for "+ TableName +  " can not be less than current version. Need to run Install Job");
                        }
                    }
                    else
                    {
                        // throw new Exception("Invalid version format.");
                    }
                    
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                if (_logger != null){
                    _logger.LogWarning(ex,"test exception");
                }
                Environment.Exit(1);
            }
        }
        public bool SetSyncLastUpdate(DateTime UpdateDate, string SyncType)
        {
            string TableName = "tabledefinitions";
            SyncType = SyncType.ToLower();
            // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
            switch (DBType)
            {
                case DatabaseType.PostgreSQL:
                    TableName = PostgresSchema + "." + TableName.ToLower();
                    break;
                default:
                    break;
            }

            if (UpdateDate > DateTime.UtcNow && UpdateDate != DateTime.MaxValue)
                UpdateDate = DateTime.UtcNow;

            var sql = string.Format(
                "UPDATE {0} SET datekeyfield = '{1}' WHERE tablename = '{2}'",
                TableName,
                UpdateDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                SyncType
            );

            int rowsAffected = ExecuteSqlNonQuery(sql);
            if (rowsAffected == 0)
            {
                Console.WriteLine(
                    "{0} {1}: [WARN ] Sync job {2} last update was not set",
                    DateTime.Now.ToString("s"),
                    nameof(SetSyncLastUpdate),
                    SyncType);
                sql = string.Format(
                    "INSERT INTO {0} (datekeyfield, tablename) VALUES ('{1}', '{2}')",
                    TableName,
                    UpdateDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    SyncType
                );
                rowsAffected = ExecuteSqlNonQuery(sql);
            }
            if (rowsAffected > 0)
            {
                Console.WriteLine(
                    "{0} {1}: Sync job {2} last update set to {3}Z",
                    DateTime.Now.ToString("s"),
                    nameof(SetSyncLastUpdate),
                    SyncType,
                    UpdateDate.ToString("s"));
            }
            else
            {
                Console.WriteLine(
                    "{0} {1}: [ERROR] Failed to set sync job {2} last update to {3}Z",
                    DateTime.Now.ToString("s"),
                    nameof(SetSyncLastUpdate),
                    SyncType,
                    UpdateDate.ToString("s"));
            }

            return rowsAffected > 0;
        }

        public DataTable GetSQLTableSchema(string TableName)
        {
            string sql = "";

            switch (DBType)
            {
                case DatabaseType.MSSQL:
                    sql = "SELECT TOP (0) * FROM " + TableName;
                    break;
                case DatabaseType.MySQL:
                case DatabaseType.PostgreSQL:
                case DatabaseType.Snowflake:
                    sql = "SELECT * FROM " + TableName + " LIMIT 0";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            DataTable? table = GetSQLTableData(sql, TableName);
            if (table is null)
                throw new DataException("Failed to retrieve table schema for " + TableName);

            return table;
        }

        public DataTable GetSQLTableData(string SQLCommand, string TableName)
        {
            DataTable DTTempData = new DataTable();

            DateTime BeforeGet = DateTime.UtcNow;

            // Create a new connection for each request - don't reuse DBConnect directly
            using (var connection = CreateNewConnection())
            {
                try
                {
                    connection.Open();
                    
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = SQLCommand;
                        
                        // Create a new adapter for each operation
                        using (var adapter = CreateDataAdapter())
                        {
                            adapter.SelectCommand = command;
                            adapter.Fill(DTTempData);
                            adapter.FillSchema(DTTempData, SchemaType.Source);
                            DTTempData.TableName = TableName;
                        }
                    }

                    int rowCount = DTTempData.Rows.Count;
                    double elapsedTime = (DateTime.UtcNow - BeforeGet).TotalSeconds;

                    var msg = $"Retrieved {rowCount} rows from table '{TableName}' using query: '{SQLCommand}'. Duration: {elapsedTime:N3} secs";
                    if (_logger != null)
                        _logger.LogInformation(msg);
                    else
                        Console.WriteLine(msg);
                }
                catch (Exception ex)
                {
                    var errorMsg = $"Exception while running query '{SQLCommand}' on table '{TableName}'";
                    if (_logger != null)
                        _logger.LogWarning(ex, errorMsg);
                    else
                        Console.WriteLine(errorMsg);
                }
            }

            return DTTempData;
        }

        // Helper method to create a new connection without modifying the shared one
        private DbConnection CreateNewConnection()
        {
            DbConnection connection = null;
            switch (DBType)
            {
                case DatabaseType.MSSQL:
                    connection = new SqlConnection(DBConnectionString);
                    break;
                case DatabaseType.MySQL:
                    connection = new MySqlConnection(DBConnectionString);
                    break;
                case DatabaseType.PostgreSQL:
                    connection = new NpgsqlConnection(DBConnectionString);
                    break;
                case DatabaseType.Snowflake:
                    connection = new SnowflakeDbConnection(DBConnectionString);
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
            return connection;
        }

        // Helper method to create a data adapter based on the database type
        private DbDataAdapter CreateDataAdapter()
        {
            DbDataAdapter adapter = null;
            switch (DBType)
            {
                case DatabaseType.MSSQL:
                    adapter = new SqlDataAdapter();
                    break;
                case DatabaseType.MySQL:
                    adapter = new MySql.Data.MySqlClient.MySqlDataAdapter();
                    break;
                case DatabaseType.PostgreSQL:
                    adapter = new NpgsqlDataAdapter();
                    break;
                case DatabaseType.Snowflake:
                    adapter = new SnowflakeDbDataAdapter();
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
            return adapter;
        }
#nullable restore

        public bool WriteRealSQLData(ref DataTable DTTempData, String TableName)
        {
            bool Successful = false;

            // Use a dedicated connection
            using (var connection = CreateNewConnection())
            {
                try
                {
                    connection.Open();
                    
                    DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();
                    if (DRControl == null)
                    {
                        Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                        return false;
                    }

                    string KeyId = DRControl["keyfield"].ToString();

                    int AddedRows = 0;
                    int ChangedRows = 0;
                    int TotalRows = 0;

                    DataTable DTTempInsTable = DTTempData.Clone();
                    DataTable DTTempUpdTable = DTTempData.Clone();

                    foreach (DataRow DRChanges in DTTempData.Rows)
                    {
                        TotalRows++;
                        if (DRChanges.RowState == DataRowState.Added)
                        {
                            DRChanges["updated"] = DateTime.UtcNow;
                            DRChanges.AcceptChanges();
                            DRChanges.SetAdded();
                            DTTempInsTable.ImportRow(DRChanges);
                            AddedRows++;
                            if (AddedRows % 100 == 0)
                                Console.Write("+");
                        }
                        else if (DRChanges.RowState == DataRowState.Modified)
                        {
                            DRChanges["updated"] = DateTime.UtcNow;
                            DRChanges.AcceptChanges();
                            DRChanges.SetModified();
                            DTTempUpdTable.ImportRow(DRChanges);
                            ChangedRows++;
                            if (ChangedRows % 100 == 0)
                                Console.Write("*");
                        }
                        else
                        {
                            if (TotalRows % 10 == 0)
                                Console.Write("N");
                        }
                    }

                    Console.WriteLine("\nData Insert\n");

                    using (var bulk = new BulkOperation(connection))
                    {
                        bulk.DestinationTableName = TableName;

                        foreach (DataColumn DCChangedRows in DTTempData.Columns)
                        {
                            if (DCChangedRows.ColumnName == KeyId)
                                bulk.ColumnMappings.Add(DCChangedRows.ColumnName, true);
                            else
                                bulk.ColumnMappings.Add(DCChangedRows.ColumnName);
                        }

                        if (ChangedRows > 0)
                        {
                            Console.WriteLine("UR{0}", DTTempUpdTable.Rows.Count);
                            bulk.BulkUpdate(DTTempUpdTable);
                        }
                        if (AddedRows > 0)
                        {
                            Console.WriteLine("IR{0}", DTTempInsTable.Rows.Count);
                            DTTempInsTable.TableName = TableName;
                            bulk.BulkInsert(DTTempInsTable);
                        }
                        
                        Successful = true;
                    }
                    
                    return Successful;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[DBUtils][WriteRealData] Error writing real-time data for table {TableName}", TableName);
                    Console.WriteLine("DB Write Real Time Error");
                    throw;
                }
            }
        }
        public bool WriteSQLDataBulk(DataTable DTTempData, String TableName)
        {
            // In case DB is Snowflake, redirect to custom built Bulk Insert/Update 
            if (DBType == DatabaseType.Snowflake)
            {
                return WriteSQLData(DTTempData, TableName);
            }

            if (DTTempData is null || DTTempData.Rows.Count == 0)
            {
                Console.WriteLine("Bulk Upsert Completed - Data Was Null");
                return true;
            }

            DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();

            // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
            switch (DBType)
            {
                case DatabaseType.PostgreSQL:
                    TableName = PostgresSchema + "." + TableName.ToLower();
                    break;
                default:
                    break;
            }

            DateTime BeforeGet = DateTime.UtcNow;

            foreach (DataRow DRRow in DTTempData.Rows)
            {
                DRRow["updated"] = DateTime.UtcNow;
            }

            DateTime AfterGet = DateTime.UtcNow;
            Console.WriteLine("Updating updated field {0}", (AfterGet - BeforeGet).TotalSeconds);

            if (DRControl == null)
            {
                Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                return false;
            }

            string KeyId = DRControl["keyfield"].ToString();

            if (DTTempData.Rows.Count == 0)
                return true;

            // Use a dedicated connection for the bulk operation
            using (var connection = CreateNewConnection())
            {
                try
                {
                    connection.Open();
                    
                    int MaxRowsToSend = 1000;
                    int currentPage = 1;

                    int totalPages = DTTempData.Rows.Count % MaxRowsToSend == 0 
                        ? DTTempData.Rows.Count / MaxRowsToSend 
                        : (DTTempData.Rows.Count / MaxRowsToSend) + 1;

                    while (currentPage <= totalPages)
                    {
                        DataTable dtTemp = DTTempData.Rows.Cast<System.Data.DataRow>()
                            .Skip((currentPage - 1) * MaxRowsToSend)
                            .Take(MaxRowsToSend)
                            .CopyToDataTable();
                        dtTemp.TableName = DTTempData.TableName;

                        Console.WriteLine("Processing Rows Block - {0} ", currentPage);
                        using (var bulk = new BulkOperation(connection))
                        {
                            bulk.DestinationTableName = TableName;
                            bulk.BatchTimeout = 600;
                            
                            foreach (DataColumn DTTempCol in dtTemp.Columns)
                            {
                                bulk.ColumnMappings.Add(DTTempCol.ColumnName);
                            }

                            List<string> PrimaryKeys = KeyId.Split(',').ToList();
                            bulk.ColumnPrimaryKeyNames = PrimaryKeys;

                            Console.WriteLine("Merging Rows Block - {0} ", currentPage);
                            bulk.BulkMerge(dtTemp);
                        }

                        AfterGet = DateTime.UtcNow;
                        if (currentPage == totalPages)
                        {
                            Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", 
                                (AfterGet - BeforeGet).TotalSeconds, currentPage, DTTempData.Rows.Count, DTTempData.Rows.Count);
                        }
                        else
                        {
                            Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", 
                                (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempData.Rows.Count);
                        }
                        
                        currentPage++;
                    }

                    AfterGet = DateTime.UtcNow;
                    Console.WriteLine("Bulk Upsert Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);
                    
                    return true;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[DBUtils][BulkWrite] Error performing bulk write for table {TableName}", TableName);
                    throw;
                }
            }
        }

        public bool WriteSQLDataBulkWithDeletion(DataTable DTTempData, String TableName)
        {
            if (DBType == DatabaseType.Snowflake)
                return WriteSQLData(DTTempData, TableName);

            if (DTTempData is null)
            {
                Console.WriteLine("Bulk Upsert Completed - Data Was Null");
                return true;
            }

            DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();

            // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
            switch (DBType)
            {
                case DatabaseType.PostgreSQL:
                    TableName = PostgresSchema + "." + TableName.ToLower();
                    break;
                default:
                    break;
            }

            DateTime BeforeGet = DateTime.UtcNow;

            foreach (DataRow DRRow in DTTempData.Rows)
            {
                DRRow["updated"] = DateTime.UtcNow;
            }

            DateTime AfterGet = DateTime.UtcNow;
            Console.WriteLine("Updating updated field {0}", (AfterGet - BeforeGet));

            if (DRControl == null)
            {
                Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                return false;
            }

            string KeyId = DRControl["keyfield"].ToString();

            if (DTTempData.Rows.Count == 0)
                return true;

            int MaxRowsToSend = 10000;
            int currentPage = 1;
            int totalPages = 0;
            StringBuilder idsNotToDeleteBuilder = new StringBuilder();

            if (DTTempData.Rows.Count % MaxRowsToSend == 0)
            {
                Console.WriteLine("Reading Block of Data :Equal Division Pages is not adding one");
                totalPages = (DTTempData.Rows.Count / MaxRowsToSend);
            }
            else
            {
                Console.WriteLine("Reading Block of Data :Not Equal Division Pages adding one");
                totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;
            }

            // Create a single connection for the entire operation
            using (var connection = CreateNewConnection())
            {
                try
                {
                    connection.Open();

                    while (currentPage <= totalPages)
                    {
                        DataTable dtTemp = DTTempData.Rows.Cast<System.Data.DataRow>()
                            .Skip((currentPage - 1) * MaxRowsToSend)
                            .Take(MaxRowsToSend)
                            .CopyToDataTable();
                        dtTemp.TableName = DTTempData.TableName;

                        Console.WriteLine("Processing Rows Block - {0} ", currentPage);
                        
                        using (var bulk = new BulkOperation(connection))
                        {
                            bulk.DestinationTableName = TableName;
                            bulk.BatchTimeout = 600;
                            
                            foreach (DataColumn DTTempCol in dtTemp.Columns)
                            {
                                bulk.ColumnMappings.Add(DTTempCol.ColumnName);
                            }

                            List<string> PrimaryKeys = KeyId.Split(',').ToList();
                            bulk.ColumnPrimaryKeyNames = PrimaryKeys;

                            Console.WriteLine("Merging Rows Block - {0} ", currentPage);
                            bulk.BulkMerge(dtTemp);
                        }
                        
                        // Construct the DELETE query to remove rows not present in DTTempData
                        string currentIds = string.Join(",", dtTemp.AsEnumerable().Select(row => $"'{row[KeyId]}'").Distinct());
                        
                        if (idsNotToDeleteBuilder.Length > 0)
                        {
                            idsNotToDeleteBuilder.Append(",");
                        }
                        idsNotToDeleteBuilder.Append(currentIds);

                        AfterGet = DateTime.UtcNow;
                        if(currentPage == totalPages)
                        {
                            Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", 
                                (AfterGet - BeforeGet).TotalSeconds, currentPage, DTTempData.Rows.Count, DTTempData.Rows.Count);
                        }
                        else
                        {
                            Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", 
                                (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempData.Rows.Count);
                        }
                        currentPage++;
                    }

                    AfterGet = DateTime.UtcNow;
                    Console.WriteLine("Bulk Upsert Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);

                    BeforeGet = DateTime.UtcNow;
                    string deleteQuery = $"DELETE FROM {TableName} WHERE {KeyId} NOT IN ({idsNotToDeleteBuilder.ToString()})";

                    // Execute the delete query using the same connection
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = deleteQuery;
                        command.ExecuteNonQuery();
                    }

                    AfterGet = DateTime.UtcNow;
                    Console.WriteLine("Delete Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);
                    
                    return true;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[DBUtils][BulkWriteWithDeletion] Error performing bulk write with deletion for table {TableName}", TableName);
                    throw;
                }
            }
        }

#nullable enable
        public enum BulkOperationActionType
        {
            Save,
            Merge
        }

        private enum DynamicSQLActionType
        {
            Insert,
            Update
        }

        public void WriteSQLDataBulk(DataTable table)
        {
            WriteSQLDataBulk(BulkOperationActionType.Merge, table);
        }

        public void WriteSQLDataBulk(DataTable table, List<string> keys)
        {
            WriteSQLDataBulk(BulkOperationActionType.Merge, table, keys);
        }

        public void WriteSQLDataBulk(BulkOperationActionType actionType, DataTable table)
        {
            WriteSQLDataBulk(actionType, table, table.PrimaryKey.Select(x => x.ColumnName).ToList());
        }

        public void WriteSQLDataBulk(BulkOperationActionType actionType, DataTable table, List<string> keys)
        {
            // In case DB is Snowflake, redirect to custom built Bulk Insert/Update 
            if (DBType == DatabaseType.Snowflake)
            {
                WriteSQLData(table, table.TableName.ToLower());
                return;
            }


            if (table.Rows.Count == 0)
                return;

            var timer = System.Diagnostics.Stopwatch.StartNew();

            // Create a new connection specifically for this bulk operation
            using (var connection = CreateNewConnection())
            {
                try
                {
                    connection.Open();

                    using (var bulk = new BulkOperation(connection))
                    {
                        bulk.CaseSensitive = CaseSensitiveType.Insensitive;
                        bulk.DestinationTableName = table.TableName.ToLower();
                        bulk.BatchTimeout = 600;
                        bulk.BatchSize = 1000;
                        
                        foreach (DataColumn column in table.Columns)
                        {
                            bulk.ColumnMappings.Add(column.ColumnName);
                        }
                        bulk.ColumnPrimaryKeyNames = keys;
                        
                        if (_logger != null)
                            bulk.Log = s => _logger?.LogTrace(s);

                        switch (actionType)
                        {
                            case BulkOperationActionType.Save:
                                bulk.BulkSaveChanges(table);
                                break;
                            case BulkOperationActionType.Merge:
                                bulk.BulkMerge(table);
                                break;
                            default:
                                throw new NotImplementedException();
                        }
                    }

                    Console.WriteLine(
                        "Bulk upsert of {0} rows for {1} completed in {2:N3} secs",
                        table.Rows.Count,
                        table.TableName,
                        timer.Elapsed.TotalSeconds);
                }
                catch (Exception ex)
                {
                    if (_logger != null)
                        _logger.LogError(
                            ex,
                            "Bulk upsert of {Rows} rows for {Table} FAILED in {TimeTaken:N3} secs",
                            table.Rows.Count,
                            table.TableName,
                            timer.Elapsed.TotalSeconds);
                    else
                        Console.WriteLine(
                            "Bulk upsert of {0} rows for {1} FAILED in {2:N3} secs",
                            table.Rows.Count,
                            table.TableName,
                            timer.Elapsed.TotalSeconds);
                    throw;
                }
            }
        }

#nullable restore

        public Boolean WriteSQLDataSync(DataTable DTTempData, String TableName)
        {
            if (DBType == DatabaseType.Snowflake)
                return WriteSQLData(DTTempData, TableName);

            DateTime BeforeGet = DateTime.UtcNow;
            DateTime AfterGet = DateTime.UtcNow;

            DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();

            // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
            switch (DBType)
            {
                case DatabaseType.PostgreSQL:
                    TableName = PostgresSchema + "." + TableName.ToLower();
                    break;
                default:
                    break;
            }

            if (DRControl == null)
            {
                Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                return false;
            }

            string KeyId = DRControl["keyfield"].ToString();

            if (DTTempData.Rows.Count == 0)
                return true;

            // Create a dedicated connection for the operation
            using (var connection = CreateNewConnection())
            {
                try
                {
                    connection.Open();
                    
                    int MaxRowsToSend = 10000;
                    int currentPage = 1;
                    int totalPages = 0;

                    if (DTTempData.Rows.Count % MaxRowsToSend == 0)
                    {
                        Console.WriteLine("Reading Block of Data :Equal Division Pages is not adding one");
                        totalPages = (DTTempData.Rows.Count / MaxRowsToSend);
                    }
                    else
                    {
                        Console.WriteLine("Reading Block of Data :Not Equal Division Pages adding one");
                        totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;
                    }

                    while (currentPage <= totalPages)
                    {
                        DataTable dtTemp = DTTempData.Rows.Cast<System.Data.DataRow>()
                            .Skip((currentPage - 1) * MaxRowsToSend)
                            .Take(MaxRowsToSend)
                            .CopyToDataTable();
                        dtTemp.TableName = DTTempData.TableName;

                        Console.WriteLine("Processing Rows Block - {0} ", currentPage);
                        
                        using (var bulk = new BulkOperation(connection))
                        {
                            bulk.DestinationTableName = TableName;
                            bulk.BatchTimeout = 180;
                            
                            foreach (DataColumn DTTempCol in dtTemp.Columns)
                            {
                                bulk.ColumnMappings.Add(DTTempCol.ColumnName);
                            }

                            List<string> PrimaryKeys = KeyId.Split(',').ToList();
                            bulk.ColumnPrimaryKeyNames = PrimaryKeys;

                            Console.WriteLine("Merging Rows Block - {0} ", currentPage);
                            bulk.BulkSynchronize(dtTemp);
                        }

                        AfterGet = DateTime.UtcNow;
                        Console.WriteLine("SQL Syncing Current Page {1:N3} : Completed {0} secs. Records : {2} of {3} ", 
                            (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempData.Rows.Count);

                        currentPage++;
                    }

                    AfterGet = DateTime.UtcNow;
                    Console.WriteLine("Bulk Syncing Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);
                    
                    return true;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[DBUtils][SyncData] Error performing sync for table {TableName}", TableName);
                    throw;
                }
            }
        }

        public bool WriteSQLData(DataTable DTTempData, String TableName)
        {    
            string SearchString = String.Empty;

            DataSet DSFromDataBase = new DataSet();

            DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();

            if (DRControl == null)
            {
                Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                return false;
            }

            string KeyId = DRControl["keyfield"].ToString();

            if (DTTempData.Rows.Count == 0)
                return true;

            if (DRControl != null)
            {
                DateTime BeforeGet = DateTime.UtcNow;
                Console.WriteLine("Preparing to Write Data for the {0} Table", TableName);

                ConnectToDatabase();

                bool AddedSearchOptions = false;
                int MaxRowsToSend = 5000;
                int currentPage = 1;

                int totalPages = 0;
                //int totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;

                if (DTTempData.Rows.Count % MaxRowsToSend == 0)
                {
                    Console.WriteLine("Reading Bock of Data :Equal Division Pages is not adding one");
                    totalPages = (DTTempData.Rows.Count / MaxRowsToSend);
                }
                else
                {
                    Console.WriteLine("Reading Bock of Data :Not Equal Division Pages adding one");
                    totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;
                }

                #region DBUpdateMethod
                // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
                switch (DBType)
                {
                    case DatabaseType.PostgreSQL:
                        TableName = PostgresSchema + "." + TableName.ToLower();
                        break;
                    default:
                        break;
                }

                DateTime AfterGet = DateTime.UtcNow;

                while (currentPage <= totalPages)
                {
                    DBSelectCommand = DBConnect.CreateCommand();
                    DBUpdateCommand = DBConnect.CreateCommand();
                    DBDeleteCommand = DBConnect.CreateCommand();

                    Console.WriteLine("Working On Batch Page : {0}", currentPage);
                    StringBuilder SelectString = new StringBuilder();
                    DataTable dtTemp = DTTempData.Select("", "", DataViewRowState.CurrentRows).Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                    dtTemp.TableName = DTTempData.TableName;

                    int CurrentRow = 0;
                    foreach (DataRow DRRow in dtTemp.Rows)
                    {
                        SelectString.Append(DRRow[KeyId] + ",");
                        AddedSearchOptions = true;
                        CurrentRow++;
                    }

                    if (AddedSearchOptions == true)
                    {
                        SelectString.Length = SelectString.Length - 1;
                    }
                    else
                        break;

                    Console.WriteLine("Filled Search String ");
                    // TODO: The way the SQL is generated here needs reworking.
                    switch (DBType)
                    {
                        case DatabaseType.MSSQL:
                            SearchString = "DECLARE @data NVARCHAR(MAX),  " +
                                            "@delimiter NVARCHAR(5) " +
                                            "SELECT @data = '" + SelectString.ToString() + "' " +
                                            "set @delimiter = ',' " +
                                            "DECLARE @textXML XML; " +
                                            "SELECT @textXML = CAST('<d>' + REPLACE(@data, @delimiter, '</d><d>') + '</d>' AS XML); " +
                                            "  " +
                                            "declare @temp table (num nvarchar(255)) " +
                                            "insert into @temp " +
                                            "SELECT T.split.value('.', 'nvarchar(max)') AS data " +
                                            "FROM @textXML.nodes('/d') T (split) " +
                                            " " +
                                            "Select * from " + TableName + "  where " + KeyId + " in(select num from @temp);";

                            DBSelectCommand.CommandText = @SearchString;

                            break;
                        case DatabaseType.MySQL:
                            MySqlCommand MYSQLcmd = new MySqlCommand
                            {
                                CommandType = CommandType.StoredProcedure,
                                CommandText = "FETCHROWS"
                            };

                            MYSQLcmd.Parameters.AddWithValue("@inputString", SelectString.ToString());
                            MYSQLcmd.Parameters["@inputString"].Direction = ParameterDirection.Input;
                            MYSQLcmd.Parameters.AddWithValue("@dbName", DBConnect.Database);
                            MYSQLcmd.Parameters["@dbName"].Direction = ParameterDirection.Input;
                            MYSQLcmd.Parameters.AddWithValue("@tableName", TableName);
                            MYSQLcmd.Parameters["@tableName"].Direction = ParameterDirection.Input;
                            MYSQLcmd.Parameters.AddWithValue("@keyField", KeyId);
                            MYSQLcmd.Parameters["@keyField"].Direction = ParameterDirection.Input;

                            DBSelectCommand.CommandText = MYSQLcmd.CommandText;
                            DBSelectCommand.CommandType = CommandType.StoredProcedure;

                            foreach (MySqlParameter DBParams in MYSQLcmd.Parameters)
                            {
                                DBSelectCommand.Parameters.Add(DBParams);
                            }

                            MYSQLcmd = null;
                            break;

                        case DatabaseType.PostgreSQL:
                            NpgsqlCommand PstSQLCmd = new NpgsqlCommand
                            {
                                //CommandType = CommandType.StoredProcedure,
                                CommandText = @"select * from " + TableName + " where " + KeyId + " in ('" + SelectString.ToString().Replace(",", "','") + "')"
                            };

                            DBSelectCommand.CommandText = PstSQLCmd.CommandText;
                            DBSelectCommand.CommandType = CommandType.Text;

                            break;

                        case DatabaseType.Snowflake:
                            DBSelectCommand.CommandText = @"select * from " + TableName + " where " + KeyId + " in ('" + SelectString.ToString().Replace(",", "','") + "')";
                            break;

                        default:
                            throw new NotImplementedException("Database type is not implemented");
                    }

                    Console.WriteLine("Getting Existing Data From DB");
                    DBSelectCommand.Connection = DBConnect;
                    DBSelectCommand.CommandTimeout = 0;
                    DBDataAdapter.SelectCommand = DBSelectCommand;
                    DBDataAdapter.Fill(DSFromDataBase);
                    Console.WriteLine("Got Existing Data From DB");

                    currentPage++;
                }

                DSFromDataBase.Tables[0].TableName = TableName;

                DBSelectCommand = null;

                Console.WriteLine("\nTotal Rows from GC: {0}", DTTempData.Rows.Count);
                Console.WriteLine("Total Rows from DB: {0}", DSFromDataBase.Tables[0].Rows.Count);

                DTTempData.Columns.Remove("updated");
                DTTempData.AcceptChanges();
                DSFromDataBase.Tables[0].Columns.Remove("updated");
                DSFromDataBase.Tables[0].AcceptChanges();


                int AddedRows = 0;
                int ChangedRows = 0;
                // Identify new records
                var newRecords = 
                    (from drNew in DTTempData.AsEnumerable()
                    join drOld in DSFromDataBase.Tables[0].AsEnumerable() 
                    on drNew.Field<String>(KeyId) equals drOld.Field<String>(KeyId) into match
                    from drOld in match.DefaultIfEmpty()
                    where drOld == null
                    select drNew).ToList();

                // Identify updated records
                var potentialUpdatedRecords  = 
                    (from drNew in DTTempData.AsEnumerable()
                    join drOld in DSFromDataBase.Tables[0].AsEnumerable() 
                    on drNew.Field<String>(KeyId) equals drOld.Field<String>(KeyId)
                    where drOld != null && !DataRowComparer.Default.Equals(drNew, drOld)
                    select new { drNew, drOld }).ToList();

                var updatedRecords = new List<DataRow>();

                foreach (var record in potentialUpdatedRecords)
                {
                    var drNew = record.drNew;
                    var drOld = record.drOld;
                
                    bool isDifferent = false;

                    foreach (DataColumn column in DTTempData.Columns)
                    {
                        if (DSFromDataBase.Tables[0].Columns.Contains(column.ColumnName))
                        {
                            var newValue = drNew[column];
                            var oldValue = drOld[column.ColumnName];
                            bool valuesAreEqual;
                            if (newValue is DateTime newDateTime && oldValue is DateTime oldDateTime)
                            {
                                valuesAreEqual = newDateTime.ToString("yyyy-MM-dd HH:mm:ss") == oldDateTime.ToString("yyyy-MM-dd HH:mm:ss");
                            }
                            else if (newValue is string newString && oldValue is string oldString)
                            {
                                valuesAreEqual = string.Equals(newString?.Trim(), oldString?.Trim(), StringComparison.Ordinal);
                            }
                            else if (newValue is DBNull && oldValue is DBNull)
                            {
                                valuesAreEqual = true;
                            }
                            else if ((newValue is double || newValue is float || newValue is decimal) &&
                            (oldValue is double || oldValue is float || oldValue is decimal))
                            {
                                var oldDouble = Convert.ToDouble(oldValue);
                                string oldValueStr = oldDouble.ToString(); 
                                int precision = 0;
                                int decimalPlaceIndex = oldValueStr.IndexOf('.');
                                if (decimalPlaceIndex != -1)
                                {
                                    precision = oldValueStr.Length - decimalPlaceIndex - 1;
                                }
                                precision = Math.Min(precision, 15);
                                var newDouble = Convert.ToDouble(newValue);
                                double roundedNewValue = Math.Round(newDouble, precision, MidpointRounding.AwayFromZero);
                                double roundedOldValue = Math.Round(oldDouble, precision, MidpointRounding.AwayFromZero);

                                valuesAreEqual = roundedNewValue == roundedOldValue;
                            }
                            else if ((newValue == null || newValue == DBNull.Value || newValue.Equals(string.Empty)) &&
                                    (oldValue == null || oldValue == DBNull.Value || oldValue.Equals(string.Empty)))
                            {
                                valuesAreEqual = true;
                            }
                            else
                            {
                                valuesAreEqual = Equals(newValue, oldValue);
                            }

                            if (!valuesAreEqual)
                            {
                                isDifferent = true;
                                // Console.WriteLine($" Column: {column.ColumnName}, Old Value: {oldValue}, New Value: {newValue}");
                            }
                        }
                        else
                        {
                            // Console.WriteLine($"Column '{column.ColumnName}' does not exist in the database table.");
                        }
                    }

                    if (isDifferent)
                    {
                        updatedRecords.Add(drNew);
                    }
                }

				var addOrUpdateRecords = newRecords.Concat(updatedRecords).ToList();

                Console.WriteLine("\nTotal Rows to Add: {0}", newRecords.Count);
                Console.WriteLine("\nTotal Rows to Update: {0}", updatedRecords.Count);
                if (addOrUpdateRecords.Count > 0)
                {
                    DataTable DTChangedRows = addOrUpdateRecords.CopyToDataTable();
                    DTChangedRows.TableName = TableName;

                    DTChangedRows.Columns.Add("updated", typeof(DateTime));
                    DTChangedRows.AcceptChanges();

                    DataTable DTTempInsTable = DTChangedRows.Clone();
                    DataTable DTTempUpdTable = DTChangedRows.Clone();

                    foreach (DataRow DRChanges in DTChangedRows.Rows)
                    {
                        DataRow DRCheckRow = DSFromDataBase.Tables[0].Select(KeyId + " = '" + DRChanges[KeyId].ToString().Trim() + "'").FirstOrDefault();

                        if (DRCheckRow != null)
                        {
                            DRChanges["updated"] = DateTime.UtcNow;
                            DRChanges.AcceptChanges();
                            DRChanges.SetModified();
                            DTTempUpdTable.ImportRow(DRChanges);
                            ChangedRows++;
                            //Console.Write("*");
                            if (ChangedRows % 100 == 0)
                                Console.Write("*");
                        }
                        else
                        {
                            DRChanges["updated"] = DateTime.UtcNow;
                            DRChanges.AcceptChanges();
                            DRChanges.SetAdded();
                            DTTempInsTable.ImportRow(DRChanges);
                            AddedRows++;
                            //Console.Write("+");
                            if (AddedRows % 100 == 0)
                                Console.Write("+");
                        }
                    }

                    Console.WriteLine("\nAttempting Adapter Update");

                    var bulk = new BulkOperation(DBConnect)
                    {
                        DestinationTableName = TableName
                    };

                    foreach (DataColumn DCChangedRows in DTChangedRows.Columns)
                    {
                        if (DCChangedRows.ColumnName == KeyId)
                            bulk.ColumnMappings.Add(DCChangedRows.ColumnName, true);
                        else
                            bulk.ColumnMappings.Add(DCChangedRows.ColumnName);
                    }

                    if (ChangedRows > 0)
                    {
                        Console.WriteLine("Updating Rows - Count: {0} ", DTTempUpdTable.Rows.Count);

                        MaxRowsToSend = 5000;
                        currentPage = 1;

                        if (DTTempUpdTable.Rows.Count % MaxRowsToSend == 0)
                        {
                            Console.WriteLine("Writing Bock of Data :Equal Division Pages is not adding one");
                            totalPages = (DTTempUpdTable.Rows.Count / MaxRowsToSend);
                        }
                        else
                        {
                            Console.WriteLine("Writing Bock of Data :Not Equal Division Pages adding one");
                            totalPages = (DTTempUpdTable.Rows.Count / MaxRowsToSend) + 1;

                        }


                        while (currentPage <= totalPages)
                        {
                            DataTable dtTemp = DTTempUpdTable.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                            dtTemp.TableName = DTTempUpdTable.TableName;
                            //dtTemp.WriteXml(TableName + "Test.XML");
                            Console.WriteLine("Updating Rows Block - {0} ", currentPage);


                            if (DBType == DatabaseType.Snowflake)
                            {
                                //if (BulkUpdate(dtTemp, KeyId))
                                if (BulkMerge(dtTemp, KeyId))
                                {
                                    Console.WriteLine("Snowflake Records Updated.");
                                    AfterGet = DateTime.UtcNow;
                                    if(currentPage == totalPages)
                                    {
                                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, DTTempUpdTable.Rows.Count , DTTempUpdTable.Rows.Count);
                                    }
                                    else
                                    {
                                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempUpdTable.Rows.Count);
                                    }

                                }
                                else
                                {
                                    Console.WriteLine("Snowflake Records couldn't be updated.");
                                    return false;
                                }
                            }
                            else
                            {
                                bulk.BatchTimeout = 180;
                                bulk.BulkUpdate(dtTemp);
                            }

                            currentPage++;
                        }
                    }
                    else
                        Console.WriteLine("Updating Rows - No Rows to Update");

                    if (AddedRows > 0)
                    {
                        Console.WriteLine("Inserting Rows - Count: {0}", DTTempInsTable.Rows.Count);

                        //DTTempInsTable.WriteXml(TableName + "DataInsert.xml");

                        MaxRowsToSend = 5000;
                        currentPage = 1;

                        if (DTTempInsTable.Rows.Count % MaxRowsToSend == 0)
                        {
                            Console.WriteLine("Equal Division Pages is not adding one");
                            totalPages = (DTTempInsTable.Rows.Count / MaxRowsToSend);
                        }
                        else
                        {
                            Console.WriteLine("Not Equal Division Pages adding one");
                            totalPages = (DTTempInsTable.Rows.Count / MaxRowsToSend) + 1;

                        }
                        //totalPages = (DTTempInsTable.Rows.Count / MaxRowsToSend) + 1;

                        while (currentPage <= totalPages)
                        {

                            DataTable dtTemp = DTTempInsTable.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                            dtTemp.TableName = DTTempInsTable.TableName;
                            //dtTemp.WriteXml(TableName + ".xml");
                            Console.WriteLine("Inserting Rows Block - {0} ", currentPage);
                            bulk.BatchTimeout = 180;

                            if (DBType == DatabaseType.Snowflake)
                            {
                                // Trigger as separate thread
                                if (BulkMerge(dtTemp, KeyId))
                                {
                                    Console.WriteLine("Records Inserted to Snowflake.");
                                    AfterGet = DateTime.UtcNow;
                                    if(currentPage == totalPages)
                                    {
                                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, DTTempInsTable.Rows.Count , DTTempInsTable.Rows.Count);
                                    }
                                    else
                                    {
                                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempInsTable.Rows.Count);
                                    }
                                }
                                else
                                {
                                    Console.WriteLine("Records couldn't inserted to Snowflake.");
                                    return false;
                                }
                            }
                            else
                            {
                                #warning Snowflake is not supported by Z.BulkOperations
                                bulk.BulkInsert(dtTemp);
                            }
                            currentPage++;
                        }
                    }
                    else
                        Console.WriteLine("Inserting Rows - No Rows to Insert");

                    Console.WriteLine("Added Rows {0} Changed Rows {1}", AddedRows, ChangedRows);
                }
                else
                    Console.WriteLine("No Updates Required");
                #endregion

                AfterGet = DateTime.UtcNow;
                Console.WriteLine("Bulk Upsert Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);
            }

            if (DBConnect.State == ConnectionState.Open)
                CloseConnectionToDatabase();

            DTTempData.Columns.Add("updated", typeof(DateTime));
            DTTempData.AcceptChanges();

            return true;
        }

        private bool BulkInsert(DataTable dtTemp)
        {
            bool Successful = false;
            StringBuilder sb = new StringBuilder();
            int rowsCount = 0;
            int colsCount = 0;

            #region Prepare SQL Script to Insert Bulk Data

            foreach (DataRow dr in dtTemp.Rows)
            {
                if (rowsCount == 0)
                {
                    sb.AppendLine($"INSERT INTO {dtTemp.TableName} (");
                    foreach (DataColumn dc in dtTemp.Columns)
                    {
                        if (colsCount > 0) sb.Append(", ");
                        sb.AppendLine(dc.ColumnName);
                        colsCount++;
                    }
                    sb.AppendLine(") ");
                    sb.AppendLine("VALUES ");
                }
                else
                {
                    sb.Append(", ");
                }

                sb.AppendLine("(");

                colsCount = 0;
                foreach (DataColumn dc in dtTemp.Columns)
                {
                    if (colsCount > 0) sb.Append(", ");

                    sb.Append(GetTypedValueFromDataColumn(dr, dc, DynamicSQLActionType.Insert));
                    colsCount++;
                }

                sb.AppendLine(")");

                rowsCount++;
            }

            #endregion

            // Insert into Database
            if (sb.ToString().Length > 0)
                try
                {
                    Successful = ExecuteSQLQuery(sb.ToString());
                }
                catch (System.Exception ex)
                {
                    Console.WriteLine(sb.ToString());
                    Console.WriteLine(ex.Message);
                    //throw;
                }

            return Successful;
        }

        private bool BulkUpdate(DataTable dtTemp, string KeyId)
        {
            bool Successful = false;
            StringBuilder sb = new StringBuilder();
            int rowsCount = 0;
            int colsCount = 0;

            #region Prepare SQL Script to Update Bulk Data

            foreach (DataRow dr in dtTemp.Rows)
            {
                colsCount = 0;
                sb.AppendLine($"-- Update record {rowsCount + 1}  --");
                sb.AppendLine($"UPDATE  {dtTemp.TableName} ");
                sb.AppendLine($"SET ");

                //Set Value
                foreach (DataColumn dc in dtTemp.Columns)
                {
                    if (colsCount > 0) sb.Append(", ");

                    sb.Append(GetTypedValueFromDataColumn(dr, dc, DynamicSQLActionType.Update));

                    colsCount++;
                }
                sb.AppendLine(" WHERE ");
                // Where Clause
                colsCount = 0;
                foreach (DataColumn dc in dtTemp.Columns)
                {
                    if (KeyId.ToLower() == dc.ColumnName.ToLower())
                    {
                        sb.Append(GetTypedValueFromDataColumn(dr, dc, DynamicSQLActionType.Update));
                    }
                    colsCount++;
                }

                sb.AppendLine(";");
                sb.AppendLine(" -- **********  ");
                rowsCount++;

            }

            #endregion

            // Update Records
            if (sb.ToString().Length > 0)
            {
                Successful = ExecuteSQLQuery(sb.ToString());
            }


            return Successful;
        }

        private bool BulkMerge(DataTable dtTemp, string KeyId)
        {
            bool Successful = false;
            StringBuilder sb = new StringBuilder();

            int rowsCount = 0;
            int colsCount = 0;
            try
            {
                string columnNames = GetSource(dtTemp);
                string updateClause = GetUpdateClause(dtTemp, KeyId);
                string insertClause = GetInsertClause(dtTemp);

                sb.AppendLine($"MERGE INTO {dtTemp.TableName} AS target");
                sb.AppendLine($"USING (");
                sb.AppendLine($"    VALUES ");

                foreach (DataRow dr in dtTemp.Rows)
                {
                    if (rowsCount > 0) sb.Append(",");

                    sb.AppendLine("(");
                    colsCount = 0;
                    foreach (DataColumn dc in dtTemp.Columns)
                    {
                        if (dc.ColumnName.ToLower().ToString().Equals("null")) continue;
                        if (colsCount > 0)
                        {
                            sb.Append(",");
                        }
                        sb.Append(GetTypedValueFromDataColumn(dr, dc, DynamicSQLActionType.Insert));
                        colsCount++;
                    }
                    sb.Append(")");
                    rowsCount++;
                }

                sb.AppendLine($" AS source ({columnNames})");

                sb.AppendLine($" )"); // End Using

                sb.AppendLine($"ON target.{KeyId} = source.{KeyId}");
                sb.AppendLine($"WHEN MATCHED THEN");
                sb.AppendLine($"UPDATE SET");
                sb.AppendLine($"        {updateClause}");
                sb.AppendLine($"WHEN NOT MATCHED THEN");
                sb.AppendLine($"INSERT ({columnNames})");
                sb.AppendLine($"VALUES ({insertClause})");

                // Update Records
                if (sb.ToString().Length > 0)
                {
                    Successful = ExecuteSQLQuery(sb.ToString());
                    
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Writing DB Write Error");
                throw;
            }
            return Successful;
        }


        private string GetSource(System.Data.DataTable dtTemp)
        {
            StringBuilder sb = new StringBuilder();
            int colsCount = 0;

            foreach (DataColumn dc in dtTemp.Columns)
            {
                if (dc.ColumnName.ToLower().ToString().Equals("null")) continue;
                if (colsCount > 0) sb.Append(",");

                if (!dc.ColumnName.Equals("updated", StringComparison.InvariantCultureIgnoreCase))
                    sb.Append($"{dc.ColumnName}");
                else
                    sb.Append($"{dc.ColumnName}");

                colsCount++;
            }
            return sb.ToString();
        }

        private string GetUpdateClause(System.Data.DataTable dtTemp, string keyId)
        {
            StringBuilder sb = new StringBuilder();
            int colsCount = 0;

            foreach (DataColumn dc in dtTemp.Columns)
            {
                if (dc.ColumnName.ToLower().Equals("null")) continue;

                if (sb.Length > 0) sb.Append(",");
                if (!dc.ColumnName.Equals(keyId, StringComparison.InvariantCultureIgnoreCase))
                    if (!dc.ColumnName.ToLower().Equals("updated", StringComparison.InvariantCultureIgnoreCase))
                        sb.Append($"{dc.ColumnName} = source.{dc.ColumnName}");
                    else 
                        sb.Append($"{dc.ColumnName} = source.{dc.ColumnName}");

                colsCount++;
            }
            return sb.ToString();
        }

        private string GetInsertClause(System.Data.DataTable dtTemp)
        {
            StringBuilder sbInsert = new StringBuilder();
            int colsCount = 0;

            foreach (DataColumn dc in dtTemp.Columns)
            {
                if (dc.ColumnName.ToLower().ToString().Equals("null")) continue;

                if (colsCount > 0) sbInsert.Append(",");
                if (!dc.ColumnName.Equals("updated", StringComparison.InvariantCultureIgnoreCase))
                    sbInsert.Append($"source.{dc.ColumnName}");
                else
                    sbInsert.Append($"source.{dc.ColumnName}");

                colsCount++;
            }
            return sbInsert.ToString();
        }


        private string GetTypedValueFromDataColumn(DataRow dr, DataColumn dc, DynamicSQLActionType actionType)
        {
            try
            {
                string typedValue = string.Empty;
                if (dc.DataType == typeof(string))
                {
                    typedValue = ($"'{dr[dc.ColumnName].ToString().Replace("'", "''")}'");
                    typedValue = typedValue.Replace("\\", "\\\\\\\\");
                     
                }
                else if (dc.DataType == typeof(int) || dc.DataType == typeof(decimal) || dc.DataType == typeof(bool))
                {
                    typedValue = ($"{dr[dc.ColumnName]}");
                }
                else if (dc.DataType == typeof(DateTime))
                {
                    if (dr[dc.ColumnName] != DBNull.Value)
                    {
                        DateTime dt = Convert.ToDateTime(dr[dc.ColumnName]);
                        typedValue = $"'{dt.ToString("yyyy-MM-dd HH:mm:ss")}'";
                    }
                }
                else
                {
                    typedValue = $"'{dr[dc.ColumnName].ToString()}'";
                }

                if (string.IsNullOrEmpty(typedValue) || typedValue == "''")
                    typedValue = "null";



                if (actionType == DynamicSQLActionType.Update)
                {
                    if (dc.ColumnName != "updated")
                        return $"\"{dc.ColumnName.ToUpper()}\" = {typedValue}";
                    else
                        return $"{dc.ColumnName} = {typedValue}";

                }

                return typedValue;
            }
            catch (System.Exception ex)
            {

                throw;
            }
        }

        public bool WriteDynamicSQLData(DataTable DTTempData, String TableName)
        {
            bool Successful = false;
            Console.WriteLine("DBUtils:Checking Columns for Dynamic Data Storage\nTable Name {0} \nActual Tab Name {1} Total Rows {2}\n", TableName, DTTempData.TableName, DTTempData.Rows.Count);
            try
            {
                string SQLStatement = string.Empty;

                switch (DBType)
                {
                    case DatabaseType.MSSQL:
                        SQLStatement = "Select TOP (0) * From " + TableName;
                        break;
                    case DatabaseType.MySQL:
                    case DatabaseType.PostgreSQL:
                    case DatabaseType.Snowflake:
                        SQLStatement = "Select * From " + TableName + " limit 0";
                        break;
                    default:
                        throw new NotImplementedException("Database type is not implemented");
                }

                DataTable DTCompare = GetSQLTableData(SQLStatement, TableName).Clone();
                DataColumnCollection columns = DTCompare.Columns;

                foreach (DataColumn DCTemp in DTTempData.Columns)
                {
                    //Console.WriteLine("Comparing Column Names {0} ", DCTemp.ColumnName);
                    Console.Write("CC:");
                    if (columns.Contains(DCTemp.ColumnName) == false)
                    {
                        if (DCTemp.MaxLength > 0)
                        {
                            Successful = AddColumnToDB(TableName, DCTemp.ColumnName, DCTemp.DataType.ToString(), DCTemp.MaxLength);
                        }
                        else
                        {
                            Successful = AddColumnToDB(TableName, DCTemp.ColumnName, DCTemp.DataType.ToString());
                        }
                    }
                }

                Console.WriteLine("\n");

                Successful = WriteSQLDataBulk(DTTempData, TableName);
            }
            catch (System.Exception ex)
            {
                Console.WriteLine("Dynamic Writing DB Write Error");
                throw;
            }

            return Successful;
        }

        public bool MYSQLScript(string SQLCommand)
        {
            ConnectToDatabase();

            if (DBConnect.State == ConnectionState.Open)
            {
                MySqlScript MySqlscript = new MySqlScript((MySqlConnection)DBConnect, SQLCommand);
                MySqlscript.Delimiter = "$$";
                MySqlscript.Execute();
            }
            CloseConnectionToDatabase();

            return true;
        }

#nullable enable
        /// <summary>
        /// Execute a non-query SQL statement
        /// </summary>
        /// <param name="sql">the SQL text to execute.</param>
        /// <param name="commandTimeout">If specified, overrides the default command timeout, in seconds.</param>
        public int ExecuteSqlNonQuery(string sql, int commandTimeout = -1)
        {
            int rowsAffected = 0;

            try
            {
                // Use a new connection for this operation
                using (var connection = CreateNewConnection())
                {
                    connection.Open();
                    
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = sql;
                        if (commandTimeout >= 0)
                            command.CommandTimeout = commandTimeout;

                        if (DBType == DatabaseType.Snowflake)
                        {
                            // Set statement count
                            var stmtCountParam = command.CreateParameter();
                            stmtCountParam.ParameterName = "MULTI_STATEMENT_COUNT";
                            stmtCountParam.DbType = DbType.Int16;
                            stmtCountParam.Value = 0;
                            command.Parameters.Add(stmtCountParam);
                        }

                        var timer = System.Diagnostics.Stopwatch.StartNew();
                        try
                        {
                            rowsAffected = command.ExecuteNonQuery();
                        }
                        catch (Exception ex) when (ex.InnerException is TimeoutException)
                        {
                            _logger?.LogError(
                                ex,
                                "SQL command timed out, executed for {TimeTaken:N3} secs, " +
                                "command timeout is {CommandTimeout:N3} secs.",
                                timer.Elapsed.TotalSeconds,
                                command.CommandTimeout);

                            throw;
                        }
                    }
                }
                
                return rowsAffected;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[DBUtils][NonQuery] Error executing SQL: {SQL}", sql);
                throw;
            }
        }

        public bool ExecuteSQLQuery(string SQLCommand)
        {
            try
            {
                // Use a new connection for this operation
                using (var connection = CreateNewConnection())
                {
                    connection.Open();
                    
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = SQLCommand;
                        
                        if (DBType == DatabaseType.Snowflake)
                        {
                            // Set statement count
                            var stmtCountParam = command.CreateParameter();
                            stmtCountParam.ParameterName = "MULTI_STATEMENT_COUNT";
                            stmtCountParam.DbType = DbType.Int16;
                            stmtCountParam.Value = 0;
                            command.Parameters.Add(stmtCountParam);
                        }
                        
                        command.ExecuteNonQuery();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger != null)
                    _logger?.LogWarning(ex, "Suppressed error in ExecuteSQLQuery");
                else
                    Console.WriteLine(ex.ToString());

                return false;
            }
        }
#nullable restore

        public Boolean AddColumnToDB(string Tablename, string ColumnName, string ColumnType, int ColumnSize = 0)
        {
            Boolean Successful = false;

            string ColType = string.Empty;

            if (ColumnSize == 0)
            {
                switch (DBType)
                {
                    case DatabaseType.MSSQL:
                        ColumnSize = 200;
                        break;
                    case DatabaseType.MySQL:
                        ColumnSize = 50;
                        break;
                    case DatabaseType.PostgreSQL:
                    case DatabaseType.Snowflake:
                        ColumnSize = 100;
                        break;
                    default:
                        throw new NotImplementedException("Database type is not implemented");
                }
            }

            Console.WriteLine("Adding Col: {0} to Table:{1} Type : {2}", ColumnName, Tablename, ColumnType);

            string AddColumn = String.Empty;

            switch (DBType)
            {
                case DatabaseType.MSSQL:
                    switch (ColumnType.ToLower())
                    {
                        case "system.string":
                            ColType = "[nvarchar](" + ColumnSize + ")";
                            break;
                        case "system.int32":
                        case "system.int16":
                            ColType = "[int]";
                            break;
                        case "system.decimal":
                            ColType = "[decimal](20, 2)";
                            break;
                        case "system.boolean":
                            ColType = "[bit]";
                            break;
                        case "system.datetime":
                            ColType = "[datetime]";
                            break;
                        case "system.text":
                            ColType = "[nvarchar](max)";
                            break;
                    }
                    AddColumn = "ALTER TABLE " + Tablename + " " +
                                "ADD [" + ColumnName + "] " + ColType + " NULL;";
                    break;

                case DatabaseType.MySQL:
                    switch (ColumnType.ToLower())
                    {
                        case "system.string":
                            ColType = "nvarchar (" + ColumnSize + ")";
                            break;
                        case "system.int32":
                            ColType = "int";
                            break;
                        case "system.decimal":
                            ColType = "decimal (20, 2)";
                            break;
                        case "system.boolean":
                            ColType = "tinyint";
                            break;
                        case "system.datetime":
                            ColType = "datetime ";
                            break;
                        case "system.text":
                            ColType = "mediumtext ";
                            break;
                    }


                    AddColumn = "ALTER TABLE " + Tablename + " " +
                                "ADD `" + ColumnName + "` " + ColType + " NULL;";
                    break;


                case DatabaseType.PostgreSQL:
                    switch (ColumnType.ToLower())
                    {
                        case "system.string":
                            ColType = "character varying(" + ColumnSize + ")";
                            break;
                        case "system.int32":
                            ColType = "integer";
                            break;
                        case "system.decimal":
                            ColType = "numeric(20, 2)";
                            break;
                        case "system.boolean":
                            ColType = "bit";
                            break;
                        case "system.datetime":
                            ColType = "timestamp without time zone";
                            break;
                        case "system.text":
                            ColType = "text ";
                            break;
                    }


                    AddColumn = "ALTER TABLE " + Tablename.ToLower() + " " +
                                "ADD COLUMN \"" + ColumnName.ToLower() + "\" " + ColType + ";";
                    break;

                case DatabaseType.Snowflake:
                    switch (ColumnType.ToLower())
                    {
                        case "system.string":
                            ColType = "varchar (" + ColumnSize + ")";
                            break;
                        case "system.int32":
                        case "system.int16":
                            ColType = "number (38,0)";
                            break;
                        case "system.decimal":
                            ColType = "number (20, 2)";
                            break;
                        case "system.boolean":
                            ColType = "boolean";
                            break;
                        case "system.datetime":
                            ColType = "TIMESTAMP_LTZ";
                            break;
                        case "system.text":
                            ColType = "nvarchar";
                            break;
                    }
                   
                    AddColumn = "ALTER TABLE " + Tablename.ToLower() + " " +
                               "ADD COLUMN IF NOT exists " + ColumnName.ToLower() + " " + ColType + ";";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            ExecuteSqlNonQuery(AddColumn);
         
            Console.WriteLine("Altering Column              {0} ", Successful);
            return Successful;
        }

#nullable enable
        public Database ParseConnectionString(
            Database databaseOptions,
            string connectionString)
        {
            switch (databaseOptions.Type)
            {
                case DatabaseType.MSSQL:
                    var mssqlBuilder = new SqlConnectionStringBuilder(connectionString);
                    if (!string.IsNullOrEmpty(mssqlBuilder.DataSource))
                        databaseOptions.Address = mssqlBuilder.DataSource;
                    if (!string.IsNullOrEmpty(mssqlBuilder.InitialCatalog))
                        databaseOptions.Name = mssqlBuilder.InitialCatalog;
                    if (!string.IsNullOrEmpty(mssqlBuilder.UserID) && !string.IsNullOrEmpty(mssqlBuilder.Password))
                    {
                        databaseOptions.User = mssqlBuilder.UserID;
                        databaseOptions.Password = new Secret(mssqlBuilder.UserID, mssqlBuilder.Password).Encrypted;
                    }
                    databaseOptions.ConnectOptions = mssqlBuilder.ConnectionString;
                    break;
#if MYSQL
                case DatabaseType.MySQL:
                    var mysqlBuilder = new MySqlConnectionStringBuilder(connectionString);
                    if (!string.IsNullOrEmpty(mysqlBuilder.Server))
                        databaseOptions.Address = mysqlBuilder.Server;
                    if (mysqlBuilder.Port != 0)
                        databaseOptions.Port = (int)mysqlBuilder.Port;
                    if (!string.IsNullOrEmpty(mysqlBuilder.Database))
                        databaseOptions.Name = mysqlBuilder.Database;
                    if (!string.IsNullOrEmpty(mysqlBuilder.UserID) && !string.IsNullOrEmpty(mysqlBuilder.Password))
                    {
                        databaseOptions.User = mysqlBuilder.UserID;
                        databaseOptions.Password = new Secret(mysqlBuilder.UserID, mysqlBuilder.Password).Encrypted;
                    }
                    databaseOptions.ConnectOptions = mysqlBuilder.ConnectionString;
                    break;
#endif
                case DatabaseType.PostgreSQL:
                    var pgBuilder = new NpgsqlConnectionStringBuilder(connectionString);
                    if (!string.IsNullOrEmpty(pgBuilder.Host))
                    {
                        databaseOptions.Address = pgBuilder.Host;
                    }
                    if (pgBuilder.Port != 0)
                    {
                        databaseOptions.Port = pgBuilder.Port;
                    }
                    if (!string.IsNullOrEmpty(pgBuilder.Database))
                    {
                        databaseOptions.Name = pgBuilder.Database;
                    }
                    if (!string.IsNullOrEmpty(pgBuilder.Username) && !string.IsNullOrEmpty(pgBuilder.Password))
                    {
                        databaseOptions.User = pgBuilder.Username;
                        databaseOptions.Password = new Secret(pgBuilder.Username, pgBuilder.Password).Encrypted;
                    }
                    if (!string.IsNullOrEmpty(pgBuilder.SearchPath))
                    {
                        databaseOptions.Schema = pgBuilder.SearchPath;
                    }
                    databaseOptions.ConnectOptions = pgBuilder.ConnectionString;
                    break;

                case DatabaseType.Snowflake:
                    var snowflakeBuilder = new SnowflakeDbConnectionStringBuilder();
                    snowflakeBuilder.ConnectionString = connectionString;
                    if (snowflakeBuilder.ContainsKey("Account"))
                        databaseOptions.Address = snowflakeBuilder["Account"] as string;
                    if (snowflakeBuilder.ContainsKey("Database"))
                        databaseOptions.Name = snowflakeBuilder["Database"] as string;
                    if (snowflakeBuilder.ContainsKey("User") && snowflakeBuilder.ContainsKey("Password"))
                    {
                        databaseOptions.User = snowflakeBuilder["User"] as string;
                        var pwd = snowflakeBuilder["Password"] as string;
                        databaseOptions.Password = new Secret(databaseOptions.User!, pwd!).Encrypted;
                        pwd = null;
                    }
                    databaseOptions.ConnectOptions = snowflakeBuilder.ConnectionString;
                    break;
                default:
                    throw new NotImplementedException("Not a supported database platform");
            }

            return databaseOptions;
        }

        public string BuildConnectionString(CSG.Adapter.Configuration.Database databaseOptions)
        {
            const string appName = "Genesys Cloud Data Adapter";
            string connectionString = "";

            if (databaseOptions.User is null || databaseOptions.Password is null)
                throw new ArgumentNullException("Database user or password not set.");

            switch (databaseOptions.Type)
            {
                case DatabaseType.MSSQL:
                    // https://learn.microsoft.com/en-us/dotnet/api/microsoft.data.sqlclient.sqlconnectionstringbuilder
                    var mssqlBuilder = new SqlConnectionStringBuilder(
                        "Connection Timeout=60;" +
                        databaseOptions.ConnectOptions);

                    if (databaseOptions.Port == 0)
                        mssqlBuilder.DataSource = databaseOptions.Address;
                    else
                        mssqlBuilder.DataSource = string.Format("{0},{1}", databaseOptions.Address, databaseOptions.Port);
                    mssqlBuilder.InitialCatalog = databaseOptions.Name;
                    mssqlBuilder.UserID = databaseOptions.User;
                    mssqlBuilder.Password = new Secret(databaseOptions.User, databaseOptions.Password).PlainText;
                    mssqlBuilder.ApplicationName = appName;

                    connectionString = mssqlBuilder.ToString();
                    mssqlBuilder.Password = "***";
                    _logger?.LogDebug("Using connection string {0}", mssqlBuilder.ToString());
                    break;
#if MYSQL
                case DatabaseType.MySQL:
                    // https://dev.mysql.com/doc/dev/connector-net/6.10/html/T_MySql_Data_MySqlClient_MySqlConnectionStringBuilder.htm
                    var mysqlBuilder = new MySqlConnectionStringBuilder(
                        "SSL Mode=Required;" +
                        databaseOptions.ConnectOptions);

                    mysqlBuilder.Server = databaseOptions.Address;
                    if (databaseOptions.Port != 0)
                        mysqlBuilder.Port = (uint)databaseOptions.Port;
                    mysqlBuilder.Database = databaseOptions.Name;
                    mysqlBuilder.UserID = databaseOptions.User;
                    mysqlBuilder.Password = new Secret(databaseOptions.User, databaseOptions.Password).PlainText;

                    databaseOptions.Port = (int)mysqlBuilder.Port;
                    if (!string.IsNullOrEmpty(mysqlBuilder.Database))
                        databaseOptions.Name = mysqlBuilder.Database;
                    if (!string.IsNullOrEmpty(mysqlBuilder.UserID) && !string.IsNullOrEmpty(mysqlBuilder.Password))
                    {
                        databaseOptions.User = mysqlBuilder.UserID;
                        databaseOptions.Password = new Secret(mysqlBuilder.UserID, mysqlBuilder.Password).Encrypted;
                    }
                    databaseOptions.ConnectOptions = mysqlBuilder.ConnectionString;

                    connectionString = mysqlBuilder.ToString();
                    mysqlBuilder.Password = "***";
                    _logger?.LogDebug("Using connection string {0}", mysqlBuilder.ToString());
                    break;
#endif
                case DatabaseType.PostgreSQL:
                    // https://www.npgsql.org/doc/connection-string-parameters.html
                    // https://www.npgsql.org/doc/api/Npgsql.NpgsqlConnectionStringBuilder.html

                    // https://www.postgresql.org/docs/current/ddl-schemas.html
                    // The first schema named in the search path is called the
                    // current schema. Aside from being the first schema
                    // searched, it is also the schema in which new tables will
                    // be created if the CREATE TABLE command does not specify a
                    // schema name.

                    var pgBuilder = new NpgsqlConnectionStringBuilder(
                        "Timeout=90;Connection Idle Lifetime=10;Pooling=true;MinPoolSize=1;MaxPoolSize=20;" +
                        databaseOptions.ConnectOptions);

                    pgBuilder.Host = databaseOptions.Address;
                    if (pgBuilder.Port != 0)
                        pgBuilder.Port = databaseOptions.Port;
                    pgBuilder.Database = databaseOptions.Name;
                    pgBuilder.Username = databaseOptions.User;
                    pgBuilder.Password = new Secret(databaseOptions.User, databaseOptions.Password).PlainText;
                    
                    // Set the search path in the connection string instead of running a command after connecting
                    pgBuilder.SearchPath = databaseOptions.Schema;
                    pgBuilder.ApplicationName = appName;

                    connectionString = pgBuilder.ToString();
                    pgBuilder.Password = "***";
                    _logger?.LogDebug("Using connection string {0}", pgBuilder.ToString());
                    break;
                case DatabaseType.Snowflake:
                    var snowflakeBuilder = new SnowflakeDbConnectionStringBuilder();
                    snowflakeBuilder.ConnectionString = databaseOptions.ConnectOptions;

                    snowflakeBuilder["Account"] = databaseOptions.Address?.TrimEnd(".snowflakecomputing.com".ToCharArray());
                    if (!snowflakeBuilder.ContainsKey("Port"))
                        snowflakeBuilder["Port"] = databaseOptions.Port;
                    snowflakeBuilder["DB"] = databaseOptions.Name;
                    snowflakeBuilder["User"] = databaseOptions.User;
                    snowflakeBuilder["Password"] = new Secret(databaseOptions.User, databaseOptions.Password).PlainText;
                    snowflakeBuilder["Schema"] = databaseOptions.Schema;

                    connectionString = snowflakeBuilder.ToString();
                    snowflakeBuilder["Password"] = "***";
                    _logger?.LogDebug("Using connection string {0}", snowflakeBuilder.ToString());
                    break;
                default:
                    throw new NotImplementedException("Not a supported database platform");
            }

            return connectionString;
        }
#nullable restore

        public void ConnectToDatabase()
        {
            switch (DBType)
            {
                case DatabaseType.MSSQL:
                    DBConnect = new SqlConnection(DBConnectionString);
                    DBDataAdapter = new SqlDataAdapter();
                    break;
                case DatabaseType.MySQL:
                    DBConnect = new MySqlConnection(DBConnectionString);
                    DBDataAdapter = new MySql.Data.MySqlClient.MySqlDataAdapter();
                    break;
                case DatabaseType.PostgreSQL:
                    DBConnect = new NpgsqlConnection(DBConnectionString);
                    DBDataAdapter = new NpgsqlDataAdapter();
                    break;
                case DatabaseType.Snowflake:
                    DBConnect = new SnowflakeDbConnection(DBConnectionString);
                    DBDataAdapter = new SnowflakeDbDataAdapter();
                    break;
                default:
                    throw new NotImplementedException();
            }

            var timer = System.Diagnostics.Stopwatch.StartNew();
            if (DBConnect.State != ConnectionState.Open)
            {
                try
                {
                    DBConnect.Open();
                }
                catch (Exception ex)
                {
                    if (_logger != null)
                    {
                        _logger?.LogWarning(
                            ex,
                            "Failed to connect to database, timeout {0}, took {1}",
                            DBConnect.ConnectionTimeout,
                            timer.Elapsed.ToString());
                    }
                    else
                    {
                        Console.WriteLine("Failed to connect to database, timeout {0}, took {1}",
                            DBConnect.ConnectionTimeout,
                            timer.Elapsed.ToString());
                        Console.WriteLine(ex.ToString());
                    }
                    throw;
                }
            }
            if (timer.Elapsed > TimeSpan.FromSeconds(DBConnect.ConnectionTimeout * 0.75))
                Console.WriteLine("WARNING: Connect to database took {0}, timeout {1}",
                        timer.Elapsed.ToString(),
                        DBConnect.ConnectionTimeout);
            timer.Restart();

            // Remove the problematic command execution for PostgreSQL
            // The search_path should be set in the connection string instead
        }

        internal void CloseConnectionToDatabase()
        {
            // This method should no longer be used with the new connection management approach
            // Only keeping it to avoid breaking existing code, but it's a no-op now
            if (DBConnect != null)
            {
                try
                {
                    DBConnect?.Close();
                    DBConnect?.Dispose();
                    DBConnect = null;
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Error closing database connection");
                }
            }
        }

        internal bool ReadDBConnectionString()
        {
            DBConnectionString = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_SQLCONNECTIONSTRING");
            /*
            Utils Util = new Utils();
            try
            {
                UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
                DBConnectionStringEncrypted = Util.ReadSetting("CSG_SQLCONNECTIONSTRING");
                DBConnectionString = UCAEncryption.DecryptData(DBConnectionStringEncrypted);
            }
            catch (Exception ex)
            {
                if (_logger != null)
                    _logger?.LogWarning(ex, "DB Unencrypt the Connection String Error");
                else
                    Console.WriteLine("DB Unencrypt the Connection String Error: {0}", ex.ToString());

                throw;
            }
            */

            return true;
        }

        public DataTable CreateInMemTable(string TableName)
        {
            string SQLStatement = string.Empty;
            switch (DBType)
            {
                case DatabaseType.MSSQL:
                    SQLStatement = "SELECT TOP (0) * FROM " + TableName;
                    break;
                case DatabaseType.MySQL:
                case DatabaseType.Snowflake:
                    SQLStatement = "SELECT  * FROM " + TableName + " LIMIT 0";
                    break;
                case DatabaseType.PostgreSQL:
                    SQLStatement = "SELECT  * FROM " + TableName.ToLower() + " LIMIT 0";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            _logger?.LogTrace("SQL Statement {0}", SQLStatement);
            try
            {
                DataTable DTTemp = GetSQLTableData(SQLStatement, TableName).Clone();

                DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns[0] };

                return DTTemp;
            }
            catch (Exception ex)
            {
                if (!SurpressErrors)
                {
                    _logger?.LogTrace("SQL Statement {0}", SQLStatement);
                    if (_logger != null)
                        _logger?.LogWarning(ex, "Issue Creating Blank Memory Table: {TableName}", TableName);
                    else
                        Console.WriteLine("Issue Creating Blank Memory Table :{0}\nError:{1}\nInner:{2}", TableName, ex.ToString(), ex.InnerException);
                }
                return null;
            }
        }

        public void DeleteSchedData(DateTime StartDate, DateTime EndDate)
        {
            ConnectToDatabase();
            try
            {
                DBDeleteCommand = DBConnect.CreateCommand();

                string DeleteString = String.Empty;

                if (DBConnect.State == ConnectionState.Open)
                {
                    switch (DBType)
                    {
                        case DatabaseType.MSSQL:
                        case DatabaseType.MySQL:
                            DeleteString = "Delete a from scheduleData a Inner Join ( select distinct sd.shiftid,sd.scheduleid from scheduleData sd where sd.activitystartdate between @startdate and @enddate) b on  a.shiftid = b.shiftid and a.scheduleid = b.scheduleid where a.shiftstartdate between @startdate and @enddate";
                            break;
                        case DatabaseType.PostgreSQL:
                            DeleteString = "DELETE FROM scheduledata a using ( select distinct sd.shiftid,sd.scheduleid from scheduledata sd where sd.activitystartdate between @startdate and @enddate) b where  a.shiftid = b.shiftid and a.scheduleid = b.scheduleid and a.shiftstartdate between @startdate and @enddate";
                            break;
                        case DatabaseType.Snowflake:
                            DeleteString = "DELETE FROM scheduledata a using ( select distinct sd.shiftid,sd.scheduleid from scheduledata sd where sd.activitystartdate between :startdate and :enddate) b where  a.shiftid = b.shiftid and a.scheduleid = b.scheduleid and a.shiftstartdate between :startdate and :enddate";
                            break;
                        default:
                            throw new NotImplementedException("Database type is not implemented");
                    }
                    DBDeleteCommand.CommandText = DeleteString;

                    IDbDataParameter DeleteParameter;

                    DeleteParameter = DBDeleteCommand.CreateParameter();
                    DeleteParameter.ParameterName = "startdate";
                    DeleteParameter.Value = StartDate;
                    if (DBType == DatabaseType.Snowflake)
                    {
                        // https://learn.microsoft.com/en-us/dotnet/api/system.data.dbtype
                        // "If the type is not specified, ADO.NET infers the data provider Type of the Parameter from the
                        // Value property of the Parameter object."
                        // Snowflake does not appear to implement automatic type mapping for bind variables.
                        // Dapper uses a manually defined lookup, so will manually assign.
                        // https://github.com/DapperLib/Dapper/blob/4fb1ea29d490d13251b0135658ecc337aeb60cdb/Dapper/SqlMapper.cs#L169
                        // When this is moved to a generic implementation of parameters, fully define the mapping.
                        DeleteParameter.DbType = DbType.DateTime;
                    }
                    DBDeleteCommand.Parameters.Add(DeleteParameter);

                    DeleteParameter = DBDeleteCommand.CreateParameter();
                    DeleteParameter.ParameterName = "enddate";
                    DeleteParameter.Value = EndDate;
                    if (DBType == DatabaseType.Snowflake)
                        DeleteParameter.DbType = DbType.DateTime;
                    DBDeleteCommand.Parameters.Add(DeleteParameter);

                    DBDeleteCommand.ExecuteNonQuery();
                }
            }
            finally
            {
                if (DBConnect?.State == ConnectionState.Open)
                    CloseConnectionToDatabase();
            }
        }

        /// <summary>
        /// Executes a SQL command and returns the first column of the first row in the result set
        /// </summary>
        /// <param name="SQLCommand">The SQL command to execute</param>
        /// <returns>First column of first row or null if no results</returns>
        public object ExecuteScalar(string SQLCommand)
        {
            _logger?.LogDebug("[DBUtils][Scalar] Executing scalar query: {SQLCommand}", SQLCommand);
            
            try
            {
                Initialize();
                
                // Use a new connection for this operation
                using (var connection = CreateNewConnection())
                {
                    connection.Open();
                    
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = SQLCommand;
                        command.CommandTimeout = 90; // Use a standard timeout of 90 seconds
                        return command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[DBUtils][Scalar] Error executing scalar query: {SQLCommand}", SQLCommand);
                throw;
            }
        }

        public bool TruncateTable(string tableName, ILogger logger = null)
        {
            try
            {
                logger?.LogInformation("[DBUtils][Truncate] Truncating table {TableName}", tableName);
                
                string truncateStatement = DBType switch
                {
                    CSG.Adapter.Configuration.DatabaseType.MSSQL => 
                        $"DELETE FROM {tableName}",
                    CSG.Adapter.Configuration.DatabaseType.MySQL => 
                        $"TRUNCATE TABLE {tableName}",
                    CSG.Adapter.Configuration.DatabaseType.PostgreSQL => 
                        $"TRUNCATE TABLE {tableName}",
                    CSG.Adapter.Configuration.DatabaseType.Snowflake => 
                        $"TRUNCATE TABLE {tableName}",
                    _ => $"DELETE FROM {tableName}"
                };
                
                int rowsAffected = ExecuteSqlNonQuery(truncateStatement);
                logger?.LogInformation("[DBUtils][Truncate] Successfully truncated table {TableName} - {RowsAffected} rows affected", 
                    tableName, rowsAffected);
                
                return true;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "[DBUtils][Truncate] Error truncating table {TableName}", tableName);
                return false;
            }
        }

    }
}
// spell-checker: ignore: tinyint, Npgsql, shiftid
