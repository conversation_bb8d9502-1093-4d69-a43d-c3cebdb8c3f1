using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils.WebSocket
{
    /// <summary>
    /// Helper class for creating combined subscriptions to notification topics
    /// following Genesys Cloud best practices while maintaining compatibility with legacy tables.
    /// </summary>
    public static class CombinedSubscriptionHelper
    {
        /// <summary>
        /// Creates batched subscriptions for a list of user IDs across one or more channels
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="jsonUtils">JsonUtils instance for making API calls</param>
        /// <param name="userTable">DataTable containing user IDs</param>
        /// <param name="channel">WebSocket channel to subscribe on</param>
        /// <param name="topicPrefix">Prefix for the topic (e.g., "v2.users.")</param>
        /// <param name="topicSuffix">Suffix for the topic (e.g., ".presence")</param>
        /// <param name="idColumnName">Column name containing the IDs</param>
        /// <returns>True if subscriptions were created successfully</returns>
        public static async Task<bool> CreateUserSubscriptionsAsync(
            ILogger logger,
            JsonUtils jsonUtils,
            DataTable userTable,
            WebSocketDetail channel,
            string topicPrefix,
            string topicSuffix,
            string idColumnName = "id")
        {
            // Add null checks for all parameters
            if (logger == null)
                throw new ArgumentNullException(nameof(logger), "Logger cannot be null");
                
            if (jsonUtils == null)
                throw new ArgumentNullException(nameof(jsonUtils), "JsonUtils cannot be null");
                
            if (userTable == null)
                throw new ArgumentNullException(nameof(userTable), "User table cannot be null");
                
            if (channel == null)
                throw new ArgumentNullException(nameof(channel), "Channel cannot be null");
                
            if (string.IsNullOrEmpty(topicPrefix))
                throw new ArgumentException("Topic prefix cannot be null or empty", nameof(topicPrefix));
                
            if (string.IsNullOrEmpty(topicSuffix))
                throw new ArgumentException("Topic suffix cannot be null or empty", nameof(topicSuffix));
                
            if (string.IsNullOrEmpty(idColumnName))
                idColumnName = "id"; // Default to "id" if not specified
                
            if (!userTable.Columns.Contains(idColumnName))
                throw new ArgumentException($"User table does not contain column '{idColumnName}'", nameof(idColumnName));
                
            if (userTable.Rows.Count == 0)
            {
                logger.LogWarning("User table is empty - no subscriptions to create");
                return false;
            }

            logger?.LogInformation("Creating {TopicType} subscriptions for {Count} users on channel {ChannelId}",
                topicSuffix.TrimStart('.'), userTable.Rows.Count, channel.id);

            const int maxTopicsPerRequest = 1000;
            int totalUsers = userTable.Rows.Count;
            bool success = true;
            int retryLimit = 3;

            for (int i = 0; i < totalUsers; i += maxTopicsPerRequest)
            {
                int chunkSize = Math.Min(maxTopicsPerRequest, totalUsers - i);
                var subscriptionJson = new StringBuilder();
                int counter = 0;

                // Build JSON array of topics
                for (int j = i; j < i + chunkSize && j < totalUsers; j++)
                {
                    var row = userTable.Rows[j];
                    string id = row[idColumnName]?.ToString();
                    if (!string.IsNullOrEmpty(id))
                    {
                        subscriptionJson.Append($"{{ \"id\": \"{topicPrefix}{id}{topicSuffix}\"}},");
                        counter++;
                    }
                }

                if (counter > 0)
                {
                    // Remove trailing comma
                    subscriptionJson.Length--;
                    
                    // Final JSON payload
                    string jsonBody = $"[{subscriptionJson}]";
                    string url = $"{jsonUtils.ApiEndpoint}/api/v2/notifications/channels/{channel.id}/subscriptions";

                    logger?.LogDebug("Sending combined subscription request for {Count} topics (batch {BatchNumber} of {TotalBatches})",
                        counter, (i / maxTopicsPerRequest) + 1, (int)Math.Ceiling((double)totalUsers / maxTopicsPerRequest));

                    // Change: Add retry logic with exponential backoff
                    int retryCount = 0;
                    bool requestSuccess = false;
                    
                    while (!requestSuccess && retryCount < retryLimit)
                    {
                        try
                        {
                            // Make the API request
                            string response = await jsonUtils.JsonRestAsync(url, null, "POST", jsonBody);

                            if (string.IsNullOrEmpty(response))
                            {
                                logger?.LogWarning("Empty response when creating subscriptions, batch {BatchNumber}, retry {RetryCount}",
                                    (i / maxTopicsPerRequest) + 1, retryCount);
                                retryCount++;
                            }
                            else if (response.Contains("error"))
                            {
                                logger?.LogError("Error creating subscriptions: {Response}, retry {RetryCount}", 
                                    response, retryCount);
                                retryCount++;
                            }
                            else
                            {
                                requestSuccess = true;
                                logger?.LogDebug("Successfully created {Count} subscriptions in batch {BatchNumber}",
                                    counter, (i / maxTopicsPerRequest) + 1);
                            }

                            // Add a delay between batches to avoid rate limiting - larger delay if retrying
                            int delayMs = retryCount > 0 ? 1000 * (int)Math.Pow(2, retryCount) : 1000;
                            await Task.Delay(delayMs);
                        }
                        catch (Exception ex)
                        {
                            logger?.LogError(ex, "Exception creating subscriptions, batch {BatchNumber}, retry {RetryCount}",
                                (i / maxTopicsPerRequest) + 1, retryCount);
                            retryCount++;
                            
                            // Add exponential backoff on error
                            await Task.Delay(Math.Min(30000, 1000 * (int)Math.Pow(2, retryCount)));
                        }
                    }
                    
                    if (!requestSuccess)
                    {
                        success = false;
                        logger?.LogError("Failed to create subscriptions after {RetryLimit} attempts", retryLimit);
                    }
                }
            }

            return success;
        }

        /// <summary>
        /// Creates batched subscriptions for a list of queue IDs across one or more channels
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="jsonUtils">JsonUtils instance for making API calls</param>
        /// <param name="queueTable">DataTable containing queue IDs</param>
        /// <param name="channel">WebSocket channel to subscribe on</param>
        /// <param name="topicSuffix">Suffix for the topic (e.g., ".observations" or ".conversations")</param>
        /// <param name="idColumnName">Column name containing the IDs</param>
        /// <returns>True if subscriptions were created successfully</returns>
        public static async Task<bool> CreateQueueSubscriptionsAsync(
            ILogger logger,
            JsonUtils jsonUtils,
            DataTable queueTable,
            WebSocketDetail channel,
            string topicSuffix,
            string idColumnName = "id")
        {
            // Add similar null checks as above
            if (logger == null)
                throw new ArgumentNullException(nameof(logger), "Logger cannot be null");
                
            if (jsonUtils == null)
                throw new ArgumentNullException(nameof(jsonUtils), "JsonUtils cannot be null");
                
            if (queueTable == null)
                throw new ArgumentNullException(nameof(queueTable), "Queue table cannot be null");
                
            if (channel == null)
                throw new ArgumentNullException(nameof(channel), "Channel cannot be null");
                
            if (string.IsNullOrEmpty(topicSuffix))
                throw new ArgumentException("Topic suffix cannot be null or empty", nameof(topicSuffix));
                
            if (string.IsNullOrEmpty(idColumnName))
                idColumnName = "id"; // Default to "id" if not specified
                
            if (!queueTable.Columns.Contains(idColumnName))
                throw new ArgumentException($"Queue table does not contain column '{idColumnName}'", nameof(idColumnName));
                
            if (queueTable.Rows.Count == 0)
            {
                logger.LogWarning("Queue table is empty - no subscriptions to create");
                return false;
            }

            return await CreateUserSubscriptionsAsync(
                logger,
                jsonUtils,
                queueTable,
                channel,
                "v2.routing.queues.",
                topicSuffix,
                idColumnName);
        }
    }
}
