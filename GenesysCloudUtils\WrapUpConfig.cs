﻿using Newtonsoft.Json.Linq;
using System;
using System.Data;
using StandardUtils;
using System.Net;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    public class WrapUpConfig
    {
        // Instance properties
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger _logger;

        public WrapUpConfig(ILogger logger)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();
            Console.WriteLine("Initialization of GC Wrapup Config V2.00.00");
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
        }

        public DataTable GetWrapUpDataFromGC()
        {
            Console.WriteLine("Get WrapUp Data");

            DataTable WrapUps = DBUtil.CreateInMemTable("wrapupDetails");
            int CurrentPage = 1;
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            JsonActions.MaxPages = 1;

            while (CurrentPage <= JsonActions.MaxPages)
            {
                Console.Write("*");
                JArray json = JsonActions.JsonReturn(URI + "/api/v2/routing/wrapupcodes?pageSize=100&pageNumber=" + CurrentPage, GCApiKey);
                DataTable TempWrapUps = ConvWrapUps(json);

                if (TempWrapUps.Rows.Count > 0)
                {
                    foreach (DataRow dr in TempWrapUps.Rows)
                    {
                        WrapUps.ImportRow(dr);
                    }
                    TempWrapUps = null;
                    json = null;
                }
                CurrentPage += 1;
            }
            Console.WriteLine("\nTotal WrapUps:{0} ", WrapUps.Rows.Count);

            return WrapUps;
        }

        private DataTable createWrapUpsTable()
        {
            DataTable DtTemp = new DataTable("WrapUps");

            DtTemp.Columns.Add("id", typeof(String));
            DtTemp.Columns.Add("name", typeof(String));
            DtTemp.Columns.Add("updated", typeof(String));
            return DtTemp;
        }

        // Make this an instance method (not static) and use the instance logger
        public DataTable ConvWrapUps(Object json)
        {
            DataTable dtWU = new DataTable();
            dtWU.TableName = "WrapUpCodes";
            dtWU.Columns.Add("id", typeof(string));
            dtWU.Columns.Add("name", typeof(string));
            dtWU.Columns.Add("dateModified", typeof(DateTime));

            try
            {
                // Add null checking to avoid JsonReaderException
                if (json == null || string.IsNullOrEmpty(json.ToString()))
                {
                    _logger?.LogWarning("ConvWrapUps received null or empty JSON");
                    return dtWU;
                }

                string jsonStr = json.ToString();
                
                if (jsonStr.TrimStart().StartsWith("{"))
                {
                    // It's a JSON object, not an array
                    JObject jsonObj = JObject.Parse(jsonStr);
                    
                    // Check if it has an entities property that contains the array
                    if (jsonObj["entities"] != null && jsonObj["entities"].Type == JTokenType.Array)
                    {
                        // Process the entities array
                        foreach (var wu in jsonObj["entities"])
                        {
                            DataRow dr = dtWU.NewRow();
                            dr["id"] = wu["id"].ToString();
                            dr["name"] = wu["name"].ToString();
                            
                            // Add safe date parsing
                            if (wu["dateModified"] != null && DateTime.TryParse(wu["dateModified"].ToString(), out DateTime dtMod))
                            {
                                dr["dateModified"] = dtMod;
                            }
                            else
                            {
                                dr["dateModified"] = DateTime.UtcNow;
                            }
                            
                            dtWU.Rows.Add(dr);
                        }
                    }
                }
                else if (jsonStr.TrimStart().StartsWith("["))
                {
                    // It's a JSON array
                    JArray wrapUps = JArray.Parse(jsonStr);
                    
                    foreach (var wu in wrapUps)
                    {
                        DataRow dr = dtWU.NewRow();
                        dr["id"] = wu["id"].ToString();
                        dr["name"] = wu["name"].ToString();
                        
                        // Add safe date parsing
                        if (wu["dateModified"] != null && DateTime.TryParse(wu["dateModified"].ToString(), out DateTime dtMod))
                        {
                            dr["dateModified"] = dtMod;
                        }
                        else
                        {
                            dr["dateModified"] = DateTime.UtcNow;
                        }
                        
                        dtWU.Rows.Add(dr);
                    }
                }
                else
                {
                    // Not valid JSON - log information
                    _logger?.LogWarning("ConvWrapUps received invalid JSON: {0}",
                        jsonStr.Length > 100 ? jsonStr.Substring(0, 100) + "..." : jsonStr);
                }
            }
            catch (Exception ex)
            {
                // Log the exception but return empty table instead of throwing
                _logger?.LogError(ex, "Error in ConvWrapUps parsing JSON: {0}",
                    json?.ToString()?.Length > 100 ? json.ToString().Substring(0, 100) + "..." : json?.ToString() ?? "null");
            }
            
            return dtWU;
        }
    }
}
