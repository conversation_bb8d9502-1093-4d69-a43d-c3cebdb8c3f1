using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using GCRealTime.Services;
using Microsoft.Extensions.Logging;

namespace GCRealTime.Core
{
    /// <summary>
    /// Factory for creating realtime service instances
    /// </summary>
    public class RealtimeServiceFactory
    {
        private readonly ILogger _logger;
        private readonly DBUtils.DBUtils _dbAdapter;

        public RealtimeServiceFactory(ILogger logger, DBUtils.DBUtils dbAdapter)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dbAdapter = dbAdapter ?? throw new ArgumentNullException(nameof(dbAdapter));
        }

        /// <summary>
        /// Creates a specific service by service type name
        /// </summary>
        public IRealtimeService CreateService(string serviceType)
        {
            _logger?.LogInformation("Creating service of type {ServiceType}", serviceType);

            switch (serviceType.ToLowerInvariant())
            {
                case "userattendance":
                case "useradherence":
                    return (IRealtimeService)new UserAdherenceService(_logger, _dbAdapter);

                case "useractivity":
                    return (IRealtimeService)new UserActivityService(_logger, _dbAdapter);

                case "usercalldetails":
                    return (IRealtimeService)new UserCallDetailsService(_logger, _dbAdapter);

                case "usercallstats":
                    return (IRealtimeService)new UserCallStatsService(_logger, _dbAdapter);

                case "queuecalldetails":
                    return (IRealtimeService)new QueueCallDetailsService(_logger, _dbAdapter);

                case "queueobservation":
                    return (IRealtimeService)new QueueObservationService(_logger, _dbAdapter);

                default:
                    throw new ArgumentException($"Unknown service type: {serviceType}", nameof(serviceType));
            }
        }

        /// <summary>
        /// Dynamically discovers and creates all available IRealtimeService implementations
        /// </summary>
        public List<IRealtimeService> CreateAllServices(IEnumerable<string> excludeServices = null)
        {
            var services = new List<IRealtimeService>();
            var excludeList = excludeServices?.ToList() ?? new List<string>();

            try
            {
                _logger?.LogInformation("Discovering available real-time services");

                // Get all available service types
                var availableServices = new[]
                {
                    "UserActivity",
                    "UserCallStats",
                    "UserCallDetails",
                    "QueueCallDetails",
                    "UserAdherence",
                    "QueueObservation"
                };

                foreach (var serviceType in availableServices)
                {
                    if (excludeList.Contains(serviceType))
                    {
                        _logger?.LogInformation("Skipping excluded service: {ServiceType}", serviceType);
                        continue;
                    }

                    try
                    {
                        _logger?.LogInformation("Creating service: {ServiceType}", serviceType);
                        var service = CreateService(serviceType);
                        services.Add(service);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Failed to create service {ServiceType}", serviceType);
                    }
                }

                _logger?.LogInformation("Created {Count} real-time services", services.Count);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error discovering real-time services");
            }

            return services;
        }
    }
}
