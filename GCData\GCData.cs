﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Net;
using GenesysCloudUtils;
using Microsoft.Extensions.Logging;
using StandardUtils;
using System.Threading;
using System.Threading.Tasks;

namespace GCData
{
    public class GCGetData
    {
        public DateTime UserPresenceLastUpdate { get; set; }
        public DateTime UserInteractionLastUpdate { get; set; }
        public DateTime WFMScheduleLastUpdate { get; set; }
        public DateTime QueueInteractionLastUpdate { get; set; }
        public DateTime QueueUserAuditLastUpdate { get; set; }
        public DateTime AdherenceLastUpdate { get; set; }
        public DateTime AdherenceFromDate { get; set; }
        public DateTime DetailInteractionLastUpdate { get; set; }
        public DateTime DetailEvaluationLastUpdate { get; set; }
        public DateTime DetailChatLastUpdate { get; set; }
        public DateTime ShrinkageLastUpdate { get; set; }
        public DateTime LastUpdate { get; set; }
        public DateTime DateToSyncFrom { get; set; }
        public TimeSpan MaxSpanToSync { get; set; }
        public TimeSpan LookBackSpan { get; set; }
        public string TimeZoneConfig { get; set; }
        public string AggInterval { get; set; }
        public string UserAggViews { get; set; }
        public string QueueAggViews { get; set; }

        const int WFMDaysMaxProcess = 20;
        DBUtils.DBUtils DBConnector = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public GCGetData()
        {
        }

        public GCGetData(ILogger logger)
        {
            _logger = logger;
        }

        public void Initialize(string SyncType)
        {
            try
            {
                DBConnector.Initialize();
            }
            catch (Exception dbEx)
            {
                _logger?.LogError(dbEx, "Error initializing DBConnector");
                throw; // Re-throw to prevent further execution
            }
            DataTable jobMinimumDefinition = DBConnector.GetSQLTableData("select * from jobminimumdefinition", "jobminimumdefinition");

            if (!string.IsNullOrEmpty(SyncType))
                DateToSyncFrom = DBConnector.GetSyncLastUpdate(SyncType);

            Utils UCAUtils = new Utils();
            DataTable ClientFeatures = null;
            try
            {
                ClientFeatures = UCAUtils.GetGCCustomerConfig();
            }
            catch (Exception configEx)
            {
                _logger?.LogError(configEx, "Error getting customer config");
                // Consider whether to throw or continue with default values
            }

            string jobName = CSG.Adapter.Compatability.LegacyOptions.GetOption("Job");


            DataView dataView = new DataView(jobMinimumDefinition)
            {
                RowFilter = $"JobName = '{jobName}'"
            };
            DataTable filteredJobMinimumDefinition = dataView.ToTable();

            DataRow row = filteredJobMinimumDefinition.Rows[0];
            string minMaxSyncSpan = row["MaxSyncSpan"].ToString();
            string minLookBackSpan = row["LookBackSpan"].ToString();
            TimeSpan parsedMinimumMaxSyncSpan = TimeSpan.FromDays(1);
            if (!TimeSpan.TryParse(
                minMaxSyncSpan,
                out parsedMinimumMaxSyncSpan))
            {
                parsedMinimumMaxSyncSpan = TimeSpan.FromDays(1);
            }

            TimeSpan parsedMinimumLookBackSpan = TimeSpan.FromDays(1);
            if (!TimeSpan.TryParse(
                minLookBackSpan,
                out parsedMinimumLookBackSpan))
            {
                parsedMinimumLookBackSpan = TimeSpan.FromDays(1);
            }

            TimeSpan parsedMaxSyncSpan = TimeSpan.FromDays(1);
            if (!TimeSpan.TryParse(
                CSG.Adapter.Compatability.LegacyOptions.GetOption("MaxSyncSpan"),
                out parsedMaxSyncSpan))
            {
                parsedMaxSyncSpan = parsedMinimumMaxSyncSpan;
            }
            if(parsedMaxSyncSpan < parsedMinimumMaxSyncSpan)
            {
                MaxSpanToSync = parsedMinimumMaxSyncSpan;
            }
            else
            {
                MaxSpanToSync = parsedMaxSyncSpan;
            }
            TimeSpan parsedLookBack = TimeSpan.FromDays(1);
            if (!TimeSpan.TryParse(
                CSG.Adapter.Compatability.LegacyOptions.GetOption("LookBackSpan"),
                out parsedLookBack))
            {
                parsedLookBack = parsedMinimumLookBackSpan;
            }
            if (parsedLookBack < parsedMinimumLookBackSpan)
            {
                LookBackSpan = parsedMinimumLookBackSpan;
            }
            else
            {
                LookBackSpan = parsedLookBack;
            }
            
            TimeZoneConfig = Convert.ToString(ClientFeatures.Rows[0]["datetimezone"]);
            AggInterval = Convert.ToString(ClientFeatures.Rows[0]["Interval"]);
            UserAggViews = Convert.ToString(ClientFeatures.Rows[0]["useraggviews"]);
            QueueAggViews = Convert.ToString(ClientFeatures.Rows[0]["queueaggviews"]);
        }

        public DataTable ActiveQMembersData()
        {



            DataTable QueueDetails = DBConnector.GetSQLTableData("select * from queuedetails", "queuedetails");


            BUData BusUnitData = new BUData();
            BusUnitData.TimeZoneConfig = TimeZoneConfig;

            BusUnitData.Initialize();

            DataTable ActiveQMembers = BusUnitData.ActiveQMembers(QueueDetails);

            return ActiveQMembers;

        }

        public DataTable HeadcountForecastData()
        {


            DataTable ScheduleDetails = DBConnector.GetSQLTableData("select * from scheduleDetails", "scheduleDetails");


            BUData BusUnitData = new BUData();
            BusUnitData.TimeZoneConfig = TimeZoneConfig;

            BusUnitData.Initialize();

            DataTable HeadcountForecast = BusUnitData.HeadCountForecast(ScheduleDetails);

            return HeadcountForecast;

        }

        public DataTable OfferedForecastData(string StartDate)
        {
            DataTable BusinessUnitDetails = DBConnector.GetSQLTableData("select * from buDetails", "buDetails");

            BUData BusUnitData = new BUData();
            BusUnitData.TimeZoneConfig = TimeZoneConfig;

            BusUnitData.Initialize();

            DataTable HeadcountForecast = BusUnitData.OfferedForecast(BusinessUnitDetails, StartDate);

            return HeadcountForecast;

        }

        public DataTable WFMScheduleDetails()
        {
            BUData GCWFMScheduleDetails = new BUData();

            GCWFMScheduleDetails.TimeZoneConfig = TimeZoneConfig;
            GCWFMScheduleDetails.Initialize();

            string StartDate = DateToSyncFrom.ToString("yyyy-MM-ddTHH:00:00.000Z");

            DataTable ScheduleDetails = GCWFMScheduleDetails.GetScheduleDetailsFromCC(StartDate);

            return ScheduleDetails;
        }

        public DataTable SystemCallUsageData(int DayOffset)
        {
            adminData GCSystemCallUsageData = new adminData(_logger);

            GCSystemCallUsageData.TimeZoneConfig = TimeZoneConfig;
            GCSystemCallUsageData.Initialize();

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable OauthUsageData = GCSystemCallUsageData.GetSystemCallUsage(DayOffset);

            Console.WriteLine("Rows Returned: {0}", OauthUsageData.Rows.Count);

            return OauthUsageData;
        }

        public DataTable HoursBlockData(int MonthOffset)
        {
            UserData GCHoursBlockData = new UserData();

            GCHoursBlockData.TimeZoneConfig = TimeZoneConfig;
            GCHoursBlockData.Initialize();

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable HoursBlockData = GCHoursBlockData.GetHoursBlockData(MonthOffset);

            Console.WriteLine("Rows Returned: {0}", HoursBlockData.Rows.Count);

            return HoursBlockData;
        }

        public DataTable OauthUsageData(int MonthOffset)
        {
            adminData GCOauthUsageData = new adminData(_logger);

            GCOauthUsageData.TimeZoneConfig = TimeZoneConfig;
            GCOauthUsageData.Initialize();

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable OauthUsageData = GCOauthUsageData.GetOauthUsage(MonthOffset);

            Console.WriteLine("Rows Returned: {0}", OauthUsageData.Rows.Count);

            return OauthUsageData;
        }

        public DataTable SubHoursData()
        {
            adminData GCSubscriptionData = new adminData(_logger);

            GCSubscriptionData.TimeZoneConfig = TimeZoneConfig;
            GCSubscriptionData.Initialize();

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable SubHoursData = GCSubscriptionData.GetSubUserUsage(DateToSyncFrom);

            Console.WriteLine("Rows Returned: {0}", SubHoursData.Rows.Count);

            return SubHoursData;
        }

        public DataTable SubOverviewData()
        {
            adminData GCSubscriptionData = new adminData(_logger);

            GCSubscriptionData.TimeZoneConfig = TimeZoneConfig;
            GCSubscriptionData.Initialize();

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable SubscriptionData = GCSubscriptionData.GetSubscriptionOverViewDatafromGC();

            return SubscriptionData;
        }

        public DataTable UserPresenceData(DataTable Users)
        {
            UserData GCUserPresenceData = new UserData();

            GCUserPresenceData.TimeZoneConfig = TimeZoneConfig;
            GCUserPresenceData.AggInterval = AggInterval;
            GCUserPresenceData.Initialize();

            DataTable Presences = DBConnector.GetSQLTableData("select * from presenceDetails", "presenceDetails");

            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");

            GCUserPresenceData.UserPresenceLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            string EndDate = DateToSyncFrom.Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

            DataTable UserPresenceData = GCUserPresenceData.GetUserPresenceDataFromGC(Users, Presences, StartDate, EndDate);

            return UserPresenceData;
        }

        public DataTable TimeOffReqData()
        {
            BUData GCTimeOffRequestData = new BUData();


            GCTimeOffRequestData.TimeZoneConfig = TimeZoneConfig;
            GCTimeOffRequestData.Initialize();

            DataTable TimeOffRequestData = GCTimeOffRequestData.GetTimeOffDataFromGC();

            return TimeOffRequestData;
        }

        public DataTable UserPresenceDetailedData()
        {
            UserData GCUserPresenceDetailedData = new UserData();

            GCUserPresenceDetailedData.TimeZoneConfig = TimeZoneConfig;
            GCUserPresenceDetailedData.Initialize();

            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00");

            GCUserPresenceDetailedData.UserPresenceLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss", null).ToUniversalTime();

            //string EndDate = DateTime.ParseExact(DateToSyncFrom, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime().AddDays(MaxDaysToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);

            if (DTEndDate > DateTime.UtcNow)
            {
                Console.WriteLine("Before : DTDate:{0} Now:{1}", DTEndDate, DateTime.UtcNow);
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
                Console.WriteLine("After  : DTDate:{0} Now:{1}", DTEndDate, DateTime.UtcNow);
            }

            String EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:00");

            DataTable UserPresenceData = GCUserPresenceDetailedData.GetUserDetailedPresenceFromGC(StartDate, EndDate);

            if (UserPresenceData != null)
            {

                if (GCUserPresenceDetailedData.UserPresenceLastUpdate > UserPresenceLastUpdate)
                    UserPresenceLastUpdate = GCUserPresenceDetailedData.UserPresenceLastUpdate;
            }

            UserPresenceLastUpdate = DTEndDate;

            return UserPresenceData;
        }
        public DataSet AdherenceData()
        {
            BUData GCAdherenceData = new BUData();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            GCAdherenceData.TimeZoneConfig = TimeZoneConfig;
            GCAdherenceData.Initialize();

            DateTime FromDate = DateToSyncFrom;
            Console.WriteLine("Initial Date to Sync From:       {0}", FromDate);

            //Console.ReadKey();
            double TotalDays = MaxSpanToSync.TotalDays;
            TotalDays = TotalDays > 31 ? 31 : TotalDays;
            double TotalLookBackDays = LookBackSpan.TotalDays;
            DateTime StartDateDT = FromDate.AddDays(-TotalLookBackDays).Date;
            DateTime EndDateDT = FromDate.AddDays(TotalDays).Date;

            DateTime UTCFromDate = DateTime.SpecifyKind(StartDateDT, DateTimeKind.Utc);

            double Minutes = AppTimeZone.GetUtcOffset(UTCFromDate).TotalMinutes;
            int RemoveHrs = (int) (Minutes/60);

            Console.WriteLine("Lookback Span: {0}", TotalLookBackDays);
            Console.WriteLine("Initial Querying Start Date   :{0}", StartDateDT);      

            StartDateDT = StartDateDT.AddHours(-RemoveHrs);
            EndDateDT = EndDateDT.AddHours(-RemoveHrs);

            Console.WriteLine("Remove Hours :{0}", RemoveHrs);
            Console.WriteLine("Converted Querying Start Date   :{0}", StartDateDT);

            bool isDstOld = AppTimeZone.IsDaylightSavingTime(StartDateDT);
            DateTime EndDateDTOld = EndDateDT;
            bool dstChange = false;

            for (var dt = StartDateDT; dt <= EndDateDT; dt = dt.AddDays(1))
            {
                // Check for DST change on each date
                bool isDstNew = AppTimeZone.IsDaylightSavingTime(dt);
                // Console.WriteLine($"{dt:dd/MM/yyyy HH:mm:ss} - DST: {isDstNew}");
                EndDateDTOld = dt;

                if(isDstNew != isDstOld)
                {
                    Console.WriteLine($"DST condition for Date {dt:dd/MM/yyyy HH:mm:ss} has changed.");
                    dstChange = true;
                    // EndDateDTOld = dt;
                    break;
                }

                isDstOld =isDstNew;
                // EndDateDTOld = dt;
            }

            string StartDate = StartDateDT.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            string EndDate = EndDateDTOld.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");

            if (StartDate == EndDate)
            {
                StartDate = StartDateDT.AddDays(-1).ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                EndDate = EndDateDT.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            }

            if (DateTime.Parse(EndDate) > DateTime.UtcNow)
            {
                EndDate = DateTime.UtcNow.AddMinutes(-10).ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            }
            Console.WriteLine("Final Querying Start Date   :{0}", StartDate);
            Console.WriteLine("Final Querying End Date     :{0}", EndDate);

            GCAdherenceData.AdherenceLastUpdate = FromDate.ToUniversalTime();

            DataSet AdherenceData = GCAdherenceData.GetAdherenceDataFromGC(StartDate, EndDate, dstChange);

            if (GCAdherenceData.Errors == true)
            {
                return null;
            }

            if (AdherenceData != null)
            {
                Console.WriteLine("Last Update Returned : {0}", GCAdherenceData.AdherenceLastUpdate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
                if (GCAdherenceData.AdherenceLastUpdate > AdherenceLastUpdate)
                {
                    if(dstChange)
                    {
                        AdherenceLastUpdate = DateTime.Parse(EndDate).AddDays(TotalLookBackDays);
                    }
                    else
                    {
                        AdherenceLastUpdate = DateTime.Parse(EndDate);
                    }
                }
            }

            // AdherenceLastUpdate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fff", null).AddHours(RemoveHrs * -1);

            return AdherenceData;
        }
        public DataSet WFMScheduleData()
        {
            BUData GCWFMScheduleData = new BUData();

            GCWFMScheduleData.TimeZoneConfig = TimeZoneConfig;
            GCWFMScheduleData.Initialize();

            string StartDate = DateToSyncFrom.ToUniversalTime().ToString("yyyy-MM-ddTHH:00:00.000Z");
            Console.WriteLine("Date:{0}", StartDate);
            
            string  EndDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");
            DateTime CurrentDate = DateTime.UtcNow;
            DateTime CompareDate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            DataSet DSTemp = new DataSet();
            if (CompareDate > CurrentDate)
            {
                Console.WriteLine("\nIn the Future. Need to Back Date.");
                StartDate = CurrentDate.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            }


            if ((DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime() - DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime()).TotalDays >= 30)
            {
                Console.WriteLine("\nOver 30 Days to Sync: Blocking Out");

                foreach (DateTime ProcessDay in EachDay(DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime(), DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime()))
                {
                    DateTime ProcessTo = ProcessDay.AddDays(WFMDaysMaxProcess).ToUniversalTime();

                    if (ProcessDay > CompareDate)
                        break;

                    DSTemp.Merge(GCWFMScheduleData.GetScheduleDataFromGC(ProcessDay.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), ProcessTo.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")));

                    Console.WriteLine("\nProcessing : {0} to {1} Compare {2}", ProcessDay.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), ProcessTo.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), CompareDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
                }
            }
            else
            {
                DSTemp = GCWFMScheduleData.GetScheduleDataFromGC(StartDate, EndDate);
            }


            WFMScheduleLastUpdate = GCWFMScheduleData.WFMScheduleLastUpdate;

            return DSTemp;
        }
        public DataTable UserInteractionData()
        {
            UserData GCUserInteractionData = new UserData();

            GCUserInteractionData.TimeZoneConfig = TimeZoneConfig;
            GCUserInteractionData.AggInterval = AggInterval;

            GCUserInteractionData.Initialize();

            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            string EndDate = DateToSyncFrom.Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

            GCUserInteractionData.UserInteractionLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            DataTable UserInteractionData = GCUserInteractionData.GetUserInteractionDataFromGC(StartDate, EndDate, UserAggViews);      

            UserInteractionLastUpdate = DateToSyncFrom.Add(MaxSpanToSync);
            
            return UserInteractionData;
        }
        public DataTable QueueInteractionData()
        {
            QueueData GCQueueInteractionData = new QueueData();

            GCQueueInteractionData.AggInterval = AggInterval;
            GCQueueInteractionData.TimeZoneConfig = TimeZoneConfig;
            GCQueueInteractionData.Initialize();

            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            string EndDate = DateToSyncFrom.Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

            GCQueueInteractionData.QueueInteractionLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            DataTable QueueInteractionData = GCQueueInteractionData.GetQueueInteractionDataFromGC(StartDate, EndDate, QueueAggViews);        
            
            QueueInteractionLastUpdate = DateToSyncFrom.Add(MaxSpanToSync);
            
            return QueueInteractionData;
        }
        public DataTable QueueAuditData()
        {

            Console.WriteLine("Queue User Membership Audit");
            QueueData GCQueueAuditData = new QueueData();

            GCQueueAuditData.TimeZoneConfig = TimeZoneConfig;
            GCQueueAuditData.Initialize();

            string StartDate = DateToSyncFrom.ToUniversalTime().Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            string EndDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

            GCQueueAuditData.QueueInteractionLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            DataTable QueueQueueUserAuditData = GCQueueAuditData.GetQueueAuditData(StartDate, EndDate);


            if (QueueQueueUserAuditData != null)
            {
                Console.WriteLine("Last Update Returned : {0}", EndDate);

                QueueUserAuditLastUpdate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

                if (QueueUserAuditLastUpdate > DateTime.UtcNow)
                    QueueUserAuditLastUpdate = DateTime.UtcNow.AddHours(-2);
            }
            return QueueQueueUserAuditData;
        }

        public DataTable ShrinkageData()
        {

            Console.WriteLine("Shrinkage Data");
            ShrinkageData GCShrinkageData = new ShrinkageData();

            GCShrinkageData.TimeZoneConfig = TimeZoneConfig;
            GCShrinkageData.Initialize();

            string StartDate = DateToSyncFrom.ToUniversalTime().Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");

            string EndDate = "";

            if (DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync) > DateTime.UtcNow)
            {
                EndDate = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:00:00.000Z");
            }
            else
            {
                EndDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");
            }

            GCShrinkageData.ShrinkageLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            DataTable ShrinkageData = GCShrinkageData.GetShrinkageData(StartDate, EndDate);

            if (ShrinkageData != null)
            {
                ShrinkageLastUpdate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync);
            }

            return ShrinkageData;
        }

        public DataTable WFMAuditData()
        {

            Console.WriteLine("WFM Changes Audit");
            WFMAuditData GCWFMAuditData = new WFMAuditData();

            GCWFMAuditData.TimeZoneConfig = TimeZoneConfig;
            GCWFMAuditData.Initialize();


            string StartDate = DateToSyncFrom.ToUniversalTime().Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            string EndDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

            GCWFMAuditData.WFMAuditLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            DataTable WFMAuditData = GCWFMAuditData.GetWFMAuditData(StartDate, EndDate);


            if (WFMAuditData != null)
            {
                Console.WriteLine("Last Update Returned : {0}", EndDate);

                QueueUserAuditLastUpdate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

                if (QueueUserAuditLastUpdate > DateTime.UtcNow)
                    QueueUserAuditLastUpdate = DateTime.UtcNow.AddHours(-2);
            }
            return WFMAuditData;
        }
        public async Task<DataSet> DetailInteractionData(CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames)
        {
            DataSet DetailInteractionData = new DataSet();
            DetailData GCDetailInteractionData = new DetailData(_logger, renameParticipantAttributeNames);

            GCDetailInteractionData.TimeZoneConfig = TimeZoneConfig;

            // TODO: Be smarter here. If there are any conversations that have not ended don't update the sync time past
            // the start of the in-progress conversation. Then this lookback window will not be required.
            TimeSpan adjustedLookBackSpan = Math.Abs(LookBackSpan.TotalHours) == 2 ? TimeSpan.FromHours(4) : LookBackSpan;

            string StartDate = DateToSyncFrom.ToUniversalTime().Subtract(adjustedLookBackSpan).ToString("yyyy-MM-ddTHH:mm:00.000Z");
            DateTime DTEndDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                Console.WriteLine("Before: DTDate:{0} Now:{1}", DTEndDate, DateTime.UtcNow);
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
                Console.WriteLine("After: DTDate:{0} Now:{1}", DTEndDate, DateTime.UtcNow);
            }

            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:00.000Z");

            GCDetailInteractionData.DetailInteractionLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            DateTime FromDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync);
            DateTime ToDate = DateTime.UtcNow.ToUniversalTime();

            TimeSpan DateDiff = ToDate - FromDate;
            double MinutesDifference = DateDiff.TotalMinutes;

            Console.WriteLine("\nLast Update Returned : {0}", GCDetailInteractionData.DetailInteractionLastUpdate);
            //Console.ReadLine();

            Console.WriteLine("Syncing Span: {0}\nBetween {1} ... {2} \nCompare Date {3} ... {4}",
                DateDiff,
                StartDate,
                DTEndDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"),
                FromDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"),
                ToDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"));
            //Console.ReadLine();
            if (MinutesDifference < 1440)
            {
                Console.WriteLine("Detailed Interaction Sync: Using Query");
                DetailInteractionData = GCDetailInteractionData.GetDetailInteractionDataFromGC("QUERY", StartDate, EndDate);
            }
            else
            {
                Console.WriteLine("Detailed Interaction Sync: Using Job");
                DetailInteractionData = GCDetailInteractionData.GetDetailInteractionDataFromGC("JOB", StartDate, EndDate);
            }



            if (DetailInteractionData != null)
            {

                if (DTEndDate > DateTime.UtcNow)
                {
                    DetailInteractionLastUpdate = DateTime.UtcNow;
                }
                else
                {
                    DetailInteractionLastUpdate = DTEndDate;
                }

                Console.WriteLine("\nLast Update Returned : {0} - Before Writing Data", GCDetailInteractionData.DetailInteractionLastUpdate);
                //Console.ReadLine();

            }
            return DetailInteractionData;
        }

        public DataSet VoiceAnalysisData(DataTable Conversations)
        {
            VoiceAnalysis VoiceOverView = new VoiceAnalysis();
            DataSet VoiceAnalysisData = new DataSet();

            VoiceOverView.TimeZoneConfig = TimeZoneConfig;
            VoiceOverView.Initialize();

            DataTable VoiceOverViewTable = VoiceOverView.GetVoiceOverViewData(ref Conversations);

            DataSet TempVoiceDetail = VoiceOverView.GetVoiceDetailData(ref VoiceOverViewTable);

            VoiceAnalysisData.Tables.Add(VoiceOverViewTable);
            VoiceAnalysisData.Tables.Add(TempVoiceDetail.Tables[0].Copy());
            VoiceAnalysisData.Tables.Add(TempVoiceDetail.Tables[1].Copy());

            return VoiceAnalysisData;
        }

        public DataSet EvaluationDetailedData()
        {
            DataSet EvaluationDetailsSet = new DataSet();

            EvalData GCEvaluationData = new EvalData();

            GCEvaluationData.TimeZoneConfig = TimeZoneConfig;

            GCEvaluationData.Initialize();

            string StartDate = DateToSyncFrom.ToUniversalTime().Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            
            string EndDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

            Console.WriteLine("Date to Sync From: {0}", DateToSyncFrom.ToUniversalTime().ToString("yyyy-MM-ddTHH:00:00.000Z"));
            Console.WriteLine("LookBackSpan for Evaluation: {0}", LookBackSpan);
            Console.WriteLine("MaxSyncSpan for Evaluation: {0}", MaxSpanToSync);
            Console.WriteLine("Start Date and End Date for Evaluation: {0}, {1}", StartDate, EndDate);

            GCEvaluationData.DetailEvaluationLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            EvaluationDetailsSet = GCEvaluationData.GetEvalDetailsFromGC(StartDate, EndDate);

            // if (EvaluationDetailsSet != null)
            // {
            //     if (EvaluationDetailsSet.Tables.Contains("evaldata"))
            //     {
            //         DataTable evaldata = EvaluationDetailsSet.Tables["evaldata"];
            //         Console.WriteLine("Last Update Returned : {0}", GCEvaluationData.DetailEvaluationLastUpdate);
            //         var assignedDateQuery = evaldata.AsEnumerable()
            //                             .Select(row => row.Field<DateTime>("assigneddate"));
               
            //         DateTime maxAssignedDate;
            //         if (assignedDateQuery.Any())
            //         {
            //             maxAssignedDate = assignedDateQuery.Max();
            //         }
            //         else
            //         {
            //             maxAssignedDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync).AddHours(-2);
            //         }
            //      DetailEvaluationLastUpdate = maxAssignedDate;
            //     }         
            // }
            // else
            // {
            //     DetailEvaluationLastUpdate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync).AddHours(-2);
            // }

            DetailEvaluationLastUpdate = DateToSyncFrom.Add(MaxSpanToSync);

            Console.WriteLine("Evaluation Last Update NEW: {0}", DetailEvaluationLastUpdate);

            return EvaluationDetailsSet;
        }

        public DataSet EvalDataCatchUp(DataTable PendingEvals)
        {
            DataSet EvaluationDetailsSet = new DataSet();

            EvalData GCEvaluationData = new EvalData();

            GCEvaluationData.TimeZoneConfig = TimeZoneConfig;

            GCEvaluationData.Initialize();
            string StartDate = DateToSyncFrom.ToUniversalTime().Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            
            string EndDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");


            EvaluationDetailsSet = GCEvaluationData.GetEvaluationDetailsFromGC(PendingEvals, StartDate, EndDate);

            return EvaluationDetailsSet;
        }

        public DataTable ChatData(CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames)
        {
            ChatData GCChatData = new ChatData(_logger);
            DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

            DBUtil.Initialize();

            GCChatData.TimeZoneConfig = TimeZoneConfig;

            string StartDate = DateToSyncFrom.ToUniversalTime().Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            string EndDate = DateToSyncFrom.ToUniversalTime().Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

            GCChatData.DetailChatLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            DataTable UserConfig = DBUtil.GetSQLTableData("Select * from userDetails", "userDetails");

            DataTable DetailChatData = GCChatData.CalculateChatData(
                StartDate, EndDate, UserConfig, renameParticipantAttributeNames, ForSQL: true);

            if (DetailChatData != null)
            {
                Console.WriteLine("Last Update Returned : {0}", GCChatData.DetailChatLastUpdate);

                if (GCChatData.DetailChatLastUpdate > DetailChatLastUpdate)
                    DetailChatLastUpdate = GCChatData.DetailChatLastUpdate;
            }

            return DetailChatData;
        }

        public DataTable UserDetailsFromDB()
        {
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DataTable DTUser = new DataTable();
            try
            {
                DTUser = DBAdapter.GetSQLTableData("select * from userRealTimeData", "userRealTimeData");
            }
            catch (Exception ex)
            {
                if (_logger != null)
                    _logger?.LogWarning(ex, "Suppressed error");
                else
                    Console.WriteLine(ex.ToString());
            }
            finally
            {
                DBAdapter = null;
            }

            return DTUser;
        }

        public DataTable LearningAssignmentResultsData()
        {
           DataTable LearningModuleAssignments = DBConnector.GetSQLTableData("select * from learningmoduleassignments", "learningmoduleassignments");

            LearningDataConfig LearningData = new LearningDataConfig();

            LearningData.Initialize();

            DataTable LearningAssignmentResultsData = LearningData.GetLearningAssignmentResultsFromGC(LearningModuleAssignments);

            return LearningAssignmentResultsData;
        }

        public DataTable KnowledgeBaseDocumentData()
        {
            DataTable KnowledgeBaseDetails = DBConnector.GetSQLTableData("select * from knowledgeBase", "knowledgeBase");

            KnowledgeBaseConfig KnowledgeBase = new KnowledgeBaseConfig();

            KnowledgeBase.Initialize();

            DataTable KnowledgeBaseDocumentData = KnowledgeBase.GetKnowledgeBaseDocumentDataFromGC(KnowledgeBaseDetails);

            return KnowledgeBaseDocumentData;
        }


#nullable enable
        public bool UpdateLastSuccessDate(DateTime LastSuccessDate, string Key)
        {
            bool Successful = false;

            Successful = DBConnector.SetSyncLastUpdate(LastSuccessDate, Key);

            return Successful;
        }
#nullable restore

        public IEnumerable<DateTime> EachDay(DateTime from, DateTime to)
        {
            for (var day = from.Date; day.Date <= to.Date; day = day.AddDays(WFMDaysMaxProcess))
                yield return day;
        }

        private bool ToBool(string s)
        {
            return s == "0" ? false : true;
        }
    }
}
