using System;
using Microsoft.Extensions.DependencyInjection;

namespace GCRealTime.Services
{
    /// <summary>
    /// Extension methods for IServiceCollection to register realtime services
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Registers all the specified realtime service types with the service collection.
        /// Renamed from AddRealtimeServices to RegisterRealtimeServices to avoid ambiguity.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="serviceTypes">Array of realtime service types to register</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection RegisterRealtimeServices(this IServiceCollection services, Type[] serviceTypes)
        {
            foreach (var serviceType in serviceTypes)
            {
                services.AddTransient(typeof(IRealtimeService), serviceType);
            }
            return services;
        }
    }
}
