using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using DBUtils;
using StandardUtils;
using Newtonsoft.Json;
using GenesysCloudUtils;
using GenesysCloudUtils.WebSocket;

namespace GCRealTime.Core
{
    public abstract class BaseRealTimeService : IDisposable
    {
        protected readonly ILogger _logger;
        protected readonly JsonUtils _jsonUtils; // Replace IAuthenticationService with JsonUtils
        protected TimeZoneInfo _timeZone;
        protected string _timeZoneConfig;
        // Change to auto-property with public get and internal set
        public DBUtils.DBUtils DBAdapter { get; internal set; }
        protected List<GenesysCloudUtils.WebSocket.IWebSocketManager> WebSocketManagers = new List<GenesysCloudUtils.WebSocket.IWebSocketManager>();
        protected Dictionary<string, WebSocketDetail> WebSocketDetails { get; private set; } = new Dictionary<string, WebSocketDetail>();
        protected List<Thread> WebSocketThreads = new List<Thread>();
        protected int TotalErrors = 0;
        protected const int MAX_TOPICS_PER_SUBSCRIPTION = 999;
        private bool _disposedValue;
        protected bool _isRunning = false;
        protected System.Timers.Timer _databaseWriteTimer;
        protected bool _pendingDatabaseWrite = false;
        protected int _failedChannelAttempts = 0;
        protected const int MAX_CHANNEL_FAILURES = 3;
        
        // Add counters for monitoring
        private long _processedEventsCount = 0;
        private long _errorCount = 0;
        private DateTime _lastProcessedEventTime = DateTime.MinValue;
        private DateTime _serviceStartTime;
        
        // Expose these as public properties for the monitoring system
        public long ProcessedEventsCount => _processedEventsCount;
        public long ErrorCount => _errorCount;
        public DateTime LastProcessedEventTime => _lastProcessedEventTime;
        public TimeSpan Uptime => DateTime.UtcNow - _serviceStartTime;
        
        // Class to track activity statistics for each entity (user/queue)
        public class EntityActivityStats
        {
            public string EntityId { get; set; }
            public string EntityName { get; set; }
            public string EntityType { get; set; } // "User" or "Queue"
            public long EventsProcessed { get; set; }
            public DateTime LastActivity { get; set; }
            
            public EntityActivityStats(string id, string name, string type)
            {
                EntityId = id;
                EntityName = name;
                EntityType = type;
                EventsProcessed = 0;
                LastActivity = DateTime.UtcNow;
            }
        }
        
        // Add entity-level tracking for detailed statistics
        protected readonly Dictionary<string, EntityActivityStats> _entityStats = new Dictionary<string, EntityActivityStats>();
        protected readonly object _entityStatsLock = new object();
        
        public BaseRealTimeService(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _jsonUtils = new JsonUtils(logger); // Create JsonUtils directly
            
            // Default to UTC instead of empty string to avoid TimeZoneNotFoundException
            _timeZoneConfig = "UTC";
            _timeZone = TimeZoneInfo.Utc; // Default to UTC time zone
            
            try 
            {
                // Try to get timezone from options with null safety
                string configTimeZone = null;
                try
                {
                    configTimeZone = CSG.Adapter.Compatability.LegacyOptions.GetOption("DateTimeZone");
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Error retrieving DateTimeZone from LegacyOptions, using UTC instead");
                }

                if (!string.IsNullOrEmpty(configTimeZone))
                {
                    _logger?.LogInformation("Using timezone from configuration: {TimeZone}", configTimeZone);
                    _timeZoneConfig = configTimeZone;
                    
                    // Only try to find the TimeZoneInfo if we have a non-empty ID
                    if (configTimeZone.Contains("/"))
                    {
                        // IANA timezone format, map to Windows format
                        string windowsTimeZone = MapIanaToWindowsTimeZone(configTimeZone);
                        _timeZone = TimeZoneInfo.FindSystemTimeZoneById(windowsTimeZone);
                        _logger?.LogInformation("Mapped IANA timezone '{0}' to Windows timezone '{1}'", 
                            configTimeZone, windowsTimeZone);
                    }
                    else
                    {
                        // Direct Windows timezone format
                        _timeZone = TimeZoneInfo.FindSystemTimeZoneById(configTimeZone);
                    }
                    
                    _logger?.LogInformation("Set timezone to {TimeZone}", _timeZoneConfig);
                }
            }
            catch (Exception ex)
            {
                // If any error occurs during timezone setup, log it and default to UTC
                _logger?.LogWarning(ex, "Error setting timezone from configuration, using UTC instead");
                _timeZoneConfig = "UTC";
                _timeZone = TimeZoneInfo.Utc;
            }
        }
        
        protected BaseRealTimeService(ILogger logger, DBUtils.DBUtils dbAdapter) : this(logger)
        {
            DBAdapter = dbAdapter ?? throw new ArgumentNullException(nameof(dbAdapter));
        }
        
        public virtual void Initialize()
        {
            if (DBAdapter == null)
            {
                _logger?.LogError("[Base][Init] DBAdapter is not initialized.");
                throw new InvalidOperationException("DBAdapter must be initialized before calling Initialize.");
            }

            try
            {
                DBAdapter.Initialize();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Init] Error initializing database adapter");
                throw;
            }

            // Get timezone from configuration sources
            try
            {
                // Get timezone from legacy options system - the primary source
                string tzConfig = null;
                try
                {
                    tzConfig = CSG.Adapter.Compatability.LegacyOptions.GetOption("DateTimeZone");
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "[Base][Init] Error retrieving DateTimeZone from LegacyOptions, using UTC instead");
                }
                
                if (!string.IsNullOrEmpty(tzConfig))
                {
                    _logger?.LogInformation("[Base][Init] Using timezone from configuration: {TimeZone}", tzConfig);
                    _timeZoneConfig = tzConfig;
                    
                    try
                    {
                        // If IANA format (contains '/'), try to map it to Windows format
                        if (tzConfig.Contains("/"))
                        {
                            string windowsTimeZone = MapIanaToWindowsTimeZone(tzConfig);
                            _logger?.LogInformation("Mapped IANA timezone '{0}' to Windows timezone '{1}'", 
                                tzConfig, windowsTimeZone);
                            _timeZone = TimeZoneInfo.FindSystemTimeZoneById(windowsTimeZone);
                        }
                        else
                        {
                            // Try direct lookup
                            _timeZone = TimeZoneInfo.FindSystemTimeZoneById(tzConfig);
                        }
                        
                        _logger?.LogInformation("[Base][Init] Service initialized with timezone: {TimeZone}", _timeZoneConfig);
                    }
                    catch (Exception tzEx)
                    {
                        _logger?.LogError(tzEx, "[Base][Init] Error setting timezone from '{TimeZone}', using UTC", tzConfig);
                        _timeZone = TimeZoneInfo.Utc;
                        _timeZoneConfig = "UTC";
                    }
                }
                else
                {
                    _logger?.LogWarning("[Base][Init] No timezone configuration found, using UTC");
                    _timeZoneConfig = "UTC";
                    _timeZone = TimeZoneInfo.Utc;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Init] Error setting timezone, using UTC");
                _timeZoneConfig = "UTC";
                _timeZone = TimeZoneInfo.Utc;
            }
            
            _logger?.LogInformation("[Base][Init] Service initialized with timezone: {0}", _timeZoneConfig);
        }
        
        // Add a helper method to map IANA timezone IDs to Windows timezone IDs
        private string MapIanaToWindowsTimeZone(string ianaTimeZoneId)
        {
            // Simple mapping for common IANA timezone IDs
            Dictionary<string, string> ianaToWindows = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                // Australia
                { "Australia/Sydney", "AUS Eastern Standard Time" },
                { "Australia/Melbourne", "AUS Eastern Standard Time" },
                { "Australia/Brisbane", "E. Australia Standard Time" },
                { "Australia/Adelaide", "Cen. Australia Standard Time" },
                { "Australia/Perth", "W. Australia Standard Time" },
                { "Australia/Darwin", "AUS Central Standard Time" },
                { "Australia/Hobart", "Tasmania Standard Time" },
                
                // North America
                { "America/New_York", "Eastern Standard Time" },
                { "America/Chicago", "Central Standard Time" },
                { "America/Denver", "Mountain Standard Time" },
                { "America/Los_Angeles", "Pacific Standard Time" },
                
                // Europe
                { "Europe/London", "GMT Standard Time" },
                { "Europe/Paris", "Central European Standard Time" },
                { "Europe/Berlin", "Central European Standard Time" },
                
                // Asia
                { "Asia/Tokyo", "Tokyo Standard Time" },
                { "Asia/Singapore", "Singapore Standard Time" }
            };
            
            if (ianaToWindows.TryGetValue(ianaTimeZoneId, out string windowsTimeZoneId))
            {
                return windowsTimeZoneId;
            }
            
            // Fallback: use region prefix to make a best guess
            string regionPrefix = ianaTimeZoneId.Split('/')[0];
            return regionPrefix switch
            {
                "Australia" => "AUS Eastern Standard Time",
                "America" => "Eastern Standard Time",
                "Europe" => "GMT Standard Time", 
                "Asia" => "Singapore Standard Time",
                "Africa" => "South Africa Standard Time",
                "Pacific" => "Fiji Standard Time",
                _ => "UTC"
            };
        }
        
        // Helper methods for timezone conversion (like in GCUpdateSurveyData)
        protected DateTime ConvertToLocalTime(DateTime utcTime)
        {
            try
            {
                if (utcTime.Kind != DateTimeKind.Utc)
                {
                    // Ensure we're working with UTC time
                    utcTime = DateTime.SpecifyKind(utcTime, DateTimeKind.Utc);
                }
                
                return TimeZoneInfo.ConvertTimeFromUtc(utcTime, _timeZone);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][TimeZone] Error converting UTC time {Time} to local time, using direct conversion", utcTime);
                return utcTime.ToLocalTime();
            }
        }
        
        protected DateTime ConvertToUtcTime(DateTime localTime)
        {
            // If the time is already UTC, return it as is
            if (localTime.Kind == DateTimeKind.Utc)
                return localTime;
                
            try
            {
                // If the time is "Unspecified", assume it's in the configured timezone
                if (localTime.Kind == DateTimeKind.Unspecified)
                    localTime = DateTime.SpecifyKind(localTime, DateTimeKind.Local);
                    
                // Get the timezone offset
                TimeZoneInfo tzi = _timeZone ?? TimeZoneInfo.Utc;
                TimeSpan offset = tzi.GetUtcOffset(localTime);
                
                // Convert using the offset
                return localTime.Subtract(offset);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][TimeZone] Error converting local time to UTC time, using ToUniversalTime");
                return localTime.ToUniversalTime();
            }
        }
        
        public virtual void Dispose()
        {
            // Close websockets and clean up
            foreach (var manager in WebSocketManagers)
            {
                try 
                {
                    manager.Dispose();
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[Base][Cleanup] Error disposing websocket manager");
                }
            }
            
            WebSocketManagers.Clear();
            WebSocketDetails.Clear();
            
            // Wait for threads to terminate with a longer timeout
            foreach (var thread in WebSocketThreads)
            {
                if (thread.IsAlive)
                {
                    try
                    {
                        thread.Join(5000); // Increased timeout to 5 seconds
                        if (thread.IsAlive)
                        {
                            _logger?.LogWarning($"Thread {thread.Name} did not terminate gracefully");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"Error waiting for thread {thread.Name} to terminate");
                    }
                }
            }
            
            WebSocketThreads.Clear();
            
            _logger?.LogInformation("[Base][Cleanup] Service disposed");
        }

        // Add new method to check service health
        public virtual bool IsHealthy()
        {
            // Check if any websocket managers are still active
            if (WebSocketManagers.Count == 0)
            {
                _logger?.LogWarning("[Base][Health] No active websocket managers");
                return false;
            }

            // Check if all websockets are connected
            foreach (var manager in WebSocketManagers)
            {
                if (!manager.IsConnected)
                {
                    _logger?.LogWarning("WebSocket manager not connected");
                    return false;
                }
            }

            return true;
        }

        protected virtual void CreateWebSocket(string socketAddress, string socketChannel, string threadName)
        {
            try
            {
                // Use the existing WebSocketManager implementation from GenesysCloudUtils.WebSocket
                var detail = new GenesysCloudUtils.WebSocket.WebSocketDetail
                {
                    connectUri = socketAddress,
                    id = socketChannel,
                    ReportName = threadName,
                    Created = DateTime.UtcNow,
                    Expires = DateTime.UtcNow.AddHours(1)
                };
                
                // Create the WebSocketManager directly using the factory
                var manager = GenesysCloudUtils.WebSocket.WebSocketManagerFactory.CreateWebSocketManager(
                    _logger,
                    detail,
                    threadName,
                    (jsonString, thread) => ReceiveData(jsonString, thread),
                    _jsonUtils);
                    
                WebSocketManagers.Add(manager);
                manager.StartWebSocketConnection();
                
                _logger?.LogInformation("[Base][WebSocket:{ChannelId}][Thread:{ThreadName}] Created WebSocket connection", 
                    socketChannel, threadName);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][WebSocket] Error creating WebSocket for channel {ChannelId}", socketChannel);
                throw;
            }
        }
        
        protected virtual WebSocketDetail CreateChannel(string name)
        {
            try
            {
                _logger?.LogInformation("[Base][Channel] Attempting to create channel {Name}", name);
                
                // Add retry logic with exponential backoff
                const int maxRetries = 3;
                Exception lastException = null;
                
                for (int attempt = 1; attempt <= maxRetries; attempt++)
                {
                    try
                    {
                        // Create a notification channel - using JsonRestAsync instead of JsonReturnStringAsync
                        string response = _jsonUtils.JsonRestAsync(
                            $"{_jsonUtils.ApiEndpoint}/api/v2/notifications/channels", 
                            null, 
                            "POST", 
                            "{\"connectUri\": true}")
                            .GetAwaiter().GetResult();
                
                        if (string.IsNullOrEmpty(response))
                        {
                            _logger?.LogWarning("[Base][Channel] Empty response when creating channel {Name} (attempt {Attempt}/{MaxRetries})", 
                                name, attempt, maxRetries);
                            continue; // Try again
                        }
                
                        // Deserialize the response
                        var channelData = JsonConvert.DeserializeObject<ChannelResponse>(response);
                
                        if (channelData == null || string.IsNullOrEmpty(channelData.id) || string.IsNullOrEmpty(channelData.connectUri))
                        {
                            _logger?.LogWarning("[Base][Channel] Invalid channel data received for {Name} (attempt {Attempt}/{MaxRetries}): {Response}", 
                                name, attempt, maxRetries, response);
                            continue; // Try again
                        }
                
                        var detail = new WebSocketDetail
                        {
                            id = channelData.id,
                            connectUri = channelData.connectUri,
                            ReportName = name,
                            Created = DateTime.UtcNow,
                            Expires = DateTime.UtcNow.AddMinutes(60)
                        };
                
                        _logger?.LogInformation("[Base][Channel] Successfully created channel {Name} with ID {Id}", name, detail.id);
                        
                        // Store it in our dictionary for reference
                        WebSocketDetails[detail.id] = detail;
                        
                        return detail;
                    }
                    catch (Exception ex)
                    {
                        lastException = ex;
                        _logger?.LogWarning(ex, "[Base][Channel] Error creating notification channel {Name} (attempt {Attempt}/{MaxRetries})", 
                            name, attempt, maxRetries);
                        
                        // Add exponential backoff between retries
                        if (attempt < maxRetries)
                        {
                            int delayMs = (int)Math.Pow(2, attempt) * 500; // 1s, 2s, 4s...
                            _logger?.LogDebug("[Base][Channel] Waiting {DelayMs}ms before retry {Attempt}", delayMs, attempt + 1);
                            Thread.Sleep(delayMs);
                        }
                    }
                }
                
                // If we get here, all retries failed
                _logger?.LogError(lastException, "[Base][Channel] Failed to create channel {Name} after {MaxRetries} attempts", 
                    name, maxRetries);
                    
                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Channel] Unhandled exception creating notification channel {Name}", name);
                return null;
            }
        }

        // Channel response class for deserialization
        private class ChannelResponse
        {
            public string id { get; set; }
            public string connectUri { get; set; }
        }
        
        protected virtual void ReceiveData(string jsonString, string threadName)
        {
            // Base implementation for handling websocket data
            // This can be overridden by derived classes
            _logger?.LogDebug("[Base][Thread:{ThreadName}] Received data", threadName);
            
            // Update event processing metrics
            Interlocked.Increment(ref _processedEventsCount);
            _lastProcessedEventTime = DateTime.UtcNow;
        }
        
        protected virtual void RefreshChannels()
        {
            // Base implementation for refreshing channels
            // This should be overridden by derived classes
            _logger?.LogInformation("[Base][Channel] Refreshing channels");
        }
        
        protected void RefreshWebSocketChannels()
        {
            foreach (var channel in WebSocketDetails.Values)
            {
                if (channel.NeedsRefresh)
                {
                    _logger?.LogInformation("Channel {Id} needs refresh, recreating", channel.id);
                    
                    // Logic to recreate the channel
                    var newChannel = CreateChannel(channel.ReportName);
                    
                    // Remove old managers for this channel
                    var managersToRemove = WebSocketManagers
                        .Where(m => m.ChannelId == channel.id)
                        .ToList();
                        
                    if (managersToRemove.Count() > 0)  // Fixed: Added parentheses to Count() method
                    {
                        foreach (var manager in managersToRemove)
                        {
                            manager.Dispose();
                            WebSocketManagers.Remove(manager);
                        }
                    }
                    
                    // Create new WebSocketManager for the refreshed channel
                    CreateWebSocket(newChannel.connectUri, newChannel.id, newChannel.ReportName);
                }
            }
        }
        
        protected virtual List<DataTable> ChunkDataTable(DataTable source, int chunkSize)
        {
            List<DataTable> chunks = new List<DataTable>();
            DataTable clone = source.Clone();
            int rowCount = 0;
            
            foreach (DataRow row in source.Rows)
            {
                clone.ImportRow(row);
                rowCount++;
                
                if (rowCount >= chunkSize)
                {
                    chunks.Add(clone);
                    clone = source.Clone();
                    rowCount = 0;
                }
            }
            
            if (rowCount > 0)
            {
                chunks.Add(clone);
            }
            
            // _logger?.LogInformation($"Split table into {chunks.Count} chunks");
            return chunks;
        }

        // Changed from abstract to virtual with default implementation
        public virtual void Start()
        {
            _logger?.LogInformation("[Base][Start] Starting service");
            _isRunning = true;
            _serviceStartTime = DateTime.UtcNow;
        }

        public virtual void Stop()
        {
            _logger?.LogInformation("[Base][Stop] Stopping service");
            foreach (var manager in WebSocketManagers)
            {
                manager.Dispose();
            }
            WebSocketManagers.Clear();
            WebSocketThreads.Clear();
            WebSocketDetails.Clear();
        }

        protected virtual async Task<string> CreateNotificationChannel()
        {
            try
            {
                // Use JsonUtils directly instead of _authService.MakeApiRequest
                var response = await _jsonUtils.JsonReturnAsync(
                    $"{_jsonUtils.ApiEndpoint}/api/v2/notifications/channels", null, "POST");
                
                // Handle response format from JsonUtils
                if (response != null && response.Count() > 0)
                {
                    // Return the first element of the JArray
                    return response[0].ToString();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error creating notification channel");
            }
            return null;
        }

        // Update the helper method for checking if tables exist to include detailed logging
        protected bool TableExists(string tableName)
        {
            if (string.IsNullOrEmpty(tableName))
                throw new ArgumentException("Table name cannot be null or empty", nameof(tableName));
                
            try
            {
                // Log the exact query we're going to use
                string tableExistsQuery = DBAdapter.DBType switch
                {
                    CSG.Adapter.Configuration.DatabaseType.MSSQL => 
                        $"SELECT CASE WHEN OBJECT_ID('{tableName}') IS NOT NULL THEN 1 ELSE 0 END AS TableExists",
                    CSG.Adapter.Configuration.DatabaseType.MySQL => 
                        $"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = '{tableName.ToLower()}'",
                    CSG.Adapter.Configuration.DatabaseType.PostgreSQL => 
                        $"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = '{tableName.ToLower()}') as exists",
                    CSG.Adapter.Configuration.DatabaseType.Snowflake => 
                        $"SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '{tableName.ToUpper()}'",
                    _ => throw new NotSupportedException($"Unsupported database type: {DBAdapter.DBType}")
                };
                
                _logger?.LogDebug("[Base][DB] Table check query for '{TableName}': {Query}", tableName, tableExistsQuery);
                
                // Before executing, log information about the current context
                try
                {
                    var dbContext = DBAdapter.GetType().GetProperty("DbContext")?.GetValue(DBAdapter);
                    if (dbContext != null)
                    {
                        _logger?.LogDebug("DbContext type: {ContextType}", dbContext.GetType().FullName);
                        var connectionState = dbContext.GetType().GetProperty("Database")?.GetValue(dbContext)
                            ?.GetType().GetProperty("CurrentTransaction")?.GetValue(dbContext) != null 
                            ? "In transaction" : "No active transaction";
                        _logger?.LogDebug("Connection state: {State}", connectionState);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogDebug("Error inspecting DbContext: {Error}", ex.Message);
                }
                
                // Try to execute the query with detailed logging
                object result = null;
                try
                {
                    result = DBAdapter.ExecuteScalar(tableExistsQuery);
                    _logger?.LogDebug("Query result type: {ResultType}, value: {ResultValue}", 
                        result?.GetType().FullName ?? "null", result?.ToString() ?? "null");
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error executing table check query: {Query}", tableExistsQuery);
                    
                    // Log additional details about the inner exception chain
                    var innerEx = ex.InnerException;
                    int depth = 1;
                    while (innerEx != null)
                    {
                        _logger?.LogError("Inner exception level {Depth}: {Type} - {Message}", 
                            depth++, innerEx.GetType().FullName, innerEx.Message);
                        
                        if (innerEx is System.ArgumentNullException argNullEx)
                        {
                            _logger?.LogError("ArgumentNullException parameter name: {ParamName}", 
                                argNullEx.ParamName);
                        }
                        
                        innerEx = innerEx.InnerException;
                    }
                    
                    throw; // Re-throw to be caught by the outer catch block
                }
                
                bool exists = result != null && !result.Equals(0) && !result.Equals(false);
                
                _logger?.LogDebug("[Base][DB] Table check for '{TableName}': {Exists}", tableName, exists ? "exists" : "does not exist");
                
                return exists;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][DB] Error checking if table '{TableName}' exists", tableName);
                
                // Add fallback implementation with more detailed logging
                _logger?.LogDebug("[Base][DB] Attempting fallback check for table '{TableName}'", tableName);
                try
                {
                    // Try a direct check with GetSQLTableData with LIMIT 1
                    string fallbackQuery = DBAdapter.DBType switch
                    {
                        CSG.Adapter.Configuration.DatabaseType.PostgreSQL => $"SELECT * FROM {tableName} LIMIT 1",
                        CSG.Adapter.Configuration.DatabaseType.MySQL => $"SELECT * FROM {tableName} LIMIT 1",
                        CSG.Adapter.Configuration.DatabaseType.MSSQL => $"SELECT TOP 1 * FROM {tableName}",
                        CSG.Adapter.Configuration.DatabaseType.Snowflake => $"SELECT * FROM {tableName} LIMIT 1",
                        _ => $"SELECT * FROM {tableName} LIMIT 1"
                    };
                    
                    _logger?.LogDebug("Fallback query: {Query}", fallbackQuery);
                    
                    // Execute the fallback query
                    var dataTable = DBAdapter.GetSQLTableData(fallbackQuery, "TableExistsCheck");
                    _logger?.LogDebug("[Base][DB] Fallback check success - table '{TableName}' exists", tableName);
                    return true;
                }
                catch (Exception fallbackEx)
                {
                    _logger?.LogDebug("[Base][DB] Fallback check failed: {Error}", fallbackEx.Message);
                    _logger?.LogInformation("[Base][DB] Assuming table '{TableName}' does not exist", tableName);
                    return false;
                }
            }
        }

        // Update the helper method for getting paginated data with more detailed logging
        protected DataTable GetPaginatedTableData(string tableName, string orderByColumn, string whereClause = "", int limit = 1000)
        {
            if (string.IsNullOrEmpty(tableName))
                throw new ArgumentException("Table name cannot be null or empty", nameof(tableName));
                
            try
            {
                // Only add WHERE if whereClause is not empty
                string whereStatement = string.IsNullOrEmpty(whereClause) ? "" : $" WHERE {whereClause}";
                
                string query = DBAdapter.DBType switch
                {
                    CSG.Adapter.Configuration.DatabaseType.MSSQL => 
                        $"SELECT TOP {limit} * FROM {tableName}{whereStatement} ORDER BY {orderByColumn} DESC",
                    CSG.Adapter.Configuration.DatabaseType.MySQL => 
                        $"SELECT * FROM {tableName}{whereStatement} ORDER BY {orderByColumn} DESC LIMIT {limit}",
                    CSG.Adapter.Configuration.DatabaseType.PostgreSQL => 
                        $"SELECT * FROM {tableName}{whereStatement} ORDER BY {orderByColumn} DESC LIMIT {limit}",
                    CSG.Adapter.Configuration.DatabaseType.Snowflake => 
                        $"SELECT * FROM {tableName}{whereStatement} ORDER BY {orderByColumn} DESC LIMIT {limit}",
                    _ => throw new NotSupportedException($"Unsupported database type: {DBAdapter.DBType}")
                };
                
                _logger?.LogDebug("[Base][DB] Getting paginated data from '{TableName}' with query: {Query}", tableName, query);
                
                // Add diagnostic timing
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                try
                {
                    var result = DBAdapter.GetSQLTableData(query, $"Existing{tableName}Data");
                    stopwatch.Stop();
                    
                    _logger?.LogDebug("[Base][DB] Retrieved {RowCount} rows from '{TableName}' in {ElapsedMs}ms", 
                        result?.Rows.Count ?? 0, stopwatch.ElapsedMilliseconds);
                        
                    return result;
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    _logger?.LogError(ex, "[Base][DB] Error retrieving data from '{TableName}' after {ElapsedMs}ms. Query: {Query}", 
                        tableName, stopwatch.ElapsedMilliseconds, query);
                        
                    // Log detailed information about the exception chain
                    var innerEx = ex.InnerException;
                    int depth = 1;
                    while (innerEx != null)
                    {
                        _logger?.LogError("Inner exception level {Depth}: {Type} - {Message}", 
                            depth++, innerEx.GetType().FullName, innerEx.Message);
                        innerEx = innerEx.InnerException;
                    }
                    
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][DB] Error getting paginated data from table '{TableName}'", tableName);
                return new DataTable();
            }
        }

        protected virtual void InitializeDatabaseTimer(int interval = 3000)
        {
            _databaseWriteTimer = new System.Timers.Timer(interval);
            _databaseWriteTimer.Elapsed += DatabaseWriteTimerCallback;
            _databaseWriteTimer.AutoReset = true;
            _databaseWriteTimer.Enabled = true;
            _logger?.LogInformation("Database write timer initialized with {Interval}ms interval", interval);
        }

        protected virtual void DatabaseWriteTimerCallback(object sender, System.Timers.ElapsedEventArgs e)
        {
            // Add thread name to logs
            string threadName = Thread.CurrentThread.Name ?? "TimerThread";
            
            if (_pendingDatabaseWrite)
            {
                _logger?.LogDebug("[Base][Thread:{ThreadName}][Timer] Processing database writes", threadName);
                ProcessDatabaseWrites();
            }
        }

        // Update the ProcessDatabaseWrites method to include thread context
        protected virtual void ProcessDatabaseWrites()
        {
            // Add thread name to log
            string threadName = Thread.CurrentThread.Name ?? "UnnamedThread";
            _logger?.LogDebug("[Base][Thread:{ThreadName}][DB] Processing database writes", threadName);
            
            // Override in derived classes to implement specific database write logic
            _pendingDatabaseWrite = false;
        }

        protected virtual void CheckFailureThreshold()
        {
            if (_failedChannelAttempts >= MAX_CHANNEL_FAILURES)
            {
                _logger?.LogCritical("Service {ServiceName} has failed {FailCount} times - exceeding the threshold of {MaxFailures}. Exiting application.", 
                    GetType().Name, _failedChannelAttempts, MAX_CHANNEL_FAILURES);
                
                // Terminate the entire application
                Environment.Exit(1);
            }
        }

        protected async Task SafeWriteToDatabase(DataTable table, string dataDescription)
        {
            try
            {
                if (table != null && table.Rows.Count > 0)
                {
                    _logger?.LogInformation("[Base][DB] Writing {Count} {Description} records to database", 
                        table.Rows.Count, dataDescription);
                        
                    // Use the WriteSQLDataBulk method which should be thread-safe
                    DBAdapter.WriteSQLDataBulk(table);
                    
                    _logger?.LogInformation("[Base][DB] Successfully wrote {Count} {Description} records", 
                        table.Rows.Count, dataDescription);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][DB] Error writing {Description} to database", dataDescription);
                Interlocked.Increment(ref _failedChannelAttempts);
                CheckFailureThreshold();
                throw;
            }
        }

        // Add a method to increment error count
        protected void IncrementErrorCount()
        {
            Interlocked.Increment(ref _errorCount);
        }
        
        // -------------------------------
        // Methods added to resolve missing method errors.
        // Ensure that callers invoke these via an instance (e.g. using "this.").
        // -------------------------------

        // Method to restart the main channel - added to fix error in PerformStandardHealthCheckAsync
        protected virtual async Task<bool> RestartMainChannelAsync(
            string channelName, 
            DataTable userTable, 
            string subscriptionSuffix, 
            bool waitForConnection = true)
        {
            try
            {
                _logger?.LogInformation("[Base][Restart] Attempting to restart main channel {ChannelName}", channelName);
                
                // Clean up existing connections first
                foreach (var manager in WebSocketManagers.ToList())
                {
                    try
                    {
                        manager.StopWebSocketConnection();
                        WebSocketManagers.Remove(manager);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[Base][Restart] Error stopping WebSocket manager");
                    }
                }
                
                // Clear any thread references
                WebSocketThreads.Clear();
                
                // Create a new channel with the same name
                var newChannel = CreateChannel(channelName);
                if (newChannel != null)
                {
                    _logger?.LogInformation("[Base][Restart] Successfully created new channel {ChannelId}", newChannel.id);
                    
                    // Store it for reference
                    WebSocketDetails[newChannel.id] = newChannel;
                    
                    // Create subscriptions if user table provided
                    if (userTable != null && userTable.Rows.Count > 0 && !string.IsNullOrEmpty(subscriptionSuffix))
                    {
                        await CreateSubscriptionsForChannelAsync(newChannel, userTable, "v2.users.", subscriptionSuffix);
                    }
                    
                    // Start the WebSocket on a new thread
                    var thread = new Thread(() => CreateWebSocket(newChannel.connectUri, newChannel.id, channelName));
                    thread.Name = channelName;
                    thread.IsBackground = true;
                    thread.Start();
                    WebSocketThreads.Add(thread);
                    
                    // Wait for connection to establish if requested
                    if (waitForConnection)
                    {
                        await Task.Delay(2000);
                        bool isConnected = WebSocketManagers.Any(m => m.IsConnected);
                        _logger?.LogInformation("[Base][Restart] Channel restart complete, connection established: {IsConnected}", isConnected);
                        return isConnected;
                    }
                    
                    _logger?.LogInformation("[Base][Restart] Channel restart complete");
                    return true;
                }
                else
                {
                    _logger?.LogError("[Base][Restart] Failed to create new channel during restart");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Restart] Error restarting main channel");
                return false;
            }
        }

        // Method to get most active entities - added to fix error in GetServiceMetrics
        public List<EntityActivityStats> GetMostActiveEntities(int count = 10, string entityType = null)
        {
            lock (_entityStatsLock)
            {
                var query = _entityStats.Values.AsQueryable();
                
                if (!string.IsNullOrEmpty(entityType))
                {
                    query = query.Where(e => e.EntityType == entityType);
                }
                
                return query
                    .OrderByDescending(e => e.EventsProcessed)
                    .Take(count)
                    .ToList();
            }
        }

        // -------------------------------
        // End of methods added to resolve missing method errors.
        // -------------------------------

        /// <summary>
        /// Performs a standard health check for services
        /// </summary>
        public virtual async Task<bool> PerformStandardHealthCheckAsync(
            string serviceName,
            string channelName,
            DataTable userTable,
            string subscriptionSuffix,
            bool isRunning)
        {
            try 
            {
                _logger?.LogInformation("[{ServiceName}][Health] Performing health check", serviceName);
                
                if (!isRunning)
                {
                    _logger?.LogWarning("[{ServiceName}][Health] Service is not running", serviceName);
                    return false;
                }
                
                bool hasActiveConnections = WebSocketManagers != null && 
                                           WebSocketManagers.Count > 0 &&
                                           WebSocketManagers.Any(m => m.IsConnected);
                                           
                if (!hasActiveConnections)
                {
                    _logger?.LogWarning("[{ServiceName}][Health] No active WebSocket connections", serviceName);
                    
                    // Attempt restart - fix reference to this instance method
                    bool restartSuccessful = await this.RestartMainChannelAsync(channelName, userTable, subscriptionSuffix);
                    if (restartSuccessful)
                    {
                        _logger?.LogInformation("[{ServiceName}][Health] Restart successful - service is now healthy", serviceName);
                        return true;
                    }
                    else
                    {
                        _logger?.LogError("[{ServiceName}][Health] Restart failed - service remains unhealthy", serviceName);
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[{ServiceName}][Health] Health check failed", serviceName);
                return false;
            }
        }

        /// <summary>
        /// Creates subscriptions for a WebSocket channel
        /// </summary>
        public virtual async Task CreateSubscriptionsForChannelAsync(
            WebSocketDetail webSocket,
            DataTable userTable,
            string topicPrefix,
            string topicSuffix)
        {
            if (webSocket == null)
            {
                _logger?.LogError("[Base][Subscriptions] Cannot create subscriptions - WebSocket is null");
                return;
            }

            if (userTable == null || userTable.Rows.Count == 0)
            {
                _logger?.LogWarning("[Base][Subscriptions] User table is empty - no subscriptions to create");
                return;
            }

            if (!userTable.Columns.Contains("id"))
            {
                _logger?.LogError("[Base][Subscriptions] User table does not contain 'id' column - cannot create subscriptions");
                return;
            }

            _logger?.LogInformation("[Base][Subscriptions] Creating subscriptions for {Count} users on channel {ChannelId}", 
                userTable.Rows.Count, webSocket.id);

            try
            {
                // Copy the table to avoid concurrent modification issues
                DataTable safeCopy = userTable.Copy();
                
                // Use the helper to create combined subscriptions
                await GenesysCloudUtils.WebSocket.CombinedSubscriptionHelper
                    .CreateUserSubscriptionsAsync(
                        _logger,
                        _jsonUtils,
                        safeCopy,
                        webSocket,
                        topicPrefix,
                        topicSuffix);
                        
                _logger?.LogInformation("[Base][Subscriptions] Successfully created subscriptions");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[Base][Subscriptions] Error creating subscriptions");
                throw;
            }
        }

        /// <summary>
        /// Gets service metrics for monitoring
        /// </summary>
        public virtual Dictionary<string, object> GetServiceMetrics()
        {
            var metrics = new Dictionary<string, object>
            {
                ["ProcessedEvents"] = _processedEventsCount,
                ["ErrorCount"] = _errorCount,
                ["LastProcessedEventTime"] = _lastProcessedEventTime,
                ["Uptime"] = Uptime.ToString(@"dd\.hh\:mm\:ss"),
                ["IsRunning"] = _isRunning,
                ["ActiveConnections"] = WebSocketManagers.Count(m => m.IsConnected),
                ["UniqueEntitiesTracked"] = _entityStats.Count
            };
            
            // Get top 5 most active entities - fix reference to this instance method
            var topEntities = this.GetMostActiveEntities(5);
            if (topEntities.Any())
            {
                metrics["TopEntities"] = String.Join(", ", 
                    topEntities.Select(e => $"{e.EntityName}({e.EventsProcessed})"));
            }
            
            return metrics;
        }

        // Add method overloads with slight variations to help with method resolution
        
        /// <summary>
        /// Performs a standard health check - overload to help with method resolution
        /// </summary>
        public virtual Task<bool> StandardHealthCheck(
            string serviceName,
            string channelName,
            DataTable userTable,
            string subscriptionSuffix,
            bool isRunning)
        {
            // Call the primary implementation
            return PerformStandardHealthCheckAsync(serviceName, channelName, userTable, subscriptionSuffix, isRunning);
        }

        /// <summary>
        /// Creates subscriptions - overload to help with method resolution
        /// </summary>
        public virtual Task CreateSubscriptions(
            WebSocketDetail webSocket,
            DataTable userTable,
            string topicPrefix,
            string topicSuffix)
        {
            // Call the primary implementation
            return CreateSubscriptionsForChannelAsync(webSocket, userTable, topicPrefix, topicSuffix);
        }
    }
}