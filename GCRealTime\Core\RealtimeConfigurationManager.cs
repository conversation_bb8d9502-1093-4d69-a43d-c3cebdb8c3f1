using System;
using Microsoft.Extensions.Configuration;

namespace GCRealTime.Core
{
    public class RealtimeConfigurationManager
    {
        private static readonly Lazy<RealtimeConfigurationManager> _instance = 
            new Lazy<RealtimeConfigurationManager>(() => new RealtimeConfigurationManager());
        
        public static RealtimeConfigurationManager Instance => _instance.Value;

        private IConfiguration _configuration;

        private RealtimeConfigurationManager()
        {
            // Default configuration
            _configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string>
                {
                    { "RealTime:RefreshIntervalHours", "20" },
                    { "RealTime:MaxTopicsPerSubscription", "1000" },
                    { "RealTime:EnableDetailedLogging", "false" }
                })
                .Build();
        }

        public void Initialize(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public int RefreshIntervalHours => _configuration.GetValue("RealTime:RefreshIntervalHours", 20);
        
        public int MaxTopicsPerSubscription => _configuration.GetValue("RealTime:MaxTopicsPerSubscription", 1000);
        
        public bool EnableDetailedLogging => _configuration.GetValue("RealTime:EnableDetailedLogging", false);
        
        // Add any additional configuration properties as needed
    }
}
