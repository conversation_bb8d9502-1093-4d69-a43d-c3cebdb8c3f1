using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using GCRealTime.Services;
using System.Linq;
using System.Text;

namespace GCRealTime.Core
{
    public class GCRealtimeManager : IDisposable
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly List<GCRealTime.Services.IRealtimeService> _realtimeServices;
        private bool _isRunning = false;
        private CancellationTokenSource _cancellationTokenSource;
        private readonly object _startupLock = new object();
        
        // New fields for monitoring
        private Timer _statusTimer;
        private Timer _healthCheckTimer;
        private readonly Dictionary<string, int> _serviceRestartCounts = new Dictionary<string, int>();
        private readonly Dictionary<string, string> _serviceHealthStatus = new Dictionary<string, string>();
        private readonly Dictionary<string, DateTime> _serviceLastActivity = new Dictionary<string, DateTime>();
        private const int STATUS_INTERVAL_MS = 10000; // 10 seconds
        private const int HEALTH_CHECK_INTERVAL_MS = 10000; // 5 minutes

        // Make this constructor the PRIMARY one with exact matching types for Program.cs line 603
        public GCRealtimeManager(Microsoft.Extensions.Logging.ILogger logger, System.Collections.Generic.List<GCRealTime.Services.IRealtimeService> services)
        {
            _logger = logger;
            _serviceProvider = null;
            _realtimeServices = services ?? new List<GCRealTime.Services.IRealtimeService>();
        }

        // Keep the other constructors as fallbacks
        public GCRealtimeManager(ILogger logger, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _realtimeServices = new List<GCRealTime.Services.IRealtimeService>();
        }

        // Base constructor
        protected GCRealtimeManager(ILogger logger)
        {
            _logger = logger;
            _realtimeServices = new List<GCRealTime.Services.IRealtimeService>();
        }

        // Standard constructor with IServiceProvider
        public GCRealtimeManager(ILogger logger, IServiceProvider serviceProvider, object additionalParam) : this(logger, serviceProvider)
        {
            // Handle additionalParam if needed
        }

        // Additional overloads
        public GCRealtimeManager(ILogger logger, IEnumerable<GCRealTime.Services.IRealtimeService> services) : this(logger)
        {
            _serviceProvider = null;
            _realtimeServices = services?.ToList() ?? new List<GCRealTime.Services.IRealtimeService>();
        }

        public GCRealtimeManager(ILogger logger, List<GCRealTime.Services.IRealtimeService> services, object additionalParam) : this(logger, services)
        {
            // Handle additionalParam if needed
        }

        // Make cancellationToken optional by providing a default value
        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            _logger?.LogInformation("[RealtimeManager] Starting real-time services");
            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            try
            {
                // If we have a service provider, discover and initialize services
                if (_serviceProvider != null && _realtimeServices.Count == 0)
                {
                    foreach (var serviceType in GetRealtimeServiceTypes())
                    {
                        try
                        {
                            var service = (GCRealTime.Services.IRealtimeService)_serviceProvider.GetRequiredService(serviceType);
                            _realtimeServices.Add(service);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "[RealtimeManager] Error resolving service {ServiceName}", serviceType.Name);
                            throw;
                        }
                    }
                }

                // Initialize service monitoring dictionaries
                foreach (var service in _realtimeServices)
                {
                    string serviceName = service.GetType().Name;
                    _serviceRestartCounts[serviceName] = 0;
                    _serviceHealthStatus[serviceName] = "Initializing";
                    _serviceLastActivity[serviceName] = DateTime.UtcNow;
                }

                // Initialize and start each service - fail fast if any service fails
                foreach (var service in _realtimeServices)
                {
                    string serviceName = service.GetType().Name;
                    try
                    {
                        _logger?.LogInformation("[RealtimeManager] Initializing service {ServiceName}", serviceName);
                        await service.InitializeAsync();
                        _serviceHealthStatus[serviceName] = "Initialized";

                        _logger?.LogInformation("[RealtimeManager] Starting service {ServiceName}", serviceName);
                        await service.StartAsync(_cancellationTokenSource.Token);
                        _serviceHealthStatus[serviceName] = "Running";
                        _serviceLastActivity[serviceName] = DateTime.UtcNow;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[RealtimeManager] Error starting service {ServiceName}", serviceName);
                        _serviceHealthStatus[serviceName] = "Failed";
                        
                        // Exit immediately if any service fails - all services are mandatory
                        _logger?.LogCritical("[RealtimeManager] Service {ServiceName} failed to start. All services are mandatory. Exiting application.", serviceName);
                        
                        // Attempt to stop any services that were already started
                        await EmergencyShutdownAsync();
                        
                        // Exit the application
                        Environment.Exit(1);
                        return;
                    }
                }
                
                _isRunning = true;
                _logger?.LogInformation("[RealtimeManager] All real-time services started successfully");
                
                // Start the monitoring timers
                StartMonitoring();
                
                // Output initial status
                OutputServiceStatus();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[RealtimeManager] Error starting real-time services");
                _logger?.LogWarning("[RealtimeManager] Real-time manager is not running");
                
                // Attempt emergency shutdown
                await EmergencyShutdownAsync();
                
                // Exit the application
                Environment.Exit(1);
            }
        }

        private void StartMonitoring()
        {
            // Start timer for regular status updates
            _statusTimer = new Timer(
                callback: async _ => await OutputServiceStatusAsync(),
                state: null,
                dueTime: STATUS_INTERVAL_MS,
                period: STATUS_INTERVAL_MS
            );

            // Start timer for regular health checks
            _healthCheckTimer = new Timer(
                callback: async _ => await PerformHealthChecksAsync(),
                state: null,
                dueTime: HEALTH_CHECK_INTERVAL_MS,
                period: HEALTH_CHECK_INTERVAL_MS
            );
            
            _logger?.LogInformation("[RealtimeManager] Monitoring started. Status updates every {StatusInterval}s, health checks every {HealthCheckInterval}s", 
                STATUS_INTERVAL_MS / 1000, HEALTH_CHECK_INTERVAL_MS / 1000);
        }

        private async Task PerformHealthChecksAsync()
        {
            _logger?.LogInformation("[RealtimeManager] Performing health checks for all services...");
            
            // Update service metrics to ensure counters are correctly updated
            UpdateServiceMetrics();

            foreach (var service in _realtimeServices)
            {
                string serviceName = service.GetType().Name;
                try
                {
                    _logger?.LogDebug("[RealtimeManager] Starting health check for {ServiceName}", serviceName);
                    
                    // Add timeout to prevent health checks from hanging
                    using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                    var healthCheckTask = service.CheckHealthAsync();
                    var completedTask = await Task.WhenAny(healthCheckTask, Task.Delay(TimeSpan.FromSeconds(30), timeoutCts.Token));
                    
                    if (completedTask != healthCheckTask)
                    {
                        _logger?.LogWarning("[RealtimeManager] Health check for {ServiceName} timed out after 30 seconds", serviceName);
                        _serviceHealthStatus[serviceName] = "Timeout";
                        
                        // All services are mandatory - exit if health check times out
                        _logger?.LogCritical("[RealtimeManager] Health check for service {ServiceName} timed out. All services are mandatory. Exiting application.", serviceName);
                        await EmergencyShutdownAsync();
                        Environment.Exit(1);
                        return;
                    }
                    
                    bool isHealthy = await healthCheckTask;
                    string previousStatus = _serviceHealthStatus[serviceName];
                    
                    if (isHealthy)
                    {
                        _serviceHealthStatus[serviceName] = "Healthy";
                        _serviceLastActivity[serviceName] = DateTime.UtcNow;
                        _logger?.LogDebug("[RealtimeManager] Service {ServiceName} reported healthy status", serviceName);
                    }
                    else
                    {
                        _serviceHealthStatus[serviceName] = "Unhealthy";
                        _logger?.LogWarning("[RealtimeManager] Service {ServiceName} reported unhealthy status", serviceName);
                        
                        // Try to restart the service
                        try
                        {
                            await RestartServiceAsync(service);
                            
                            // Check if the restart was successful
                            using (var restartTimeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                            {
                                var restartHealthCheckTask = service.CheckHealthAsync();
                                var restartCompletedTask = await Task.WhenAny(
                                    restartHealthCheckTask, 
                                    Task.Delay(TimeSpan.FromSeconds(30), restartTimeoutCts.Token));
                                
                                if (restartCompletedTask != restartHealthCheckTask || !await restartHealthCheckTask)
                                {
                                    // Restart failed or health check after restart failed
                                    _logger?.LogCritical("[RealtimeManager] Service {ServiceName} failed to recover after restart. All services are mandatory. Exiting application.", serviceName);
                                    await EmergencyShutdownAsync();
                                    Environment.Exit(1);
                                    return;
                                }
                            }
                        }
                        catch (Exception restartEx)
                        {
                            _logger?.LogError(restartEx, "[RealtimeManager] Failed to restart service {ServiceName}", serviceName);
                            _logger?.LogCritical("[RealtimeManager] Service {ServiceName} is unhealthy and restart failed. All services are mandatory. Exiting application.", serviceName);
                            await EmergencyShutdownAsync();
                            Environment.Exit(1);
                            return;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _serviceHealthStatus[serviceName] = "Error";
                    _logger?.LogError(ex, "[RealtimeManager] Error checking health for service {ServiceName}", serviceName);
                    
                    // All services are mandatory - exit if health check throws an exception
                    _logger?.LogCritical("[RealtimeManager] Health check for service {ServiceName} failed with exception. All services are mandatory. Exiting application.", serviceName);
                    await EmergencyShutdownAsync();
                    Environment.Exit(1);
                    return;
                }
            }
            
            // Output the health check results
            OutputServiceStatus();
        }
        
        // Update method declaration to use the Services namespace for the interface
        private async Task RestartServiceAsync(GCRealTime.Services.IRealtimeService service)
        {
            string serviceName = service.GetType().Name;
            try
            {
                _logger?.LogWarning("[RealtimeManager] Attempting to restart service {ServiceName}", serviceName);
                
                // Increment restart count
                if (_serviceRestartCounts.ContainsKey(serviceName))
                {
                    _serviceRestartCounts[serviceName]++;
                }
                else
                {
                    _serviceRestartCounts[serviceName] = 1;
                }
                
                // Stop the service with timeout protection
                _serviceHealthStatus[serviceName] = "Stopping";
                try
                {
                    using var stopTimeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(15));
                    var stopTask = service.StopAsync();
                    var completedTask = await Task.WhenAny(stopTask, Task.Delay(TimeSpan.FromSeconds(15), stopTimeoutCts.Token));
                    
                    if (completedTask != stopTask)
                    {
                        _logger?.LogWarning("[RealtimeManager] Stop operation for {ServiceName} timed out", serviceName);
                    }
                    else
                    {
                        await stopTask; // Get any exceptions that might have been thrown
                    }
                }
                catch (Exception stopEx)
                {
                    _logger?.LogError(stopEx, "[RealtimeManager] Error stopping service {ServiceName} during restart", serviceName);
                }
                
                // Wait a moment before restarting
                await Task.Delay(5000);
                
                // Start the service again with timeout protection
                _serviceHealthStatus[serviceName] = "Restarting";
                try
                {
                    using var initTimeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                    var initTask = service.InitializeAsync();
                    var completedTask = await Task.WhenAny(initTask, Task.Delay(TimeSpan.FromSeconds(30), initTimeoutCts.Token));
                    
                    if (completedTask != initTask)
                    {
                        _logger?.LogWarning("[RealtimeManager] Initialize operation for {ServiceName} timed out", serviceName);
                        _serviceHealthStatus[serviceName] = "Failed";
                        return;
                    }
                    
                    await initTask; // Get any exceptions that might have been thrown
                }
                catch (Exception initEx)
                {
                    _logger?.LogError(initEx, "[RealtimeManager] Error initializing service {ServiceName} during restart", serviceName);
                    _serviceHealthStatus[serviceName] = "Failed";
                    return;
                }
                
                try
                {
                    using var startTimeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                    var startTask = service.StartAsync(_cancellationTokenSource.Token);
                    var completedTask = await Task.WhenAny(startTask, Task.Delay(TimeSpan.FromSeconds(30), startTimeoutCts.Token));
                    
                    if (completedTask != startTask)
                    {
                        _logger?.LogWarning("[RealtimeManager] Start operation for {ServiceName} timed out", serviceName);
                        _serviceHealthStatus[serviceName] = "Failed";
                        return;
                    }
                    
                    await startTask; // Get any exceptions that might have been thrown
                }
                catch (Exception startEx)
                {
                    _logger?.LogError(startEx, "[RealtimeManager] Error starting service {ServiceName} during restart", serviceName);
                    _serviceHealthStatus[serviceName] = "Failed";
                    return;
                }
                
                _serviceHealthStatus[serviceName] = "Running";
                _serviceLastActivity[serviceName] = DateTime.UtcNow;
                
                _logger?.LogInformation("[RealtimeManager] Successfully restarted service {ServiceName}", serviceName);
            }
            catch (Exception ex)
            {
                _serviceHealthStatus[serviceName] = "Failed";
                _logger?.LogError(ex, "[RealtimeManager] Failed to restart service {ServiceName}", serviceName);
            }
        }

        private void OutputServiceStatus()
        {
            // Call the async version but don't await it
            Task.Run(OutputServiceStatusAsync).ConfigureAwait(false);
        }

        private async Task OutputServiceStatusAsync()
        {
            try
            {
                // Force update service metrics before generating status
                UpdateServiceMetrics(true);
                
                // Replace the complex table with simple log entries
                _logger?.LogInformation("---- REALTIME SERVICES SUMMARY ({Time}) ----", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                
                foreach (var service in _realtimeServices)
                {
                    string serviceName = service.GetType().Name;
                    string status = service.IsHealthy() ? "Running" : "Unhealthy";
                    int restarts = _serviceRestartCounts.TryGetValue(serviceName, out int restartCount) ? restartCount : 0;
                    
                    // Make sure each service has updated metrics
                    service.FlushMetrics();
                    
                    // Get detailed metrics for each service
                    if (service is BaseRealTimeService baseService)
                    {
                        // Use the metrics dictionary instead of direct property access
                        var metrics = baseService.GetServiceMetrics();
                        
                        long eventsProcessed = 0;
                        if (metrics.TryGetValue("EventsProcessed", out var eventsObj) && eventsObj != null)
                        {
                            eventsProcessed = Convert.ToInt64(eventsObj);
                        }
                        
                        DateTime lastActivity = DateTime.MinValue;
                        if (metrics.TryGetValue("LastEventTime", out var lastTimeObj) && lastTimeObj is DateTime dt)
                        {
                            lastActivity = dt;
                        }
                        
                        string lastActivityStr = lastActivity == DateTime.MinValue ? 
                            "never" : 
                            $"{(DateTime.Now - lastActivity).TotalMinutes:0.0} min ago";
                        
                        int activeConnections = 0;
                        int totalConnections = 0;
                        
                        if (metrics.TryGetValue("ConnectedConnections", out var activeObj))
                        {
                            activeConnections = Convert.ToInt32(activeObj);
                        }
                        
                        if (metrics.TryGetValue("ActiveConnections", out var totalObj))
                        {
                            totalConnections = Convert.ToInt32(totalObj);
                        }
                        
                        // Log a simple line with the essential information
                        _logger?.LogInformation(
                            "{ServiceName}: Status={Status}, Restarts={Restarts}, Events={Events}, LastActivity={LastActivity}, Connections={ActiveConns}/{TotalConns}",
                            serviceName,
                            status,
                            restarts,
                            eventsProcessed,
                            lastActivityStr,
                            activeConnections,
                            totalConnections
                        );
                    }
                    else
                    {
                        // Fallback for non-BaseRealTimeService implementations
                        _logger?.LogInformation("{ServiceName}: Status={Status}, Restarts={Restarts}", 
                            serviceName, status, restarts);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error generating service status");
            }
        }

        // Make cancellationToken optional by providing a default value
        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            _logger?.LogInformation("[RealtimeManager] Stopping real-time services");
            
            // Stop the monitoring timers
            _statusTimer?.Dispose();
            _healthCheckTimer?.Dispose();
            
            _cancellationTokenSource?.Cancel();

            try
            {
                foreach (var service in _realtimeServices)
                {
                    string serviceName = service.GetType().Name;
                    try
                    {
                        _logger?.LogInformation("[RealtimeManager] Stopping service {ServiceName}", serviceName);
                        await service.StopAsync();
                        _serviceHealthStatus[serviceName] = "Stopped";
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[RealtimeManager] Error stopping service {ServiceName}", serviceName);
                        _serviceHealthStatus[serviceName] = "Error";
                    }
                }
            }
            finally
            {
                _isRunning = false;
                
                // Output final status
                OutputServiceStatus();
                
                Dispose();
                _logger?.LogInformation("[RealtimeManager] Real-time services stopped");
            }
        }

        public void Dispose()
        {
            _statusTimer?.Dispose();
            _healthCheckTimer?.Dispose();
            _cancellationTokenSource?.Dispose();
            
            foreach (var service in _realtimeServices)
            {
                (service as IDisposable)?.Dispose();
            }
            _realtimeServices.Clear();
        }

        private IEnumerable<Type> GetRealtimeServiceTypes()
        {
            // Find all types that implement IRealtimeService
            var serviceTypes = new List<Type>();
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                foreach (Type type in assembly.GetTypes())
                {
                    // Make sure we're checking against the correct interface
                    if (typeof(GCRealTime.Services.IRealtimeService).IsAssignableFrom(type) && !type.IsInterface && !type.IsAbstract)
                    {
                        serviceTypes.Add(type);
                    }
                }
            }
            return serviceTypes;
        }

        public bool IsRunning()
        {
            return _isRunning;
        }

        // Add a new method for emergency shutdown
        private async Task EmergencyShutdownAsync()
        {
            _logger?.LogWarning("[RealtimeManager] Performing emergency shutdown");
            
            // Stop the monitoring timers
            _statusTimer?.Dispose();
            _healthCheckTimer?.Dispose();
            
            // Cancel all operations
            _cancellationTokenSource?.Cancel();

            // Stop any services that were started
            foreach (var service in _realtimeServices)
            {
                string serviceName = service.GetType().Name;
                if (_serviceHealthStatus.TryGetValue(serviceName, out string status) && 
                    (status == "Running" || status == "Initialized" || status == "Healthy"))
                {
                    try
                    {
                        _logger?.LogInformation("[RealtimeManager] Stopping service {ServiceName} during emergency shutdown", serviceName);
                        await service.StopAsync();
                        _serviceHealthStatus[serviceName] = "Stopped";
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[RealtimeManager] Error stopping service {ServiceName} during emergency shutdown", serviceName);
                        _serviceHealthStatus[serviceName] = "Error";
                    }
                }
            }
            
            // Output final status before exiting
            OutputServiceStatus();
            
            _logger?.LogWarning("[RealtimeManager] Emergency shutdown completed");
        }

        // Update the method to never reset service metrics, just report them
        private void UpdateServiceMetrics(bool force = false)
        {
            foreach (var service in _realtimeServices)
            {
                try
                {
                    // Call FlushMetrics to get updated metrics (without resetting)
                    service.FlushMetrics();
                    
                    // Get the service name for logging
                    string serviceName = service.GetType().Name;
                    
                    // Get metrics through the service's own method
                    if (service is BaseRealTimeService baseService)
                    {
                        var metrics = baseService.GetServiceMetrics();
                        
                        // Update service activity timestamp if available
                        if (metrics.TryGetValue("LastEventTime", out var lastTime) && lastTime != null)
                        {
                            if (lastTime is DateTime dt && dt != DateTime.MinValue)
                            {
                                _serviceLastActivity[serviceName] = dt;
                            }
                        }
                        
                        // Log metrics without modifying them
                        if (metrics.TryGetValue("EventsProcessed", out var eventsProcessed) || 
                            metrics.TryGetValue("ProcessedEvents", out eventsProcessed))
                        {
                            _logger?.LogDebug("Service {ServiceName} current event count: {EventCount}", 
                                serviceName, eventsProcessed);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error retrieving metrics for service {ServiceName}", service.GetType().Name);
                }
            }
        }
    }
}