﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PureCloudPlatform.Client.V2.Client;
using PureCloudPlatform.Client.V2.Extensions;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class GCUtils
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        
        public DataSet GCControlData { get; set; }
        private string _genesysBaseUri;
        private readonly ILogger? _logger;
        private readonly JsonUtils _jsonUtils; // JsonUtils instance for making API calls

        public GCUtils()
        {
            _jsonUtils = new JsonUtils();
        }

        public GCUtils(ILogger? logger)
        {
            _logger = logger;
            _jsonUtils = new JsonUtils(logger);
        }

        public void Initialize()
        {
            UCAUtils = new StandardUtils.Utils();

            CustomerKeyID = UCAUtils.ReadSetting("CSG_CUSTOMERKEYID");
            Console.WriteLine("Enabling Encryption");
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            Console.WriteLine("Creating Admin Data");
            CreateGCAdminData();
            Console.WriteLine("Getting Key");
            GetGCAPIKey();
            Console.WriteLine("Retrieved Key");
        }

#nullable enable
        public GCUtils Initialize(CSG.Adapter.Configuration.GenesysApi genesysOptions)
        {
            if (genesysOptions.ClientId is null || genesysOptions.ClientSecret is null)
                throw new ArgumentNullException("Genesys credentials");
            UCAUtils = new StandardUtils.Utils();
            GetGCAPIKey(
                genesysOptions.ClientId,
                new Secret(genesysOptions.ClientId, genesysOptions.ClientSecret).PlainText,
                genesysOptions.Endpoint?.ToString());
            return this;
        }
#nullable restore

        public string OAuthKey(string userID, string password, string URL)
        {
            string oAuth = string.Empty;
            PureCloudPlatform.Client.V2.Client.Configuration.Default.ApiClient.RestClient.BaseUrl = new Uri(URL);
            var accessTokenInfo = PureCloudPlatform.Client.V2.Client.Configuration.Default.ApiClient.PostToken(userID, password);
            oAuth = accessTokenInfo.AccessToken.ToString();
            return oAuth;
        }

        private void CreateGCAdminData()
        {
            GCControlData = new DataSet();
            var settings = new DataTable("GCControlData");
            settings.Columns.Add("GC_USERId", typeof(System.String));
            settings.Columns.Add("GC_Secret", typeof(System.String));
            settings.Columns.Add("GC_URL", typeof(System.String));
            var row = settings.NewRow();
            row["GC_USERId"] = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_USERID");
            row["GC_Secret"] = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_SECRET");
            row["GC_URL"] = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_URL");
            settings.Rows.Add(row);
            GCControlData.Tables.Add(settings);
        }

        public bool GetGCAPIKey()
        {
            GetGCAPIKey(
                CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_USERID"),
                CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_SECRET"),
                CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_URL"));

            return true;
        }

        public bool GetGCAPIKey(string userId, string password, string uri)
        {
            _genesysBaseUri = uri.TrimEnd('/');
            GCApiKey = OAuthKey(userId, password, uri);

            GCApiKeyLastUpdate = DateTime.Now;
            return true;
        }

#nullable enable
        public CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me GetOrganization()
        {
            // Use JsonUtils directly without going through a wrapper method
            string response = _jsonUtils.JsonReturnString($"{_genesysBaseUri}/api/v2/organizations/me", GCApiKey);
            try
            {
                // First check if the response is an array
                if (response.StartsWith("[") && response.EndsWith("]"))
                {
                    // Parse as array first since API returns array with single object
                    var organizations = JsonConvert.DeserializeObject<CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me[]>(response);
                    return organizations?.FirstOrDefault() ?? throw new InvalidOperationException("No organization data returned");
                }
                else
                {
                    // Try parsing as a single object if not an array
                    return JsonConvert.DeserializeObject<CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me>(response) 
                        ?? throw new InvalidOperationException("Failed to deserialize organization data");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error parsing result from Genesys Cloud when calling GET /api/v2/organizations/me [{Response}]", response);
                throw;
            }
        }
#nullable restore
    }
}
