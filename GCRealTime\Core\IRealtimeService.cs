using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace GCRealTime.Core
{
    /// <summary>
    /// Defines the operations required by any real-time service.
    /// </summary>
    public interface IRealtimeService : IDisposable
    {
        // Lifecycle methods
        void Initialize();
        Task InitializeAsync();
        void Start();
        Task StartAsync(CancellationToken token);
        void Stop();
        Task StopAsync();
        
        // Health and monitoring 
        bool IsHealthy();
        Task<bool> CheckHealthAsync();
        
        // Metrics methods
        void FlushMetrics();
        Dictionary<string, object> GetServiceMetrics();
        
        // Properties for metrics
        DateTime LastActivity { get; }
        long EventsProcessed { get; }
    }
}
