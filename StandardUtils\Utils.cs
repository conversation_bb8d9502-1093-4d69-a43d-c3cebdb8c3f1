﻿using System.Text;
using System.Data;
using System.Security.Cryptography;
using System.Text.RegularExpressions;

namespace StandardUtils;

public class Utils
{
    public DataSet GCControlData { get; set; }

    public Utils()
    {
        GCControlData = new DataSet();
    }

    public string ReadSetting(string key)
    {
        // Currently in use settings that are available:
        /*
            CSG_CUSTOMERKEYID
            CSG_GENESYS_SECRET
            CSG_GENESYS_URL
            CSG_GENESYS_USERID
            CSG_SQLCONNECTIONSTRING
            CSG_SQLDATABASESCHEMA
            CSG_SQLDATABASESHAREDDATABASE
            CSG_SQLDATABASETYPE
        */
        return CSG.Adapter.Compatability.LegacyOptions.GetOption(key);
    }

    public DataTable GetGCCustomerConfig()
    {
        DataTable settings = new DataTable("CustomerDetails");
        settings.Clear();
        settings.Columns.Add("datetimezone", typeof(System.String));
        settings.Columns.Add("interval", typeof(System.String));
        settings.Columns.Add("useraggviews", typeof(System.String));
        settings.Columns.Add("queueaggviews", typeof(System.String));
        var row = settings.NewRow();
        row["datetimezone"] =      CSG.Adapter.Compatability.LegacyOptions.GetOption("DateTimeZone");
        row["interval"] =          CSG.Adapter.Compatability.LegacyOptions.GetOption("interval");
        row["useraggviews"] =      CSG.Adapter.Compatability.LegacyOptions.GetOption("useraggviews");
        row["queueaggviews"] =     CSG.Adapter.Compatability.LegacyOptions.GetOption("queueaggviews");
        settings.Rows.Add(row);
        return settings;
     }

    public DateTime ConvertFromUnixTimestamp(double timestamp)
    {
        DateTime origin = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
        return origin.AddSeconds(timestamp);
    }

    public DateTime ConvertFromUnixTimestampMS(double timestamp)
    {
        DateTime origin = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
        return origin.AddMilliseconds(timestamp);
    }

    public double ConvertToUnixTimestamp(DateTime date)
    {
        DateTime origin = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
        TimeSpan diff = date.ToUniversalTime() - origin;
        return Math.Floor(diff.TotalSeconds);
    }

    /// <summary>
    /// Converts a UTC DateTime to the local time for the specified TimeZone.
    /// </summary>
    /// <param name="utcTime">The UTC time to convert. Will be treated as UTC regardless of Kind.</param>
    /// <param name="timeZone">The timezone to convert to</param>
    /// <returns>A DateTime in the specified timezone</returns>
    public DateTime ConvertToLocalTime(DateTime utcTime, TimeZoneInfo timeZone)
    {
        try
        {
            if (utcTime.Kind != DateTimeKind.Utc)
            {
                // Ensure we're working with UTC time
                utcTime = DateTime.SpecifyKind(utcTime, DateTimeKind.Utc);
            }
            
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone);
        }
        catch
        {
            // Fallback to direct conversion
            return utcTime.ToLocalTime();
        }
    }

    /// <summary>
    /// Converts a local DateTime to UTC based on the specified TimeZone.
    /// </summary>
    /// <param name="localTime">The local time to convert</param>
    /// <param name="timeZone">The timezone of the local time</param>
    /// <returns>A DateTime in UTC</returns>
    public DateTime ConvertToUtcTime(DateTime localTime, TimeZoneInfo timeZone)
    {
        // If the time is already UTC, return it as is
        if (localTime.Kind == DateTimeKind.Utc)
            return localTime;
            
        try
        {
            // If the time is "Unspecified", assume it's in the specified timezone
            if (localTime.Kind == DateTimeKind.Unspecified)
                localTime = DateTime.SpecifyKind(localTime, DateTimeKind.Local);
                
            // Convert to UTC using the timezone
            return TimeZoneInfo.ConvertTimeToUtc(localTime, timeZone);
        }
        catch
        {
            // Fallback to direct conversion
            return localTime.ToUniversalTime();
        }
    }

    public string ReturnOS()
    {
        string OSPlatform = string.Empty;
        OperatingSystem os = Environment.OSVersion;
        PlatformID pid = os.Platform;
        switch (pid)
        {
            case PlatformID.Win32NT:
            case PlatformID.Win32S:
            case PlatformID.Win32Windows:
            case PlatformID.WinCE:
                OSPlatform = "Windows";
                break;
            case PlatformID.Unix:
                OSPlatform = "Unix/Linux";
                break;
            default:
                OSPlatform = "Invalid OS Detected";
                break;
        }

        return OSPlatform;
    }

    public int GetStableHashCode(string str)
    {
        unchecked
        {
            int hash1 = 5381;
            int hash2 = hash1;

            for (int i = 0; i < str.Length && str[i] != '\0'; i += 2)
            {
                hash1 = ((hash1 << 5) + hash1) ^ str[i];
                if (i == str.Length - 1 || str[i + 1] == '\0')
                    break;
                hash2 = ((hash2 << 5) + hash2) ^ str[i + 1];
            }

            return hash1 + (hash2 * 1566083941);
        }
    }

    public ulong GetUInt64Hash(HashAlgorithm hasher, string text)
    {
        using (hasher)
        {
            var bytes = hasher.ComputeHash(Encoding.Default.GetBytes(text));
            Array.Resize(ref bytes, bytes.Length + bytes.Length % 8); //make multiple of 8 if hash is not, for example SHA1 creates 20 bytes.
            return Enumerable.Range(0, bytes.Length / 8) // create a counter for de number of 8 bytes in the bytearray
                .Select(i => BitConverter.ToUInt64(bytes, i * 8)) // combine 8 bytes at a time into a integer
                .Aggregate((x, y) => x ^ y); //xor the bytes together so you end up with a ulong (64-bit int)
        }
    }
    public string GetSHA256CompositeKey(string CombinedData)
    {

        // Convert the combined data to bytes
        byte[] bytes = Encoding.UTF8.GetBytes(CombinedData);

        // Compute the hash of the combined data using SHA256 algorithm
        byte[] hashBytes;
        using (var sha256 = System.Security.Cryptography.SHA256.Create())
        {
            hashBytes = sha256.ComputeHash(bytes);
        }

        // Convert the hash bytes to a hexadecimal string
        string hashString = BitConverter.ToString(hashBytes).Replace("-", "");

        return hashString;
    }
    public void HandleSnowflakeColumnNames(DataTable dataTable)
    {
        List<DataColumn> columnsToRemove = new List<DataColumn>();

        foreach (DataColumn column in dataTable.Columns)
        {
            string originalColumnName = column.ColumnName;

            string pattern = @"[^a-zA-Z0-9_]";
            string replacement = "_";

            string fixedColumnName = Regex.Replace(originalColumnName, pattern, replacement);

            if (char.IsDigit(fixedColumnName[0]))
            {
                fixedColumnName = "_" + fixedColumnName;
            }

            if (originalColumnName != fixedColumnName)
            {
                if (dataTable.Columns.Contains(fixedColumnName))
                {
                    // If a column with the modified name exists, store the value there
                    foreach (DataRow row in dataTable.Rows)
                    {
                        row[fixedColumnName] = row[originalColumnName];
                    }

                    // Add the column to the list of columns to remove
                    columnsToRemove.Add(column);
                }
                else
                {
                    column.ColumnName = fixedColumnName;
                }
            }
        }
        List<string> snowflakeReservedKeywords = new List<string> 
        { 
            "ALL", "ALTER", "AND", "ANY", "AS", "BETWEEN", "BY", "CASE", "CAST", "CHECK", 
            "COLUMN", "CONNECT", "CONSTRAINT", "CREATE", "CROSS", "CURRENT_DATE", "CURRENT_TIME", 
            "CURRENT_TIMESTAMP", "CURRENT_USER", "DATABASE", "DELETE", "DISTINCT", "DROP", 
            "ELSE", "EXISTS", "FALSE", "FOLLOWING", "FOR", "FROM", "FULL", "GRANT", "GROUP", 
            "HAVING", "ILIKE", "IN", "INCREMENT", "INNER", "INSERT", "INTERSECT", "INTO", "IS", 
            "JOIN", "LATERAL", "LEFT", "LIKE", "LOCALTIME", "LOCALTIMESTAMP", "MINUS", "NATURAL", 
            "NOT", "NULL", "OF", "ON", "OR", "ORDER", "QUALIFY", "REGEXP", "REVOKE", "RIGHT", 
            "RLIKE", "ROW", "ROWS", "SAMPLE", "SELECT", "SET", "SOME", "START", "TABLE", "TABLESAMPLE", 
            "THEN", "TO", "TRIGGER", "TRUE", "UNION", "UNIQUE", "UPDATE", "USING", "VALUES", "VIEW", 
            "WHEN", "WHENEVER", "WHERE", "WITH"
        };

        foreach (DataColumn column in dataTable.Columns)
        {
            if (snowflakeReservedKeywords.Contains(column.ColumnName.ToUpper()))
            {
                columnsToRemove.Add(column);
            }
        }
        foreach (DataColumn columnToRemove in columnsToRemove)
        {
            dataTable.Columns.Remove(columnToRemove);
        }       

    }
}
// spell-checker: ignore: DTCGC
