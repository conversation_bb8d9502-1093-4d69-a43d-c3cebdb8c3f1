using Microsoft.Extensions.DependencyInjection;
using GCRealTime.Services;
using System;

namespace GCRealTime.Core
{
    public static class ServiceCollectionExtensions 
    {
        public static IServiceCollection AddRealtimeServices(this IServiceCollection services, Type[] serviceTypes)
        {
            foreach (var serviceType in serviceTypes)
            {
                services.AddSingleton(typeof(GCRealTime.Services.IRealtimeService), serviceType);
            }
            return services;
        }
    }
}
