# Genesys Cloud Data Adapter 

Synchronises Genesys metrics (both real-time and historical) to a database
allowing use by business analytics applications.

[[_TOC_]]

## Build and Test

| Dev | Prod |
|-----|------|
| [![Build Status](https://dev.azure.com/customerscience/technology/_apis/build/status/genesys-adapter?branchName=dev)](https://dev.azure.com/customerscience/technology/_build/latest?definitionId=2&branchName=dev) | [![Build Status](https://dev.azure.com/customerscience/technology/_apis/build/status/genesys-adapter?branchName=master)](https://dev.azure.com/customerscience/technology/_build/latest?definitionId=2&branchName=master) |

### Development Practises

* [GitFlow](https://nvie.com/posts/a-successful-git-branching-model/) branching model
* [C# Coding Style](https://github.com/dotnet/runtime/blob/main/docs/coding-guidelines/coding-style.md)
* [Clean Code concepts](https://github.com/thangchung/clean-code-dotnet)
* Commit messages follow [Conventional Commits](https://www.conventionalcommits.org/)

### Build

[Nuke](https://nuke.build) is used to generate the Azure build pipeline and also allows cross platform local execution
of the pipeline.

```powershell
./build.ps1 --help
./build.ps1
```

```bash
./build.sh --help
./build.sh
```

Install the nuke tool to allow execution using the `nuke` command.

```sh
dotnet nuke --help
```

Local building can also be done with the normal dotnet tools.

```sh
dotnet build
dotnet test
dotnet run
dotnet publish -c Release -r linux-musl-x64 -p:PublishSingleFile=True --self-contained --no-restore GenesysAdapter/GenesysAdapter.csproj
```

### dotnet local tools

Install the default dotnet local tools by running the command below. This will install nuke, dotnet-outdated, etc.

```sh
dotnet tool restore
```

### Podman

The pipeline will build a Docker image, it is recommended to use [Podman](https://podman.io/) for local testing.
Nuke does a locate on docker, so setting an alias to podman is not enough, creating a symlink is required to use Podman
in the Nuke pipeline.

* DockerHub doesn't support OCI images so need to set BUILDAH_FORMAT, [DockerHub #1871](https://github.com/docker/hub-feedback/issues/1871)

#### Setting alternate / symbolic link

```bash
sudo update-alternatives --install /usr/local/bin/docker docker /usr/bin/podman 20
```

```powershell
New-Item -ItemType SymbolicLink -Path:(Split-Path (Get-Command -Name podman.exe).Source) -Name:"docker.exe" -Target (Get-Command -Name podman.exe).Source
```

### Updating package dependencies

* [dotnet-outdated](https://github.com/dotnet-outdated/dotnet-outdated)

Check for updates:

```powershell
dotnet dotnet-outdated [--upgrade]
```

## Synchronisation Types and Tables

| Job               | Table (Handled In)              | API                                                       | Notes                                           |
|-------------------|---------------------------------|-----------------------------------------------------------|-------------------------------------------------|
| adherence         |                                 |                                                           |                                                 |
| aggregation       |                                 |                                                           |                                                 |
| chat              |                                 |                                                           |                                                 |
| evaluation        | evaldata                        |                                                           | One row per evaluation, with conversation ID,   |
|                   |                                 |                                                           | evaluation ID, current status, scores, related  |
|                   |                                 |                                                           | datesand user IDs.                              |
| evaluationcatchup |                                 |                                                           |                                                 |
| factdata          |                                 |                                                           |                                                 |
| headcountforecast |                                 |                                                           |                                                 |
| hoursblockdata    |                                 |                                                           |                                                 |
| interaction       | convsummaryData                 |                                                           |                                                 |
|                   | (UpdateGCDetailInteractionData) |                                                           |                                                 |
| interaction       | detailedInteractionData         |                                                           |                                                 |
| interaction       | participantAttributesDynamic    |                                                           |                                                 |
| interaction       | participantsummaryData          |                                                           |                                                 |
| oauthusage        |                                 |                                                           |                                                 |
| odcontactlists    | odcontactlistdata               | POST /api/v2/outbound/contactlists/{contactListId}/export | Depends on data from odcontactlistdetails,      |
|                   |                                 |                                                           | downloads a csv and dynamically adds columns    |
|                   |                                 |                                                           | from the csv                                    |
| oddetails         | odcontactlistdetails            | /api/v2/outbound/contactlists                             |                                                 |
|                   |                                 | /api/v2/outbound/campaigns                                |                                                 |
| offeredforecast   |                                 |                                                           |                                                 |
| oiceanalysis      |                                 |                                                           |                                                 |
| presencedetail    |                                 |                                                           |                                                 |
| queuemembership   |                                 |                                                           |                                                 |
| realtime          |                                 |                                                           |                                                 |
| scheduledetails   |                                 |                                                           |                                                 |
| subscription      |                                 |                                                           |                                                 |
| subsusers         |                                 |                                                           |                                                 |
| sysconvusage      |                                 |                                                           |                                                 |
| teamsdetails      |                                 |                                                           |                                                 |
| timeoffreq        |                                 |                                                           |                                                 |
| userqueueaudit    |                                 |                                                           |                                                 |
| userqueuemapping  |                                 |                                                           |                                                 |
| voiceanalysis     | convvoiceoverviewdata           |                                                           |                                                 |
|                   | convvoicesentimentdetaildata    |                                                           |                                                 |
|                   | convvoicetopicdetaildata        |                                                           |                                                 |
|                   | (UpdateGCVoiceAnalysisData)     |                                                           |                                                 |
| wfmaudit          |                                 |                                                           |                                                 |
| wfmschedule       |                                 |                                                           |                                                 |

# Genesys Adapter

## Migration Guide: Moving from Legacy to Refactored Implementation

### Overview

The Genesys Adapter has been refactored to improve maintainability, testability, and scalability. The monolithic classes have been broken down into service-oriented components with clear responsibilities.

### Key Changes

1. **New Architecture**: Service-based approach with dependency injection
2. **Improved Logging**: Structured logging with better context
3. **Better Exception Handling**: More robust error recovery
4. **Performance Optimizations**: Reduced memory usage and more efficient processing

### How to Migrate

#### For Applications Using the Legacy API

If your application is using the old `GCRealTime` class:

```csharp
// Old way
var gcRealTime = new GCRealTime(logger, telemetryClient);
gcRealTime.RunRealTime();
```

Change to the new implementation:

```csharp
// New way
var realtimeManager = new GCRealtimeManager(logger, telemetryClient);
realtimeManager.Start();

// When done:
realtimeManager.Stop();
realtimeManager.Dispose();
```

#### For Transitional Applications

If you need both old and new APIs during transition, use the adapter:

```csharp
// Transition way
var adapter = new LegacyAdapter(logger, telemetryClient);
adapter.StartServices();

// When done:
adapter.StopServices();
adapter.Dispose();
```

### Running the Application

The application supports both implementations:

```
# Run with new implementation (recommended)
dotnet run

# Run with legacy implementation
dotnet run legacy
```

### Important Notes

1. The legacy classes (`GCRealTime`, `UserRealTime`) are marked as deprecated and will be removed in a future version
2. The new implementation has identical functionality but with better architecture
3. QueueObsRealTime is still being refactored and will be integrated in the next release

# Genesys Realtime Services

## Architecture

The Genesys Realtime services have been refactored with a modular, service-based architecture:

1. **GCRealtimeManager**: Central coordinator for all realtime services
2. **BaseRealTimeService**: Abstract base class that handles common functionality
3. **Service Classes**: 
   - UserActivityService: Monitors user presence and status
   - UserAdherenceService: Handles workforce management adherence tracking
   - UserCallStatsService: Tracks call statistics for users
   - UserCallDetailsService: Processes detailed call information for users
   - QueueCallDetailsService: Manages queue-related call details

## Configuration

The realtime services are configured through the standard configuration system. The following options are available under the `RealTime` section:

```json
{
  "RealTime": {
    "RefreshIntervalHours": 20,
    "MaxTopicsPerSubscription": 1000,
    "EnableDetailedLogging": false
  }
}
```

## Usage

To use the realtime services, simply specify the `Realtime` job when running the GenesysAdapter:

```
GenesysAdapter Job=Realtime
```

The service will connect to Genesys Cloud, establish WebSocket connections for various notification topics, and persist the data to the configured database.
