﻿using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class JsonUtils
    {
        public int MaxPages { get; set; } = 100;
        public int RateLimitTimeToGo { get; set; }
        public string? responseCode { get; set; }
        public HttpResponseHeaders? responseHeaders { get; set; }
        private readonly ILogger? _logger;
        private HttpClient? _httpClient;
        private string _accessToken = string.Empty;
        private DateTime _tokenExpiration = DateTime.MinValue;
        private string _clientId = string.Empty;
        private string _clientSecret = string.Empty;
        private string _apiEndpoint = string.Empty;
        private int _maxRetries = 3;
        private const int MAX_PAGES_TO_RETRIEVE = 10000;
        private bool _credentialsInitialized = false;
        private DateTime _lastTokenRefresh = DateTime.MinValue;

        // Add public property to access the API endpoint
        public string ApiEndpoint 
        {
            get 
            {
                EnsureCredentials();
                return _apiEndpoint;
            }
        }

        /// <summary>
        /// Creates a new instance of JsonUtils with credentials to be loaded from default sources
        /// </summary>
        public JsonUtils()
        {
            InitializeHttpClient();
        }

        /// <summary>
        /// Creates a new instance of JsonUtils with credentials to be loaded from default sources and a logger
        /// </summary>
        /// <param name="logger">Logger instance</param>
        public JsonUtils(ILogger? logger)
        {
            _logger = logger;
            InitializeHttpClient();
        }

        /// <summary>
        /// Creates a new instance of JsonUtils with explicitly provided credentials
        /// </summary>
        /// <param name="clientId">Genesys Cloud client ID</param>
        /// <param name="clientSecret">Genesys Cloud client secret</param>
        /// <param name="apiEndpoint">Genesys Cloud API endpoint</param>
        /// <param name="logger">Optional logger instance</param>
        public JsonUtils(string clientId, string clientSecret, string apiEndpoint, ILogger? logger = null)
        {
            _logger = logger;
            _clientId = clientId;
            _clientSecret = clientSecret;
            _apiEndpoint = apiEndpoint;
            _credentialsInitialized = true;
            
            InitializeHttpClient();
            
            _logger?.LogInformation("JSON utilities initialized with provided credentials for endpoint {Endpoint}", _apiEndpoint);
        }

        private void InitializeHttpClient()
        {
            if (_httpClient == null)
            {
                _httpClient = new HttpClient();
                _httpClient.DefaultRequestHeaders.Accept.Clear();
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                _httpClient.DefaultRequestHeaders.Add("User-Agent", "GenesysCloudAdapter");
            }
        }

        /// <summary>
        /// Ensures credentials are loaded before making API calls
        /// </summary>
        private void EnsureCredentials()
        {
            if (_credentialsInitialized)
                return;
                
            // Try different credential sources in order of priority
            if (
                TryLoadCredentialsFromLegacyOptions() 
                // || TryLoadCredentialsFromEnvironment()
                // || TryLoadCredentialsFromFile()
                )
            {
                _credentialsInitialized = true;
                _logger?.LogInformation("Successfully loaded Genesys Cloud credentials for endpoint {Endpoint}", _apiEndpoint);
            }
            else
            {
                var errorMessage = "Failed to load Genesys Cloud credentials. Please set CSG_GENESYS_USERID, CSG_GENESYS_SECRET, and CSG_GENESYS_URL, or provide them explicitly.";
                _logger?.LogError(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
        }
        
        /// <summary>
        /// Attempts to load credentials from environment variables
        /// </summary>
        private bool TryLoadCredentialsFromEnvironment()
        {
            var clientId = Environment.GetEnvironmentVariable("CSG_GENESYS_USERID");
            var clientSecret = Environment.GetEnvironmentVariable("CSG_GENESYS_SECRET");
            var apiEndpoint = Environment.GetEnvironmentVariable("CSG_GENESYS_URL");
            
            if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(clientSecret) && !string.IsNullOrEmpty(apiEndpoint))
            {
                _clientId = clientId;
                _clientSecret = clientSecret;
                _apiEndpoint = apiEndpoint;
                _logger?.LogInformation("Loaded Genesys Cloud credentials from environment variables");
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// Attempts to load credentials from legacy options system
        /// </summary>
        private bool TryLoadCredentialsFromLegacyOptions()
        {
            try
            {
                string clientId = null;
                string clientSecret = null;
                string apiEndpoint = null;
                
                try
                {
                    clientId = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_USERID");
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Error getting CSG_GENESYS_USERID from LegacyOptions");
                }
                
                try
                {
                    clientSecret = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_SECRET");
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Error getting CSG_GENESYS_SECRET from LegacyOptions");
                }
                
                try
                {
                    apiEndpoint = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_URL");
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Error getting CSG_GENESYS_URL from LegacyOptions");
                }
                
                if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(clientSecret) && !string.IsNullOrEmpty(apiEndpoint))
                {
                    _clientId = clientId;
                    _clientSecret = clientSecret;
                    _apiEndpoint = apiEndpoint;
                    _logger?.LogInformation("Loaded Genesys Cloud credentials from legacy options");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to load credentials from legacy options");
            }
            
            return false;
        }
        
        /// <summary>
        /// Attempts to load credentials from a credentials file
        /// </summary>
        private bool TryLoadCredentialsFromFile()
        {
            // Try to locate a credentials file in standard locations
            string[] possiblePaths = new[] {
                "genesys_credentials.json",
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".genesys", "credentials.json"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "genesys_credentials.json")
            };
            
            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    try
                    {
                        var json = JObject.Parse(File.ReadAllText(path));
                        
                        var clientId = json["clientId"]?.ToString();
                        var clientSecret = json["clientSecret"]?.ToString();
                        var apiEndpoint = json["apiEndpoint"]?.ToString();
                        
                        if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(clientSecret) && !string.IsNullOrEmpty(apiEndpoint))
                        {
                            _clientId = clientId;
                            _clientSecret = clientSecret;
                            _apiEndpoint = apiEndpoint;
                            _logger?.LogInformation("Loaded Genesys Cloud credentials from file: {Path}", path);
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "Failed to load credentials from file: {Path}", path);
                    }
                }
            }
            
            return false;
        }

        /// <summary>
        /// Sets credentials explicitly after initialization
        /// </summary>
        public void SetCredentials(string clientId, string clientSecret, string apiEndpoint)
        {
            if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret) || string.IsNullOrEmpty(apiEndpoint))
            {
                throw new ArgumentException("Client ID, client secret, and API endpoint must all be provided");
            }
            
            _clientId = clientId;
            _clientSecret = clientSecret;
            _apiEndpoint = apiEndpoint;
            _credentialsInitialized = true;
            
            // Invalidate existing token to force refresh with new credentials
            _accessToken = string.Empty;
            _tokenExpiration = DateTime.MinValue;
            
            _logger?.LogInformation("Credentials updated for endpoint {Endpoint}", _apiEndpoint);
        }

        public async Task<JToken> JsonReturnAsync(string Endpoint, Dictionary<string, string> headerParams = null, string method = "GET", string body = "")
        {
            try
            {
                string response = await JsonRestAsync(Endpoint, headerParams, method, body);
                
                if (string.IsNullOrEmpty(response))
                {
                    _logger?.LogWarning("Empty response received from API");
                    return new JArray(); // Return empty array as fallback
                }
                
                // Check the first non-whitespace character to determine if it's an object or array
                response = response.TrimStart();
                if (response.StartsWith("{"))
                {
                    // Parse as JObject if response starts with {
                    return JObject.Parse(response);
                }
                else if (response.StartsWith("["))
                {
                    // Parse as JArray if response starts with [
                    return JArray.Parse(response);
                }
                else
                {
                    _logger?.LogWarning($"Unexpected JSON format: {response.Substring(0, Math.Min(20, response.Length))}...");
                    return new JArray(); // Return empty array as fallback
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in JsonReturnAsync: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        // Add a helper method to normalize HTTP method names
        private string NormalizeHttpMethod(string methodType)
        {
            if (string.IsNullOrEmpty(methodType))
            {
                return "GET"; // Default to GET if null or empty
            }
            
            // Remove any leading/trailing whitespace and convert to uppercase for consistent comparison
            string method = methodType.Trim().ToUpperInvariant();
            
            // Check for null or empty after trimming
            if (string.IsNullOrEmpty(method))
            {
                return "GET";
            }
            
            // Add logging to see what methodType is causing the issue
            _logger?.LogDebug("Normalizing HTTP method: '{Method}'", method);
            
            // Validate against the list of standard HTTP methods
            switch (method)
            {
                case "GET":
                case "POST":
                case "PUT":
                case "DELETE":
                case "HEAD":
                case "OPTIONS":
                case "TRACE":
                case "PATCH":
                    return method;
                default:
                    // If the method contains invalid characters, default to GET
                    if (ContainsInvalidMethodChars(method))
                    {
                        _logger?.LogWarning("HTTP method '{Method}' contains invalid characters, defaulting to GET", method);
                        return "GET";
                    }
                    
                    // If it's a non-standard method but otherwise valid, allow it (though this is unusual)
                    _logger?.LogWarning("Non-standard HTTP method '{Method}' specified, using as-is", method);
                    return method;
            }
        }

        // Helper method to check for invalid method characters according to RFC7230
        private bool ContainsInvalidMethodChars(string method)
        {
            if (string.IsNullOrEmpty(method))
                return true;
                
            // Valid HTTP method characters are uppercase letters, digits, and specific symbols
            // RFC7230 defines method as: method = token
            // token = 1*tchar
            // tchar = "!" / "#" / "$" / "%" / "&" / "'" / "*" / "+" / "-" / "." / "^" / "_" / "`" / "|" / "~" / DIGIT / ALPHA
            foreach (char c in method)
            {
                if (!(char.IsLetterOrDigit(c) || 
                      c == '!' || c == '#' || c == '$' || c == '%' || c == '&' || c == '\'' || c == '*' || 
                      c == '+' || c == '-' || c == '.' || c == '^' || c == '_' || c == '`' || c == '|' || c == '~'))
                {
                    return true; // Found an invalid character
                }
            }
            
            return false; // All characters were valid
        }

        // Add a fallback method for when HttpMethod constructor fails
        private async Task<JArray> FallbackGetRequestAsync(string uri, string accessToken = null, string jsonBody = null)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Get, uri))
            {
                // Set headers
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                // Set access token if provided
                if (!string.IsNullOrEmpty(accessToken))
                {
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                }
                else if (!string.IsNullOrEmpty(_accessToken))
                {
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                }
                
                // Execute request
                using (var response = await _httpClient.SendAsync(request))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        string responseContent = await response.Content.ReadAsStringAsync();
                        if (string.IsNullOrEmpty(responseContent))
                        {
                            return new JArray();
                        }
                        
                        // Try to parse as array first
                        try
                        {
                            return JArray.Parse(responseContent);
                        }
                        catch
                        {
                            // If it's not a valid array, parse as object and wrap in array
                            var obj = JObject.Parse(responseContent);
                            var array = new JArray();
                            array.Add(obj);
                            return array;
                        }
                    }
                    else
                    {
                        _logger?.LogError("Fallback GET request to {Uri} failed with status {Status}: {Content}", 
                            uri, response.StatusCode, await response.Content.ReadAsStringAsync());
                        return new JArray();
                    }
                }
            }
        }

        public JArray JsonReturn(string uri, string accessToken = null, string method = "GET", string body = "")
        {
            // Create headers dictionary if accessToken is provided
            Dictionary<string, string> headers = null;
            if (!string.IsNullOrEmpty(accessToken))
            {
                headers = new Dictionary<string, string>
                {
                    { "Authorization", $"Bearer {accessToken}" }
                };
            }
            
            return JsonReturnAsync(uri, headers, method, body).GetAwaiter().GetResult() as JArray;
        }

        private async Task<JArray> HandlePaginationAsync(string nextUri, string accessToken = null)
        {
            JArray allResults = new JArray();
            string currentUri = nextUri;
            int pageCount = 1;
            
            // Get the token to use - either provided or from class
            string tokenToUse = accessToken;
            if (string.IsNullOrEmpty(tokenToUse))
            {
                // If we're using the class token, ensure it's valid
                if (string.IsNullOrEmpty(_accessToken) || _tokenExpiration <= DateTime.UtcNow)
                {
                    await RefreshTokenAsync();
                }
                tokenToUse = _accessToken;
            }
            
            while (!string.IsNullOrEmpty(currentUri) && pageCount < MaxPages)
            {
                _logger?.LogDebug("Retrieving page {PageCount} from {Uri}", pageCount, currentUri);
                
                var request = new HttpRequestMessage(HttpMethod.Get, currentUri);
                request.Headers.Add("Authorization", $"Bearer {tokenToUse}");
                
                var response = await _httpClient.SendAsync(request);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var jsonResult = JObject.Parse(content);
                    
                    if (jsonResult["entities"] != null && jsonResult["entities"] is JArray entities)
                    {
                        foreach (var item in entities)
                        {
                            allResults.Add(item);
                        }
                        
                        // Check for more pages
                        if (jsonResult["nextUri"] != null && !string.IsNullOrEmpty(jsonResult["nextUri"].ToString()))
                        {
                            currentUri = jsonResult["nextUri"].ToString();
                            pageCount++;
                            
                            if (pageCount >= MaxPages)
                            {
                                _logger?.LogWarning("Reached maximum number of pages ({MaxPages}). Stopping pagination.", MaxPages);
                                break;
                            }
                            
                            // Small delay to avoid hammering the API
                            await Task.Delay(100);
                        }
                        else
                        {
                            currentUri = null;
                        }
                    }
                    else
                    {
                        currentUri = null;
                    }
                }
                else
                {
                    _logger?.LogError("Failed to retrieve page {PageCount}. Status: {StatusCode}", 
                        pageCount, response.StatusCode);
                    break;
                }
            }
            
            return allResults;
        }

        public async Task<bool> RefreshTokenAsync(bool forceRefresh = false)
        {
            EnsureCredentials();
            
            // Don't refresh if we recently got a token (within last 10 seconds) unless force refresh is requested
            if (!forceRefresh && !string.IsNullOrEmpty(_accessToken) && 
                (DateTime.UtcNow - _lastTokenRefresh) < TimeSpan.FromSeconds(10))
            {
                _logger?.LogDebug("Token refresh skipped - token was recently refreshed");
                return true;
            }
            
            try
            {
                _logger?.LogInformation("Refreshing Genesys Cloud access token{ForceRefresh}", 
                    forceRefresh ? " (forced refresh)" : "");
                
                // Construct the token URL based on the API endpoint
                // Replace 'api' with 'login' in the hostname
                Uri apiUri = new Uri(_apiEndpoint);
                string apiHost = apiUri.Host;
                
                string tokenUrl;
                
                // Smart replacement of api with login in the hostname
                if (apiHost.StartsWith("api."))
                {
                    string loginHost = "login." + apiHost.Substring(4); // Remove "api." prefix
                    tokenUrl = $"{apiUri.Scheme}://{loginHost}/oauth/token";
                }
                else
                {
                    // If the API URL doesn't start with api., throw an exception
                    throw new InvalidOperationException(
                        $"Cannot determine login URL from API endpoint '{_apiEndpoint}'. " +
                        "The API endpoint must start with 'api.'"
                    );
                }
                
                _logger?.LogDebug("Token request URL: {TokenUrl}", tokenUrl);
                
                // Create HTTP client
                using (var httpClient = new HttpClient())
                {
                    // Set timeout
                    httpClient.Timeout = TimeSpan.FromSeconds(30);
                    
                    // Create the request message
                    var request = new HttpRequestMessage(HttpMethod.Post, tokenUrl);
                    
                    // Add Basic Authentication header
                    string authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_clientId}:{_clientSecret}"));
                    request.Headers.Authorization = new AuthenticationHeaderValue("Basic", authValue);
                    
                    // Add form content
                    var formData = new Dictionary<string, string>
                    {
                        { "grant_type", "client_credentials" }
                    };
                    request.Content = new FormUrlEncodedContent(formData);
                    
                    // Make the request and get response
                    int retryCount = 0;
                    const int maxRetries = 3;
                    int baseDelayMs = 1000; // Start with 1 second delay
                    
                    while (retryCount < maxRetries)
                    {
                        try
                        {
                            var response = await httpClient.SendAsync(request);
                            var content = await response.Content.ReadAsStringAsync();
                            
                            // Save response info for caller
                            responseCode = response.StatusCode.ToString();
                            responseHeaders = response.Headers;
                            
                            if (response.IsSuccessStatusCode)
                            {
                                // Parse the JSON response
                                var tokenResponse = JObject.Parse(content);
                                
                                if (tokenResponse["access_token"] != null)
                                {
                                    _accessToken = tokenResponse["access_token"].ToString();
                                    
                                    // Set the token expiration time with a safety margin (5 minutes)
                                    int expiresIn = tokenResponse["expires_in"]?.Value<int>() ?? 3600;
                                    _tokenExpiration = DateTime.UtcNow.AddSeconds(expiresIn - 300);
                                    _lastTokenRefresh = DateTime.UtcNow; // Track when we last refreshed
                                    
                                    _logger?.LogInformation("Successfully obtained access token, expires at {Expiry}", 
                                        _tokenExpiration);
                                    
                                    return true;
                                }
                                else
                                {
                                    _logger?.LogError("Token response missing access_token: {Content}", content);
                                }
                            }
                            else if (response.StatusCode == HttpStatusCode.TooManyRequests) // 429 error
                            {
                                int retryAfter = 60; // Default retry after 60 seconds
                                
                                if (response.Headers.Contains("Retry-After") && 
                                    int.TryParse(response.Headers.GetValues("Retry-After").FirstOrDefault(), out int headerRetryValue))
                                {
                                    retryAfter = headerRetryValue;
                                }
                                
                                _logger?.LogWarning("Rate limited (429) during token refresh. Retry-After: {RetryAfter}s", retryAfter);
                                
                                // Use a more conservative retry time if rate limited
                                await Task.Delay(retryAfter * 1000);
                            }
                            else
                            {
                                _logger?.LogError("Failed to obtain access token: {StatusCode} - {Content}", 
                                    response.StatusCode, content);
                                
                                if (response.StatusCode == HttpStatusCode.NotFound)
                                {
                                    _logger?.LogError("Token endpoint not found. Check API endpoint URL: {Endpoint}", _apiEndpoint);
                                }
                                
                                if (response.StatusCode == HttpStatusCode.Unauthorized)
                                {
                                    _logger?.LogError("Unauthorized (401) when obtaining access token. Check client credentials.");
                                }
                            }
                            
                            retryCount++;
                            if (retryCount < maxRetries)
                            {
                                // Exponential backoff with jitter
                                int delay = (int)(baseDelayMs * Math.Pow(2, retryCount) * (0.8 + 0.4 * new Random().NextDouble()));
                                _logger?.LogWarning("Retrying token refresh in {Delay}ms. Attempt {RetryCount}/{MaxRetries}", 
                                    delay, retryCount + 1, maxRetries);
                                await Task.Delay(delay);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Error during token refresh attempt {Count}/{MaxRetries}", 
                                retryCount + 1, maxRetries);
                            
                            retryCount++;
                            if (retryCount < maxRetries)
                            {
                                // Exponential backoff with jitter for exceptions
                                int delay = (int)(baseDelayMs * Math.Pow(2, retryCount) * (0.8 + 0.4 * new Random().NextDouble()));
                                await Task.Delay(delay);
                            }
                        }
                    }
                    
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Unhandled error refreshing access token");
                return false;
            }
        }

        public bool RefreshToken(bool forceRefresh = false)
        {
            return RefreshTokenAsync(forceRefresh).GetAwaiter().GetResult();
        }
        
        public string GetAccessToken(bool forceRefresh = false)
        {
            EnsureCredentials();
            
            // Check if token needs refreshing or force refresh requested
            if (forceRefresh || string.IsNullOrEmpty(_accessToken) || _tokenExpiration <= DateTime.UtcNow)
            {
                RefreshToken(forceRefresh);
            }
            
            return _accessToken;
        }

        /// <summary>
        /// Makes an API request and returns the result as a string
        /// This is a legacy method maintained for backward compatibility
        /// </summary>
        public string JsonReturnString(string uri, string accessToken = null, string method = "GET", string body = "")
        {
            try
            {
                // Create headers dictionary if accessToken is provided
                Dictionary<string, string> headers = null;
                if (!string.IsNullOrEmpty(accessToken))
                {
                    headers = new Dictionary<string, string>
                    {
                        { "Authorization", $"Bearer {accessToken}" }
                    };
                }
                
                var result = JsonReturnAsync(uri, headers, method, body).GetAwaiter().GetResult();
                if (result == null || result.Count() == 0)
                    return string.Empty;
                
                return result.ToString();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in JsonReturnString for {Uri}", uri);
                return string.Empty;
            }
        }
        
        /// <summary>
        /// Converts an object to JSON string
        /// This is a legacy method maintained for backward compatibility
        /// </summary>
        public string ConvJson(object obj)
        {
            try
            {
                return JsonConvert.SerializeObject(obj);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in ConvJson");
                return "{}";
            }
        }
        
        /// <summary>
        /// Converts an object to JSON string with formatting options
        /// This is a legacy method maintained for backward compatibility
        /// </summary>
        public string ConvJson(object obj, Formatting formatting)
        {
            try
            {
                return JsonConvert.SerializeObject(obj, formatting);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in ConvJson with formatting");
                return "{}";
            }
        }
        
        /// <summary>
        /// Deletes a notification subscription
        /// This is a legacy method maintained for backward compatibility
        /// </summary>
        public bool DeleteNotificationSubscription(string channelId, string subscriptionId)
        {
            try
            {
                string url = $"{ApiEndpoint}/api/v2/notifications/channels/{channelId}/subscriptions/{subscriptionId}";
                var response = DeleteAsync(url).GetAwaiter().GetResult();
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in DeleteNotificationSubscription for channel {ChannelId}, subscription {SubscriptionId}",
                    channelId, subscriptionId);
                return false;
            }
        }
        
        /// <summary>
        /// Deletes a notification subscription with access token
        /// This is a legacy method maintained for backward compatibility
        /// </summary>
        public bool DeleteNotificationSubscription(string channelId, string subscriptionId, string accessToken)
        {
            try
            {
                string url = $"{ApiEndpoint}/api/v2/notifications/channels/{channelId}/subscriptions/{subscriptionId}";
                var response = DeleteAsync(url, accessToken).GetAwaiter().GetResult();
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in DeleteNotificationSubscription for channel {ChannelId}, subscription {SubscriptionId}",
                    channelId, subscriptionId);
                return false;
            }
        }
        
        /// <summary>
        /// Helper method for DELETE requests
        /// </summary>
        private async Task<HttpResponseMessage> DeleteAsync(string url)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Delete, url))
            {
                string token = GetAccessToken();
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                
                return await _httpClient.SendAsync(request);
            }
        }
        
        /// <summary>
        /// Helper method for DELETE requests with a specific access token
        /// </summary>
        private async Task<HttpResponseMessage> DeleteAsync(string url, string accessToken)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Delete, url))
            {
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                
                return await _httpClient.SendAsync(request);
            }
        }

        /// <summary>
        /// Asynchronously makes an API call and returns the JSON response as a string
        /// </summary>
        public async Task<string> JsonRestAsync(string url, Dictionary<string, string> headers = null, string method = "GET", string jsonBody = null)
        {
            try
            {
                // If URL doesn't start with http or https, prepend the API endpoint if available
                if (!url.StartsWith("http://") && !url.StartsWith("https://") && !string.IsNullOrEmpty(ApiEndpoint))
                {
                    // If the URL already starts with a slash, don't add another one
                    if (url.StartsWith("/"))
                        url = ApiEndpoint + url;
                    else
                        url = ApiEndpoint + "/" + url;
                }

                // Ensure we have auth token
                await RefreshTokenIfNeededAsync();

                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    
                    // Track if authorization header is added
                    bool authHeaderAdded = false;
                    
                    // Add any custom headers first
                    if (headers != null)
                    {
                        foreach (var header in headers)
                        {
                            // Skip Authorization header for now, we'll add it properly below
                            if (string.Equals(header.Key, "Authorization", StringComparison.OrdinalIgnoreCase))
                            {
                                authHeaderAdded = true;
                                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                                    header.Value.StartsWith("Bearer ") ? "Bearer" : header.Value.Split(' ')[0],
                                    header.Value.StartsWith("Bearer ") ? header.Value.Substring(7) : header.Value.Split(' ')[1]
                                );
                            }
                            else
                            {
                                httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
                            }
                        }
                    }
                    
                    // Add authorization header if we have a token and it hasn't been added from custom headers
                    if (!string.IsNullOrEmpty(_accessToken) && !authHeaderAdded)
                    {
                        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                    }

                    HttpResponseMessage response;
                    
                    // Execute the appropriate method
                    switch (method.ToUpper())
                    {
                        case "GET":
                            response = await httpClient.GetAsync(url);
                            break;
                        case "POST":
                            var postContent = jsonBody != null ? new StringContent(jsonBody, Encoding.UTF8, "application/json") : null;
                            response = await httpClient.PostAsync(url, postContent);
                            break;
                        case "PUT":
                            var putContent = jsonBody != null ? new StringContent(jsonBody, Encoding.UTF8, "application/json") : null;
                            response = await httpClient.PutAsync(url, putContent);
                            break;
                        case "DELETE":
                            response = await httpClient.DeleteAsync(url);
                            break;
                        default:
                            throw new NotSupportedException($"HTTP method {method} is not supported");
                    }

                    // Get the response content
                    string responseContent = await response.Content.ReadAsStringAsync();
                    
                    // Check if the request was successful
                    if (response.IsSuccessStatusCode)
                    {
                        return responseContent;
                    }
                    else
                    {
                        _logger?.LogError("HTTP {Method} request to {Url} failed with status code {StatusCode}: {Response}", 
                            method, url, response.StatusCode, responseContent);
                        return responseContent; // Return the error response for detailed error handling
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error making API request to {Url}", url);
                return null;
            }
        }
        
        // Helper method to refresh token if needed
        private async Task RefreshTokenIfNeededAsync()
        {
            if (string.IsNullOrEmpty(_accessToken) || _tokenExpiration <= DateTime.UtcNow.AddMinutes(5))
            {
                _logger?.LogDebug("Access token missing or expired, refreshing");
                await RefreshTokenAsync(true);
            }
        }
    }
}
