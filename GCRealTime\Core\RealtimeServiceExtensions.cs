using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace GCRealTime.Core
{
    public static class RealtimeServiceExtensions
    {
        // Extension method to initialize a service - changed to use InitializeAsync
        public static void Initialize(this IRealtimeService service)
        {
            service.InitializeAsync().GetAwaiter().GetResult();
        }

        // Extension method to start a service - changed to use StartAsync
        public static void Start(this IRealtimeService service)
        {
            service.StartAsync(CancellationToken.None).GetAwaiter().GetResult();
        }

        // Extension method to stop a service - changed to use StopAsync
        public static void Stop(this IRealtimeService service)
        {
            service.StopAsync().GetAwaiter().GetResult();
        }

        // Extension method to check service health - changed to use CheckHealthAsync
        public static bool IsHealthy(this IRealtimeService service)
        {
            return service.CheckHealthAsync().GetAwaiter().GetResult();
        }

        // Remove the redundant AddRealtimeServices method since we're using RegisterRealtimeServices instead
    }
}
